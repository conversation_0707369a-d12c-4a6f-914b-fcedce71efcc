package main

import (
	"fmt"
	"time"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

type Stacks struct {
	// +private
	PulumiToolchain *PulumiToolchain
	// +private
	Tallctl *Tallctl
}

// IaC commands.
func (m *UserFrontend) Stacks() *Stacks {
	return &Stacks{
		PulumiToolchain: m.PulumiToolchain(),
		Tallctl:         m.Tallctl(),
	}
}

func (m *Stacks) Up(
	awsDir *dagger.Directory,
	// +optional
	environment string,
	// +optional
	semanticVersion string,
	// +optional
	// +default="consumer-portal"
	app string,
	// +optional
	// +default="consumer-portal"
	projectName string,
	// +optional
	// +defaultPath="/iac/definitions/.dev-consumer-portal.yaml"
	stacksInputFile *dagger.File,
	// +optional
	// +default=false
	detectDrift bool,
) *dagger.Container {
	args := map[string]string{}
	if detectDrift {
		args["--detect-drift"] = ""
	}

	return m.executeStacksCommand(awsDir, environment, semanticVersion, app, projectName, "up", stacksInputFile, args)
}

func (m *Stacks) Refresh(
	awsDir *dagger.Directory,
	// +optional
	environment string,
	// +optional
	semanticVersion string,
	// +optional
	// +default="consumer-portal"
	app string,
	// +optional
	// +default="consumer-portal"
	projectName string,
	// +optional
	// +defaultPath="/iac/definitions/.dev-consumer-portal.yaml"
	stacksInputFile *dagger.File,
) *dagger.Container {
	return m.executeStacksCommand(awsDir, environment, semanticVersion, app, projectName, "refresh", stacksInputFile, map[string]string{})
}

func (m *Stacks) Preview(
	awsDir *dagger.Directory,
	// +optional
	environment string,
	// +optional
	semanticVersion string,
	// +optional
	// +default="consumer-portal"
	app string,
	// +optional
	// +default="consumer-portal"
	projectName string,
	// +optional
	// +defaultPath="/iac/definitions/.dev-consumer-portal.yaml"
	stacksInputFile *dagger.File,
) *dagger.Container {
	return m.executeStacksCommand(awsDir, environment, semanticVersion, app, projectName, "preview", stacksInputFile, map[string]string{})
}

func (m *Stacks) Destroy(
	awsDir *dagger.Directory,
	// +optional
	environment string,
	// +optional
	semanticVersion string,
	// +optional
	// +default="consumer-portal"
	app string,
	// +optional
	// +default="consumer-portal"
	projectName string,
	// +optional
	// +defaultPath="/iac/definitions/.dev-consumer-portal.yaml"
	stacksInputFile *dagger.File,
) *dagger.Container {
	return m.executeStacksCommand(awsDir, environment, semanticVersion, app, projectName, "destroy", stacksInputFile, map[string]string{})
}

func (m *Stacks) Get(
	awsDir *dagger.Directory,
	// +optional
	environment string,
	// +optional
	semanticVersion string,
	// +optional
	// +default="consumer-portal"
	app string,
	// +optional
	// +default="consumer-portal"
	projectName string,
	// +optional
	// +defaultPath="/iac/definitions/.dev-consumer-portal.yaml"
	stacksInputFile *dagger.File,
) *dagger.Container {
	return m.executeStacksCommand(awsDir, environment, semanticVersion, app, projectName, "get", stacksInputFile, map[string]string{})
}

func (m *Stacks) GetStackOutputsFile(
	awsDir *dagger.Directory,
	// +optional
	environment string,
	// +optional
	semanticVersion string,
	// +optional
	// +default="consumer-portal"
	app string,
	// +optional
	// +default="consumer-portal"
	projectName string,
	// +optional
	// +defaultPath="/iac/definitions/.dev-consumer-portal.yaml"
	stacksInputFile *dagger.File,
) *dagger.File {
	return m.Get(awsDir, environment, semanticVersion, app, projectName, stacksInputFile).
		File(fmt.Sprintf(".tallctl/app/%s/stacks-outputs.json", string(app)))
}

func (m *Stacks) executeStacksCommand(
	awsDir *dagger.Directory,
	environmentName,
	semanticVersion string,
	app,
	projectName,
	stacksCommand string,
	stacksInputFile *dagger.File,
	args map[string]string,
) *dagger.Container {
	return m.PulumiToolchain.Env().
		WithMountedFile(".input.yaml", stacksInputFile).
		With(m.Tallctl.AsMountedFile).
		With(AwsAuthorization(awsDir)).
		WithEnvVariable("CACHE_BUSTER", time.Now().String()).
		WithExec(
			constructTallctlExec(constructTallctlExecArgs{
				EnvironmentName: environmentName,
				SemanticVersion: semanticVersion,
				ProjectName:     projectName,
				Command:         app,
				SubCommands:     []string{"stacks", stacksCommand},
				StacksInputFile: ".input.yaml",
				Args:            args,
			}),
		)
}

func AwsAuthorization(awsDir *dagger.Directory) func(c *dagger.Container) *dagger.Container {
	return func(c *dagger.Container) *dagger.Container {
		return c.WithMountedDirectory("/root/.aws", awsDir)
	}
}
