package main

import (
	"fmt"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

type Tallctl struct {
	Binary *dagger.File
}

func (m *UserFrontend) Tallctl() *Tallctl {
	ldflags := "-s -w"

	return &Tallctl{
		Binary: m.GoToolchain().BuildEnv(dag.Directory().WithDirectory("", m.IacSource, dagger.DirectoryWithDirectoryOpts{
			Exclude: []string{"definitions"},
		})).
			WithMountedCache("/work/src/out/bin/.tallctl", dag.CacheVolume("tallctl"), dagger.ContainerWithMountedCacheOpts{
				Sharing: dagger.CacheSharingModeShared,
			}).
			WithExec([]string{"go", "build", fmt.Sprintf("-ldflags=%s", ldflags), "-buildvcs=false", "-trimpath", "-o", "out/bin/tallctl", "./tallctl"}).
			WithWorkdir("/work/src/bin").
			WithExec([]string{"cp", "../out/bin/tallctl", "./tallctl"}).
			File("./tallctl"),
	}
}

func (m *Tallctl) AsMountedFile(container *dagger.Container) *dagger.Container {
	return container.WithMountedFile("/usr/local/bin/tallctl", m.Binary)
}

type constructTallctlExecArgs struct {
	EnvironmentName string
	SemanticVersion string
	StacksInputFile string
	ProjectName     string
	Command         string
	SubCommands     []string
	Args            map[string]string
}

func constructTallctlExec(
	args constructTallctlExecArgs,
) []string {
	exec := []string{
		"tallctl",
		args.Command,
	}

	exec = append(exec, args.SubCommands...)

	if args.EnvironmentName != "" {
		exec = append(exec, "--environment-name", args.EnvironmentName)
	}
	if args.StacksInputFile != "" {
		exec = append(exec, "--stacks-input-file", args.StacksInputFile)
	}
	if args.SemanticVersion != "" {
		exec = append(exec, "--semantic-version", args.SemanticVersion)
	}
	if args.ProjectName != "" {
		exec = append(exec, "--project-name", args.ProjectName)
	}
	for k, v := range args.Args {
		exec = append(exec, k, v)
	}

	return exec
}
