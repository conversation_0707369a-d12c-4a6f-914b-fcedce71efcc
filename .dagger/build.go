package main

import (
	"context"
	"fmt"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

type Build struct {
	FrontendSource  *dagger.Directory
	SemanticVersion string
}

func (m *UserFrontend) Build() *Build {
	return &Build{
		FrontendSource:  m.FrontendSource,
		SemanticVersion: m.SemanticVersion,
	}
}

func (m *Build) Env(source *dagger.Directory) *dagger.Container {
	return dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).From("323007991338.dkr.ecr.us-west-2.amazonaws.com/node:lts-alpine").
		WithMountedCache("/root/.npm", dag.CacheVolume("npm-cache"), dagger.ContainerWithMountedCacheOpts{
			Sharing: dagger.CacheSharingModeShared,
		}).
		WithDirectory("/src", m.FrontendSource, dagger.ContainerWithDirectoryOpts{
			Include: []string{"*package.json", "*package-lock.json", "**/package.json", "**/package-lock.json"},
		}).
		WithWorkdir("/src").
		WithExec([]string{"npm", "ci"}).
		WithDirectory("/src", source)
}

func (m *Build) ConsumerPortal() *ConsumerPortal {
	build := m.Env(dag.Directory().WithDirectory("", m.FrontendSource, dagger.DirectoryWithDirectoryOpts{
		Exclude: []string{
			"apps/application-portal",
			"apps/business-portal",
			"apps/support-portal",
			"apps/testing-playground",
		},
	})).
		WithMountedCache("/src/apps/consumer-portal/.next", dag.CacheVolume("consumer-portal-next-cache"), dagger.ContainerWithMountedCacheOpts{
			Sharing: dagger.CacheSharingModeShared,
		}).
		WithWorkdir("/src/apps/consumer-portal").
		WithEnvVariable("NEXT_TELEMETRY_DISABLED", "1").
		WithExec([]string{"npm", "run", "build"}).
		WithExec([]string{"mkdir", "-p", "/build/standalone"}).
		WithExec([]string{"mkdir", "-p", "/build/static"}).
		WithExec([]string{"cp", "-r", ".next/standalone/.", "/build/standalone/"}).
		WithExec([]string{"cp", "-r", ".next/static/.", "/build/static/"})

	return &ConsumerPortal{
		Built:           build.Directory("/build/standalone").WithDirectory("/build/standalone/node_modules/dd-trace", build.Directory("/src/node_modules/dd-trace")),
		Static:          build.Directory("/build/static"),
		Public:          build.Directory("/src/apps/consumer-portal/public").WithoutFile("mockServiceWorker.js"),
		SemanticVersion: m.SemanticVersion,
	}
}

type ConsumerPortal struct {
	// +Private
	Built *dagger.Directory
	// +Private
	Static *dagger.Directory
	// +Private
	Public          *dagger.Directory
	SemanticVersion string
}

func (m *ConsumerPortal) AsContainer() *dagger.Container {
	return dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).From("323007991338.dkr.ecr.us-west-2.amazonaws.com/node:lts-alpine").
		WithExec([]string{"apk", "add", "curl"}).
		WithExec([]string{"npm", "install", "dd-trace", "-g"}).
		WithDirectory("/app", m.Built).
		WithWorkdir("/app").
		WithDirectory("apps/consumer-portal/.next/cache", dag.Directory(), dagger.ContainerWithDirectoryOpts{
			Owner: "node",
		}).
		WithDirectory("apps/consumer-portal/.next/cache/images", dag.Directory(), dagger.ContainerWithDirectoryOpts{
			Owner: "node",
		}).
		WithDirectory("apps/consumer-portal/.next/static", m.Static).
		WithDirectory("apps/consumer-portal/public", m.Public).
		WithExposedPort(3000).
		WithUser("node").
		WithEnvVariable("NODE_ENV", "production").
		WithEnvVariable("AUTH_TRUST_HOST", "true").
		WithEnvVariable("NEXT_TELEMETRY_DISABLED", "1").
		WithEntrypoint([]string{"node", "--import", "dd-trace", "apps/consumer-portal/server.js"})
}

// This doesn't quite work anymore, it requires a handful of runtime env vars to be set (like we do when we deploy to ECS)
func (m *ConsumerPortal) AsService() *dagger.Service {
	return m.AsContainer().AsService()
}

func (m *ConsumerPortal) Publish(ctx context.Context) (string, error) {
	return m.AsContainer().Publish(ctx, fmt.Sprintf("323007991338.dkr.ecr.us-west-2.amazonaws.com/consumer-portal:%s", m.SemanticVersion))
}

func (m *Build) ApplicationPortal() *ApplicationPortal {
	build := m.Env(dag.Directory().WithDirectory("", m.FrontendSource, dagger.DirectoryWithDirectoryOpts{
		Exclude: []string{
			"apps/consumer-portal",
			"apps/business-portal",
			"apps/support-portal",
			"apps/testing-playground",
		},
	})).
		WithMountedCache("/src/apps/application-portal/.next", dag.CacheVolume("application-portal-next-cache"), dagger.ContainerWithMountedCacheOpts{
			Sharing: dagger.CacheSharingModeShared,
		}).
		WithWorkdir("/src/apps/application-portal").
		WithExec([]string{"npm", "run", "build"}).
		WithExec([]string{"mkdir", "-p", "/build/standalone"}).
		WithExec([]string{"mkdir", "-p", "/build/static"}).
		WithExec([]string{"cp", "-r", ".next/standalone/.", "/build/standalone/"}).
		WithExec([]string{"cp", "-r", ".next/static/.", "/build/static/"})

	return &ApplicationPortal{
		Built:           build.Directory("/build/standalone").WithDirectory("/build/standalone/node_modules/dd-trace", build.Directory("/src/node_modules/dd-trace")),
		Static:          build.Directory("/build/static"),
		Public:          build.Directory("/src/apps/application-portal/public").WithoutFile("mockServiceWorker.js"),
		SemanticVersion: m.SemanticVersion,
	}
}

type ApplicationPortal struct {
	// +Private
	Built *dagger.Directory
	// +Private
	Static *dagger.Directory
	// +Private
	Public          *dagger.Directory
	SemanticVersion string
}

func (m *ApplicationPortal) AsContainer() *dagger.Container {
	return dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).From("323007991338.dkr.ecr.us-west-2.amazonaws.com/node:lts-alpine").
		WithExec([]string{"apk", "add", "curl"}).
		WithExec([]string{"npm", "install", "dd-trace", "-g"}).
		WithDirectory("/app", m.Built).
		WithWorkdir("/app").
		WithDirectory("apps/application-portal/.next/static", m.Static).
		WithDirectory("apps/application-portal/public", m.Public).
		WithExposedPort(3000).
		WithUser("node").
		WithEnvVariable("NODE_ENV", "production").
		WithEnvVariable("AUTH_TRUST_HOST", "true").
		WithEntrypoint([]string{"node", "--import", "dd-trace", "apps/application-portal/server.js"})
}

// This doesn't quite work anymore, it requires a handful of runtime env vars to be set (like we do when we deploy to ECS)
func (m *ApplicationPortal) AsService() *dagger.Service {
	return m.AsContainer().AsService()
}

func (m *ApplicationPortal) Publish(ctx context.Context) (string, error) {
	return m.AsContainer().Publish(ctx, fmt.Sprintf("323007991338.dkr.ecr.us-west-2.amazonaws.com/application-portal:%s", m.SemanticVersion))
}

func (m *Build) SupportPortal() *SupportPortal {
	build := m.Env(dag.Directory().WithDirectory("", m.FrontendSource, dagger.DirectoryWithDirectoryOpts{
		Exclude: []string{
			"apps/consumer-portal",
			"apps/application-portal",
			"apps/business-portal",
			"apps/testing-playground",
		},
	})).
		WithMountedCache("/src/apps/support-portal/.next", dag.CacheVolume("support-portal-next-cache"), dagger.ContainerWithMountedCacheOpts{
			Sharing: dagger.CacheSharingModeShared,
		}).
		WithWorkdir("/src/apps/support-portal").
		WithExec([]string{"npm", "run", "build"}).
		WithExec([]string{"mkdir", "-p", "/build/standalone"}).
		WithExec([]string{"mkdir", "-p", "/build/static"}).
		WithExec([]string{"cp", "-r", ".next/standalone/.", "/build/standalone/"}).
		WithExec([]string{"cp", "-r", ".next/static/.", "/build/static/"})

	return &SupportPortal{
		Built:           build.Directory("/build/standalone").WithDirectory("/build/standalone/node_modules/dd-trace", build.Directory("/src/node_modules/dd-trace")),
		Static:          build.Directory("/build/static"),
		Public:          build.Directory("/src/apps/support-portal/public").WithoutFile("mockServiceWorker.js"),
		SemanticVersion: m.SemanticVersion,
	}
}

type SupportPortal struct {
	// +Private
	Built *dagger.Directory
	// +Private
	Static *dagger.Directory
	// +Private
	Public          *dagger.Directory
	SemanticVersion string
}

func (m *SupportPortal) AsContainer() *dagger.Container {
	return dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).From("323007991338.dkr.ecr.us-west-2.amazonaws.com/node:lts-alpine").
		WithExec([]string{"apk", "add", "curl"}).
		WithExec([]string{"npm", "install", "dd-trace", "-g"}).
		WithDirectory("/app", m.Built).
		WithWorkdir("/app").
		WithDirectory("apps/support-portal/.next/static", m.Static).
		WithDirectory("apps/support-portal/public", m.Public).
		WithExposedPort(3000).
		WithUser("node").
		WithEnvVariable("NODE_ENV", "production").
		WithEnvVariable("AUTH_TRUST_HOST", "true").
		WithEntrypoint([]string{"node", "--import", "dd-trace", "apps/support-portal/server.js"})
}

// This doesn't quite work anymore, it requires a handful of runtime env vars to be set (like we do when we deploy to ECS)
func (m *SupportPortal) AsService() *dagger.Service {
	return m.AsContainer().AsService()
}

func (m *SupportPortal) Publish(ctx context.Context) (string, error) {
	return m.AsContainer().Publish(ctx, fmt.Sprintf("323007991338.dkr.ecr.us-west-2.amazonaws.com/support-portal:%s", m.SemanticVersion))
}
