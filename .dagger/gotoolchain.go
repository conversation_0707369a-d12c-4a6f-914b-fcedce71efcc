package main

import (
	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

type GoToolchain struct {
	Version string
	// +private
	GithubToken *dagger.Secret
}

func (m *UserFrontend) GoToolchain() *GoToolchain {
	return &GoToolchain{
		Version:     m.GoVersion,
		GithubToken: m.GithubToken,
	}
}

func (g *GoToolchain) Base() *dagger.Container {
	base := dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).
		From("323007991338.dkr.ecr.us-west-2.amazonaws.com/golang:"+g.Version+"-alpine").
		WithExec([]string{"apk", "add", "--no-cache", "git", "curl", "bash", "openssh"}).
		WithEnvVariable("GOOS", "linux").
		WithEnvVariable("GOARCH", "arm64").
		WithEnvVariable("CGO_ENABLED", "0").
		WithEnvVariable("GO111MODULE", "on").
		WithEnvVariable("GOPRIVATE", "github.com/Tallied-Technologies").
		WithEnvVariable("GOMODCACHE", "/go/pkg/mod").
		WithEnvVariable("GOCACHE", "/go/build-cache")

	return base
}

func (g *GoToolchain) BuildEnv(goSource *dagger.Directory) *dagger.Container {
	return g.Base().
		With(WithGhAuth(g.GithubToken)).
		With(WithGoMod(goSource)).
		WithMountedCache("/go/build-cache", dag.CacheVolume("go-build-cache"), dagger.ContainerWithMountedCacheOpts{
			Sharing: dagger.CacheSharingModeShared,
		}).
		WithMountedDirectory("/work/src", goSource)
}

func WithGhAuth(githubToken *dagger.Secret) func(c *dagger.Container) *dagger.Container {
	return func(c *dagger.Container) *dagger.Container {
		return c.
			WithSecretVariable("GITHUB_TOKEN", githubToken).
			WithExec([]string{"sh", "-c", "git config --global --add url.https://$<EMAIL>/.insteadOf https://github.com/"})
	}
}

func WithGoMod(goSource *dagger.Directory) func(c *dagger.Container) *dagger.Container {
	return func(c *dagger.Container) *dagger.Container {
		return c.
			WithMountedCache("/go/pkg/mod", dag.CacheVolume("go-mod-cache"), dagger.ContainerWithMountedCacheOpts{
				Sharing: dagger.CacheSharingModeShared,
			}).
			WithWorkdir("/work/src").
			// run `go mod download` with only go.mod files (re-run only if mod files have changed)
			WithDirectory("/work/src", goSource, dagger.ContainerWithDirectoryOpts{
				Include: []string{"go.mod", "go.sum"},
			}).
			WithExec([]string{"go", "mod", "download"})

	}
}
