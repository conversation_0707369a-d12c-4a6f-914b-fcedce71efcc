package main

import (
	"fmt"
	"time"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

type GithubToolchain struct {
	// GH CLI version
	Version string
}

func (m *UserFrontend) GithubToolchain() *GithubToolchain {
	return &GithubToolchain{
		Version: m.GithubVersion,
	}
}

func (g *GithubToolchain) Base() *dagger.Container {
	base := dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).
		From("323007991338.dkr.ecr.us-west-2.amazonaws.com/alpine").
		WithExec([]string{"apk", "add", "--no-cache", "wget", "tar", "git"}).
		WithExec([]string{"wget", "-q", fmt.Sprintf("https://github.com/cli/cli/releases/download/v%s/gh_%s_linux_arm64.tar.gz", g.<PERSON>, g.Version)}).
		WithExec([]string{"tar", "-xzf", fmt.Sprintf("gh_%s_linux_arm64.tar.gz", g.Version)}).
		WithExec([]string{"mv", fmt.Sprintf("gh_%s_linux_arm64/bin/gh", g.Version), "/usr/local/bin/gh"}).
		WithExec([]string{"rm", fmt.Sprintf("gh_%s_linux_arm64.tar.gz", g.Version)}).
		WithExec([]string{"rm", "-rf", fmt.Sprintf("gh_%s_linux_arm64", g.Version)})

	return base
}

func (g *GithubToolchain) Env(gitSource *dagger.Directory) *dagger.Container {
	return g.Base().
		WithEnvVariable("CURRENT_TIME", time.Now().String()). // Ensure dagger cache is busted to enable re-runs without code changes.
		WithWorkdir("/work").
		WithMountedDirectory("/work/.git", gitSource.Directory(".git"))
}

func (g *GithubToolchain) ReleaseEnv(gitSource *dagger.Directory, github_token *dagger.Secret) *dagger.Container {
	return g.Env(gitSource).
		WithSecretVariable("GH_TOKEN", github_token)
}
