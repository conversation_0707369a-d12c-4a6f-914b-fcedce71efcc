package main

import (
	"fmt"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

func (m *UserFrontend) Release() *Release {
	return &Release{
		FrontendSource:  m.GitSource,
		GithubToolchain: m.GithubToolchain(),
	}
}

type Release struct {
	// +private
	FrontendSource *dagger.Directory
	// +private
	GithubToolchain *GithubToolchain
}

func (m *Release) Create(
	githubToken *dagger.Secret,
	targetSha string,
	nextSemver string,
	assets []*dagger.File,
) *dagger.Container {
	releaseEnv := m.GithubToolchain.ReleaseEnv(m.FrontendSource, githubToken)

	exec := []string{
		"sh", "-c",
		fmt.Sprintf("gh release create %s --generate-notes --target %s assets/*", nextSemver, targetSha),
	}

	return releaseEnv.
		WithFiles("assets", assets).
		WithExec(exec, dagger.ContainerWithExecOpts{
			Expand: true,
		})
}
