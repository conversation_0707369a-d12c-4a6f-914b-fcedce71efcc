package main

import (
	"context"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
	"github.com/cockroachdb/errors"
)

type UserFrontend struct {
	GoVersion       string
	GithubVersion   string
	PulumiVersion   string
	SemanticVersion string

	GitSource      *dagger.Directory
	FrontendSource *dagger.Directory
	IacSource      *dagger.Directory

	// +private
	GithubToken *dagger.Secret
}

func New(
	ctx context.Context,
	// +optional
	semanticVersion string,
	// +optional
	// +defaultPath="/"
	// +ignore=["*", "!.git"]
	gitSource *dagger.Directory,
	// +optional
	// +defaultPath="/"
	// +ignore=["*", "!apps", "!packages", "!common", "!package.json", "!package-lock.json",  "**/node_modules", ".next", "**/.next", ".git"]
	frontendSource *dagger.Directory,
	// +optional
	// +defaultPath="/iac"
	// +ignore=["tools"]
	iacSource *dagger.Directory,
	// Secret containing Github PAT for cloning & downloading binaries from private GH repos.
	githubToken *dagger.Secret,
) (*UserFrontend, error) {
	ghTokenPlain, err := githubToken.Plaintext(ctx)
	if err != nil {
		return nil, err
	}
	if len(ghTokenPlain) == 0 {
		return nil, errors.New("--github-token was provided but is empty")
	}

	return &UserFrontend{
		GoVersion:       "1.24.2",
		GithubVersion:   "2.57.0",
		PulumiVersion:   "3.167.0",
		SemanticVersion: semanticVersion,
		GitSource:       gitSource,
		FrontendSource:  frontendSource,
		IacSource:       iacSource,
		GithubToken:     githubToken,
	}, nil
}

func (m *UserFrontend) ReleaseWorkflow(
	ctx context.Context,
	awsDir *dagger.Directory,
	githubToken *dagger.Secret,
	nextSemver,
	targetSha,
	currentBranch string,
) (string, error) {
	detectDrift := true
	artifactsAppName := "frontend-artifacts"
	artifactsEnvironment := "prod"
	artifactsProjectName := "frontend-artifacts"
	artifactsStacksInputFile := m.IacSource.File("./definitions/.frontend-artifacts.yaml")

	baseStacksPipeline := NewConcPipeline[*dagger.Container]("").
		AddSyncable("Frontend Artifacts Stacks Up", m.Stacks().Up(awsDir, artifactsEnvironment, nextSemver, artifactsAppName, artifactsProjectName, artifactsStacksInputFile, detectDrift))

	imagesPipeline := NewConcPipeline[string]("").
		AddPublishable("Build & Publish Consumer Portal Image", m.Build().ConsumerPortal()).
		AddPublishable("Build & Publish Application Portal Image", m.Build().ApplicationPortal()).
		AddPublishable("Build & Publish Support Portal Image", m.Build().SupportPortal())

	devEnvironment := "dev"

	networkAppName := "frontend-network"
	networkProjectName := "frontend-network"
	devNetworkStacksInputFile := m.IacSource.File("./definitions/.dev-frontend-network.yaml")

	consumerPortalAppName := "consumer-portal"
	consumerPortalProjectName := "consumer-portal"
	devConsumerPortalStacksInputFile := m.IacSource.File("./definitions/.dev-consumer-portal.yaml")

	applicationPortalAppName := "application-portal"
	applicationPortalProjectName := "application-portal"
	devApplicationPortalStacksInputFile := m.IacSource.File("./definitions/.dev-application-portal.yaml")

	supportPortalAppName := "support-portal"
	supportPortalProjectName := "support-portal"
	devSupportPortalStacksInputFile := m.IacSource.File("./definitions/.dev-support-portal.yaml")

	preProdEnvironment := "preprod"

	preProdNetworkStacksInputFile := m.IacSource.File("./definitions/.preprod-frontend-network.yaml")
	preProdConsumerPortalStacksInputFile := m.IacSource.File("./definitions/.preprod-consumer-portal.yaml")
	preProdApplicationPortalStacksInputFile := m.IacSource.File("./definitions/.preprod-application-portal.yaml")
	preProdSupportPortalStacksInputFile := m.IacSource.File("./definitions/.preprod-support-portal.yaml")

	pipeline := NewSeqPipeline[*dagger.Container]().
		AddConcPipeline(NewConcPipeline[*dagger.Container]("Deploy Base Stacks, Build & Publish Images").
			AddSyncable("Deploy Frontend Base Stacks", baseStacksPipeline).
			AddSyncable("Build & Publish Frontend Images", imagesPipeline).
			AddSyncable("Dev Frontend Network Stacks Up", m.Stacks().Up(awsDir, devEnvironment, nextSemver, networkAppName, networkProjectName, devNetworkStacksInputFile, detectDrift)).
			AddSyncable("PreProd Frontend Network Stacks Up", m.Stacks().Up(awsDir, preProdEnvironment, nextSemver, networkAppName, networkProjectName, preProdNetworkStacksInputFile, detectDrift)),
		).
		AddConcPipeline(NewConcPipeline[*dagger.Container]("Frontend App Stacks Up").
			AddSyncable("Dev Consumer Portal Stacks Up", m.Stacks().Up(awsDir, devEnvironment, nextSemver, consumerPortalAppName, consumerPortalProjectName, devConsumerPortalStacksInputFile, detectDrift)).
			AddSyncable("Dev Application Portal Stacks Up", m.Stacks().Up(awsDir, devEnvironment, nextSemver, applicationPortalAppName, applicationPortalProjectName, devApplicationPortalStacksInputFile, detectDrift)).
			AddSyncable("Dev Support Portal Stacks Up", m.Stacks().Up(awsDir, devEnvironment, nextSemver, supportPortalAppName, supportPortalProjectName, devSupportPortalStacksInputFile, detectDrift)).
			AddSyncable("PreProd Consumer Portal Stacks Up", m.Stacks().Up(awsDir, preProdEnvironment, nextSemver, consumerPortalAppName, consumerPortalProjectName, preProdConsumerPortalStacksInputFile, detectDrift)).
			AddSyncable("PreProd Application Portal Stacks Up", m.Stacks().Up(awsDir, preProdEnvironment, nextSemver, applicationPortalAppName, applicationPortalProjectName, preProdApplicationPortalStacksInputFile, detectDrift)).
			AddSyncable("PreProd Support Portal Stacks Up", m.Stacks().Up(awsDir, preProdEnvironment, nextSemver, supportPortalAppName, supportPortalProjectName, preProdSupportPortalStacksInputFile, detectDrift)),
		)

	if currentBranch == "main" {
		// Prepare Release Assets
		tallctlLinux := m.Tallctl().Binary

		// Create a reference to the Create Release job.
		createRelease := m.Release().Create(githubToken, targetSha, nextSemver, []*dagger.File{
			tallctlLinux.WithName("tallctl"),
			// attach other assets here...
		})

		// Run the meta pipeline.
		_, err := pipeline.Run(ctx)
		if err != nil {
			return "", err
		}

		// Run the Create Release job.
		createReleaseStdout, err := TraceStdoutable(ctx, "Create Github Release", createRelease)
		if err != nil {
			return "", err
		}

		return createReleaseStdout, nil
	}

	_, err := pipeline.Run(ctx)
	if err != nil {
		return "", err
	}

	return "Release not created, branch is not main", nil
}

func (m *UserFrontend) PrValidationWorkflow(
	ctx context.Context,
	awsDir *dagger.Directory,
) (string, error) {
	artifactsAppName := "frontend-artifacts"
	artifactsEnvironment := "prod"
	artifactsProjectName := "frontend-artifacts"
	artifactsStacksInputFile := m.IacSource.File("./definitions/.frontend-artifacts.yaml")

	devEnvironment := "dev"

	networkAppName := "frontend-network"
	networkProjectName := "frontend-network"
	devNetworkStacksInputFile := m.IacSource.File("./definitions/.dev-frontend-network.yaml")

	consumerPortalAppName := "consumer-portal"
	consumerPortalProjectName := "consumer-portal"
	devConsumerPortalStacksInputFile := m.IacSource.File("./definitions/.dev-consumer-portal.yaml")

	applicationPortalAppName := "application-portal"
	applicationPortalProjectName := "application-portal"
	devApplicationPortalStacksInputFile := m.IacSource.File("./definitions/.dev-application-portal.yaml")

	supportPortalAppName := "support-portal"
	supportPortalProjectName := "support-portal"
	devSupportPortalStacksInputFile := m.IacSource.File("./definitions/.dev-support-portal.yaml")

	preProdEnvironment := "preprod"

	preProdNetworkStacksInputFile := m.IacSource.File("./definitions/.preprod-frontend-network.yaml")
	preProdConsumerPortalStacksInputFile := m.IacSource.File("./definitions/.preprod-consumer-portal.yaml")
	preProdApplicationPortalStacksInputFile := m.IacSource.File("./definitions/.preprod-application-portal.yaml")
	preProdSupportPortalStacksInputFile := m.IacSource.File("./definitions/.preprod-support-portal.yaml")

	_, err := NewConcPipeline[*dagger.Container]("").
		AddSyncable("Build Consumer Portal Image", m.Build().ConsumerPortal().AsContainer()).
		AddSyncable("Build Application Portal Image", m.Build().ApplicationPortal().AsContainer()).
		AddSyncable("Build Support Portal Image", m.Build().SupportPortal().AsContainer()).
		AddSyncable("Frontend Artifacts Stacks Preview", m.Stacks().Preview(awsDir, artifactsEnvironment, m.SemanticVersion, artifactsAppName, artifactsProjectName, artifactsStacksInputFile)).
		AddSyncable("Dev Frontend Network Stack Preview", m.Stacks().Preview(awsDir, devEnvironment, m.SemanticVersion, networkAppName, networkProjectName, devNetworkStacksInputFile)).
		AddSyncable("Dev Consumer Portal Stack Preview", m.Stacks().Preview(awsDir, devEnvironment, m.SemanticVersion, consumerPortalAppName, consumerPortalProjectName, devConsumerPortalStacksInputFile)).
		AddSyncable("Dev Application Portal Stack Preview", m.Stacks().Preview(awsDir, devEnvironment, m.SemanticVersion, applicationPortalAppName, applicationPortalProjectName, devApplicationPortalStacksInputFile)).
		AddSyncable("Dev Support Portal Stack Preview", m.Stacks().Preview(awsDir, devEnvironment, m.SemanticVersion, supportPortalAppName, supportPortalProjectName, devSupportPortalStacksInputFile)).
		AddSyncable("PreProd Frontend Network Stack Preview", m.Stacks().Preview(awsDir, preProdEnvironment, m.SemanticVersion, networkAppName, networkProjectName, preProdNetworkStacksInputFile)).
		AddSyncable("PreProd Consumer Portal Stack Preview", m.Stacks().Preview(awsDir, preProdEnvironment, m.SemanticVersion, consumerPortalAppName, consumerPortalProjectName, preProdConsumerPortalStacksInputFile)).
		AddSyncable("PreProd Application Portal Stack Preview", m.Stacks().Preview(awsDir, preProdEnvironment, m.SemanticVersion, applicationPortalAppName, applicationPortalProjectName, preProdApplicationPortalStacksInputFile)).
		AddSyncable("PreProd Support Portal Stack Preview", m.Stacks().Preview(awsDir, preProdEnvironment, m.SemanticVersion, supportPortalAppName, supportPortalProjectName, preProdSupportPortalStacksInputFile)).
		Run(ctx)
	if err != nil {
		return "", errors.Wrap(err, "failed to run PR Validation Workflow")
	}

	return "PR Validation Workflow completed successfully", nil
}
