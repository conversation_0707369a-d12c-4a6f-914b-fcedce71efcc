package main

import (
	"context"
	"fmt"
	"sync"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/telemetry"
	"github.com/sourcegraph/conc/pool"

	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

type syncable[T any] interface {
	Sync(ctx context.Context) (T, error)
}

type publishable[T any] interface {
	Publish(ctx context.Context) (T, error)
}

type stdoutable[T any] interface {
	Stdout(ctx context.Context) (T, error)
}

// Job represents a unit of work.
type Job[T any] struct {
	name string
	run  func(ctx context.Context) (T, error)
}

func TraceSyncable[T any](ctx context.Context, name string, s syncable[T]) (T, error) {
	job := Job[T]{name: name, run: s.Sync}
	ctx, span := Tracer().Start(ctx, job.name, telemetry.Encapsulate())
	defer span.End()
	return job.run(ctx)
}

func TraceStdoutable[T any](ctx context.Context, name string, s stdoutable[T]) (T, error) {
	job := Job[T]{name: name, run: s.Stdout}
	ctx, span := Tracer().Start(ctx, job.name, telemetry.Encapsulate())
	defer span.End()
	return job.run(ctx)
}

type ConcPipeline[T any] struct {
	name string
	jobs []Job[T]
}

// NewPipeline creates a new pipeline. Pipelines run jobs concurrently.
func NewConcPipeline[T any](name string) *ConcPipeline[T] {
	return &ConcPipeline[T]{name: name}
}

// AddJob adds a pre-built Job.
func (p *ConcPipeline[T]) addJob(job Job[T]) *ConcPipeline[T] {
	p.jobs = append(p.jobs, job)
	return p
}

// addFunc is a convenience method that creates a Job from a name and function.
// The job is stored “raw” and will be wrapped with tracing when run.
func (p *ConcPipeline[T]) addFunc(name string, fn func(ctx context.Context) (T, error)) *ConcPipeline[T] {
	return p.addJob(Job[T]{name: name, run: fn})
}

func (p *ConcPipeline[T]) AddSyncable(name string, s syncable[T]) *ConcPipeline[T] {
	return p.addFunc(name, s.Sync)
}

func (p *ConcPipeline[T]) AddStdoutable(name string, s stdoutable[T]) *ConcPipeline[T] {
	return p.addFunc(name, s.Stdout)
}

func (p *ConcPipeline[T]) AddPublishable(name string, s publishable[T]) *ConcPipeline[T] {
	return p.addFunc(name, s.Publish)
}

// Run executes all jobs concurrently. Each job is automatically wrapped in a tracing span.
func (p *ConcPipeline[T]) Run(ctx context.Context) ([]T, error) {
	var span trace.Span
	if p.name != "" {
		// Create a span for the pipeline.
		ctx, span = Tracer().Start(ctx, p.name, telemetry.Encapsulate())
		defer span.End()
	}

	var (
		results []T
		mu      sync.Mutex
	)

	// Use a worker pool to run jobs concurrently.
	pool := pool.New().WithErrors().WithContext(ctx)
	for _, job := range p.jobs {
		job := job // capture range variable
		pool.Go(func(ctx context.Context) error {
			// Automatically start a child span for this job.
			ctx, jobSpan := Tracer().Start(ctx, job.name, telemetry.Encapsulate())
			defer jobSpan.End()

			res, err := job.run(ctx)
			if err != nil {
				jobSpan.SetStatus(codes.Error, fmt.Sprintf("error in %s: %v", job.name, err))
				return err
			}
			mu.Lock()
			results = append(results, res)
			mu.Unlock()
			return nil
		})
	}

	if err := pool.Wait(); err != nil {
		if span != nil {
			span.SetStatus(codes.Error, fmt.Sprintf("error in pipeline '%s': %v", p.name, err))
		}
		return nil, err
	}
	return results, nil
}

// Sync executes pipeline without collecting results.
func (p *ConcPipeline[T]) Sync(ctx context.Context) (*dagger.Container, error) {
	_, err := p.Run(ctx)
	if err != nil {
		return nil, err
	}

	return dag.Container(), nil
}

// Jobs returns the list of jobs in the pipeline.
func (p *ConcPipeline[T]) Jobs() []Job[T] {
	return p.jobs
}

// CombinePipelines creates a new pipeline that runs all jobs
// from the given pipelines concurrently.
func CombineConcPipelines[T any](pipelines ...*ConcPipeline[T]) *ConcPipeline[T] {
	combined := NewConcPipeline[T]("")
	for _, p := range pipelines {
		for _, job := range p.Jobs() {
			combined.addJob(job)
		}
	}
	return combined
}

type SeqPipeline[T any] struct {
	concPipelines []*ConcPipeline[T]
}

// NewSeqPipeline creates a new SeqPipeline. Pipelines run sequentially.
func NewSeqPipeline[T any]() *SeqPipeline[T] {
	return &SeqPipeline[T]{}
}

func (pg *SeqPipeline[T]) AddConcPipeline(p *ConcPipeline[T]) *SeqPipeline[T] {
	pg.concPipelines = append(pg.concPipelines, p)
	return pg
}

func (pg *SeqPipeline[T]) AddSeqPipeline(seqPipeline *SeqPipeline[T]) *SeqPipeline[T] {
	pg.concPipelines = append(pg.concPipelines, seqPipeline.concPipelines...)
	return pg
}

func (pg *SeqPipeline[T]) AddSyncable(name string, s syncable[T]) *SeqPipeline[T] {
	pipeline := NewConcPipeline[T]("")
	pipeline.addFunc(name, s.Sync)
	pg.AddConcPipeline(pipeline)
	return pg
}

func (pg *SeqPipeline[T]) AddStdoutable(name string, s stdoutable[T]) *SeqPipeline[T] {
	pipeline := NewConcPipeline[T]("")
	pipeline.addFunc(name, s.Stdout)
	pg.AddConcPipeline(pipeline)
	return pg
}

func (pg *SeqPipeline[T]) AddPublishable(name string, s publishable[T]) *SeqPipeline[T] {
	pipeline := NewConcPipeline[T]("")
	pipeline.addFunc(name, s.Publish)
	pg.AddConcPipeline(pipeline)
	return pg
}

// Run executes pipelines sequentially.
func (pg *SeqPipeline[T]) Run(ctx context.Context) ([][]T, error) {
	var allResults [][]T
	for _, p := range pg.concPipelines {
		results, err := p.Run(ctx)
		if err != nil {
			return nil, err
		}
		allResults = append(allResults, results)
	}
	return allResults, nil
}

// Sync executes pipelines sequentially without collecting results.
func (pg *SeqPipeline[T]) Sync(ctx context.Context) (*dagger.Container, error) {
	for _, p := range pg.concPipelines {
		_, err := p.Run(ctx)
		if err != nil {
			return nil, err
		}
	}
	return dag.Container(), nil
}
