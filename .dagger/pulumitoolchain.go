package main

import (
	"fmt"

	"github.com/Tallied-Technologies/user-frontend/.dagger/internal/dagger"
)

type PulumiToolchain struct {
	// Pulumi version
	Version string
}

func (m *UserFrontend) PulumiToolchain() *PulumiToolchain {
	return &PulumiToolchain{
		Version: m.PulumiVersion,
	}
}

func (p *PulumiToolchain) Base() *dagger.Container {
	base := dag.Container(dagger.ContainerOpts{
		Platform: "linux/arm64",
	}).
		From("323007991338.dkr.ecr.us-west-2.amazonaws.com/alpine").
		WithExec([]string{"apk", "add", "--no-cache", "curl", "libc6-compat"}).
		WithExec([]string{"sh", "-c", fmt.Sprintf("curl -fsSL https://get.pulumi.com/ | sh -s -- --version %s", p.Version)}).
		WithExec([]string{"ln", "-s", "/root/.pulumi/bin/pulumi", "/usr/local/bin/pulumi"}).
		WithEnvVariable("PULUMI_HOME", "/root/.pulumi").
		WithMountedCache("/root/.pulumi/plugins", dag.CacheVolume("pulumi-plugin-cache"), dagger.ContainerWithMountedCacheOpts{
			Sharing: dagger.CacheSharingModeShared,
		})

	return base
}

func (g *PulumiToolchain) Env() *dagger.Container {
	return g.Base()
}
