export async function register() {
    if (process.env.NEXT_RUNTIME === 'nodejs' && process.env.DD_ENV && process.env.DD_SERVICE) {
        console.log('Registering Datadog instrumentation')

        try {
            const ddTrace = await import('dd-trace')
            const tags = await import('dd-trace/ext/tags')
            const http = await import('node:http')

            const tracer = ddTrace.default.init({
                env: process.env.DD_ENV,
                service: process.env.DD_SERVICE,
                version: process.env.DD_VERSION,
                sampleRate: 1,
                profiling: true,
                runtimeMetrics: true,
                logInjection: true,
                dogstatsd: {
                    hostname: 'localhost',
                    port: 8125,
                },
            })

            // Monitor Next.js
            tracer
                .use('next', {
                    enabled: true,
                    measured: true,
                })
                .use('fetch', {
                    enabled: true,
                    measured: true,
                })
                .use('http', {
                    enabled: true,
                    measured: true,
                    hooks: {
                        request: (span, req) => {
                            if (span && req) {
                                if (req instanceof http.IncomingMessage) {
                                    const userAgent = req.headers['user-agent']

                                    if (userAgent === 'ELB-HealthChecker/2.0' || userAgent?.startsWith('curl/')) {
                                        // Drop the trace
                                        span.setTag(tags.MANUAL_DROP, true)
                                    }
                                }
                            }
                        },
                    },
                })

            const provider = new tracer.TracerProvider()

            provider.register()
        } catch (e) {
            console.error('Failed to register Datadog instrumentation', e)
        }
    }
}
