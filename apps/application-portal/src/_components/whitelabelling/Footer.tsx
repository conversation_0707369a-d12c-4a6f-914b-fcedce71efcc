import {Box} from '@mui/material'
import Image from 'next/image'
import {useProgramContext} from '@/_components/providers/ProgramContextProvider'

function Logo() {
    const {whitelabelConfig} = useProgramContext()

    return (
        <Image
            src={`/assets/whitelabeling/${whitelabelConfig.logoFileName}`}
            alt="Brand Logo"
            width={whitelabelConfig.logoWidth}
            height={whitelabelConfig.logoHeight}
            priority
        />
    )
}

export default function Footer() {
    return (
        <Box
            component="footer"
            sx={{
                display: 'flex',
                justifyContent: 'space-between',
                backgroundColor: 'var(--mui-palette-brand-main)',
                padding: 3,
                color: 'white',
                mt: 'auto',
                width: '100%',
            }}
        >
            <Logo />
        </Box>
    )
}
