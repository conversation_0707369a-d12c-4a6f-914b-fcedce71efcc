import Image from 'next/image'
import {Box} from '@mui/material'
import {useProgramContext} from '@/_components/providers/ProgramContextProvider'

function LoadingAsset() {
    const {whitelabelConfig} = useProgramContext()
    return (
        <Image
            src={`/assets/whitelabeling/${whitelabelConfig.loaderFileName}`}
            alt="Loading"
            width={264}
            height={264}
            priority
        />
    )
}

export default function Loader() {
    return (
        <Box
            sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            }}
        >
            <LoadingAsset />
        </Box>
    )
}
