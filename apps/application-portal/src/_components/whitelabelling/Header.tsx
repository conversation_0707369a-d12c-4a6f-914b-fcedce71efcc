import {AppBar, Toolbar} from '@mui/material'
import Image from 'next/image'
import {useProgramContext} from '@/_components/providers/ProgramContextProvider'

function Logo() {
    const {whitelabelConfig} = useProgramContext()
    return (
        <Image
            src={`/assets/whitelabeling/${whitelabelConfig.logoFileName}`}
            alt="Brand Logo"
            width={whitelabelConfig.logoWidth}
            height={whitelabelConfig.logoHeight}
            priority
        />
    )
}

export default function Header() {
    return (
        <>
            <AppBar position="static" sx={{backgroundColor: 'var(--mui-palette-brand-main)'}}>
                <Toolbar sx={{justifyContent: 'space-between'}}>
                    <Logo />
                    {/* TODO NOW - disabled until we have a help page */}
                    {/* <IconButton edge="end" color="inherit" aria-label="help">
                        <QuestionMarkIcon color='primary' />
                    </IconButton> */}
                </Toolbar>
            </AppBar>
        </>
    )
}
