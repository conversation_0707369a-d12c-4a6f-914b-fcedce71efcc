import {ApplicationFlowDefinition} from '@/flow-definitions/iface'
import {useApplicationStore} from '@/store/applicationStore'
import {Box, Stack, Typography} from '@mui/material'
import React from 'react'
import CheckIcon from '@mui/icons-material/Check'

function generateStepList(flow: ApplicationFlowDefinition): Array<number> {
    let steps = []
    for (const step of flow.steps) {
        if (step.substeps) {
            steps.push(step.substeps.length)
        } else {
            steps.push(0)
        }
    }
    return steps
}

export default function GranularStepper(props: {flow: ApplicationFlowDefinition}) {
    const stepList = generateStepList(props.flow)
    const currentStep = useApplicationStore(state => state.data.currentStep)
    const currentSubstep = useApplicationStore(state => state.data.currentSubstep)

    const renderStepLabel = (step: number) => {
        let stepLabel: number | JSX.Element = step + 1
        const isStepCompleted = step < currentStep
        if (isStepCompleted) {
            stepLabel = <CheckIcon fontSize="small" />
        }

        const onStep = step === currentStep
        const pastStep = step < currentStep

        const getBackgroundColor = () => {
            if (onStep) {
                return 'var(--mui-palette-secondary-main)'
            } else if (pastStep) {
                return 'var(--mui-palette-secondary-light)'
            }
            return 'var(--mui-palette-action-disabledBackground)'
        }

        const getTextColor = () => {
            if (onStep) {
                return 'var(--mui-palette-primary-contrastText)'
            } else if (pastStep) {
                return 'var(--mui-palette-secondary-dark)'
            }
            return 'var(--mui-palette-text-secondary)'
        }

        return (
            <Box
                sx={{
                    display: 'flex',
                    width: '32px',
                    height: '32px',
                    padding: '0px 7px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '100%',
                    background: getBackgroundColor(),
                }}
            >
                <Typography
                    sx={{
                        display: 'flex',
                        color: getTextColor(),
                        textAlign: 'right',
                        fontFamily: 'Open Sans',
                        fontSize: '16px',
                        fontStyle: 'normal',
                        fontWeight: 700,
                        lineHeight: '166%' /* 26.56px */,
                        letterSpacing: '0.4px',
                    }}
                >
                    {stepLabel}
                </Typography>
            </Box>
        )
    }

    const renderStepConnector = (step: number, subSteps: number) => {
        if (step >= stepList.length - 1) {
            return null
        }

        const onStep = step === currentStep
        const pastStep = step < currentStep

        const getSubstepColor = (substep: number) => {
            if (onStep && substep === currentSubstep) {
                return 'var(--mui-palette-secondary-main)'
            } else if (currentSubstep > substep || pastStep) {
                return 'var(--mui-palette-secondary-dark)'
            }
            return 'var(--mui-palette-action-active)'
        }

        const getStepColor = () => {
            if (onStep) {
                return 'var(--mui-palette-secondary-main)'
            } else if (pastStep) {
                return 'var(--mui-palette-secondary-dark)'
            }
            return 'var(--mui-palette-action-active)'
        }

        return (
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    flexGrow: 1, // fills remaining horizontal space
                    mx: 1,
                }}
            >
                {subSteps > 1 ? (
                    <Stack direction="row" spacing={0.5} width="100%" alignItems={'center'}>
                        {Array.from({length: subSteps}).map((_, subStep) => {
                            const onSubstep = onStep && currentSubstep === subStep
                            return (
                                <Box
                                    key={subStep}
                                    sx={{
                                        flex: 1,
                                        height: onSubstep ? 4 : 2,
                                        backgroundColor: getSubstepColor(subStep),
                                        borderRadius: '999px',
                                    }}
                                />
                            )
                        })}
                    </Stack>
                ) : (
                    <Box
                        alignItems={'center'}
                        sx={{
                            width: '100%',
                            height: onStep ? 4 : 2,
                            backgroundColor: getStepColor(),
                            borderRadius: '999px',
                        }}
                    />
                )}
            </Box>
        )
    }

    return (
        <Stack direction="row" alignItems="center" width="100%">
            {stepList.map((subSteps, index) => (
                <React.Fragment key={index}>
                    {renderStepLabel(index)}
                    {renderStepConnector(index, subSteps)}
                </React.Fragment>
            ))}
        </Stack>
    )
}
