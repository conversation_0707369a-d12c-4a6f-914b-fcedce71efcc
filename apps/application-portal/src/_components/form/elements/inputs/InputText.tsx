import {InputElement} from '@/flow-definitions/iface'
import {TextField} from '@mui/material'
import {useInputElementHelpers} from './inputElementHooks'
import {useState} from 'react'

export default function InputText(props: {row: InputElement<string>}) {
    const {resolveContent, getValue, getError, onChange} = useInputElementHelpers(props.row)
    const [interactedWith, setInteractedWith] = useState(false)

    const isError = () => {
        if (props.row.getError !== undefined) {
            return getError() !== undefined
        }
        if (props.row.required && (getValue === undefined || getValue === '')) {
            return true
        }
        return false
    }

    const getHelperText = () => {
        if (props.row.getError !== undefined) {
            return getError()
        }
        if (props.row.required && (getValue === undefined || getValue === '')) {
            return 'Required'
        }
        return props.row.optional ? 'Optional' : undefined
    }

    return (
        <TextField
            fullWidth
            label={resolveContent()}
            required={props.row.required}
            variant="standard"
            value={getValue}
            onBlur={() => setInteractedWith(true)}
            onChange={e => onChange(e.target.value)}
            error={interactedWith && isError()}
            helperText={interactedWith ? getHelperText() : props.row.optional ? 'Optional' : undefined}
        />
    )
}
