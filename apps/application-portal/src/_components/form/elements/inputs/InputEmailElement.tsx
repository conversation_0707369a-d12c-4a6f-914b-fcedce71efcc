import {InputElement} from '@/flow-definitions/iface'
import {TextField} from '@mui/material'
import {z} from 'zod'
import {useInputElementHelpers} from './inputElementHooks'
import {useApplicationStore} from '@/store/applicationStore'
import {useState} from 'react'

const emailSchema = z.string().email()

export default function InputEmailElement(props: {row: InputElement<string>}) {
    const {content} = props.row
    const {getValue, getError, onChange, setError} = useInputElementHelpers(props.row)
    const [interactedWith, setInteractedWith] = useState(false)

    const validateEmail = (value: string) => {
        if (setError === undefined) {
            return
        }
        if (!value) {
            setError(undefined)
            return
        }
        try {
            emailSchema.parse(value)
            setError(undefined)
        } catch (err) {
            setError('Please enter a valid email address')
        }
    }

    return (
        <TextField
            fullWidth
            type="email"
            required={props.row.required}
            label={typeof content === 'function' ? content(useApplicationStore.getState()) : content}
            variant="standard"
            value={getValue}
            onBlur={() => setInteractedWith(true)}
            onChange={e => {
                onChange(e.target.value)
                validateEmail(e.target.value)
            }}
            error={interactedWith && getError() !== undefined}
            helperText={interactedWith ? getError() : undefined}
        />
    )
}
