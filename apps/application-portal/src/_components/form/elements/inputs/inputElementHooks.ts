import {InputElement} from '@/flow-definitions/iface'
import {useApplicationStore} from '@/store/applicationStore'

export function useInputElementHelpers<T>(element: InputElement<T>) {
    const resolveContent = (): string | JSX.Element | undefined => {
        if (typeof element.content === 'function') {
            return element.content(useApplicationStore.getState())
        }
        return element.content
    }

    const getValue = useApplicationStore(element.getValue)

    const getError = () => {
        const state = useApplicationStore.getState()
        return element.getError ? element.getError(state) : undefined
    }

    const onChange = (val: T) => {
        const state = useApplicationStore.getState()
        element.onChange(state, val)
    }

    const setError = (val?: string) => {
        if (element.setError) {
            const state = useApplicationStore.getState()
            element.setError(state, val)
        }
    }

    return {resolveContent, getValue, getError, onChange, setError}
}
