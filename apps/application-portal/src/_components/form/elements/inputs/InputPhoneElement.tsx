import 'react-international-phone/style.css'

import {BaseTextFieldProps, InputAdornment, MenuItem, Select, TextField, Typography} from '@mui/material'
import React, {useState} from 'react'
import {CountryIso2, defaultCountries, FlagImage, parseCountry, usePhoneInput} from 'react-international-phone'
import {InputElement} from '@/flow-definitions/iface'
import {PhoneNumberUtil} from 'google-libphonenumber'
import {useInputElementHelpers} from './inputElementHooks'

export interface InputPhoneElementProps extends BaseTextFieldProps {
    value: string
    onChange: (phone: string) => void
}

export default function InputPhoneElement(props: {row: InputElement<string>}) {
    const phoneUtil = PhoneNumberUtil.getInstance()
    const {getValue, getError, onChange, setError} = useInputElementHelpers(props.row)
    const [interactedWith, setInteractedWith] = useState(false)

    const validatePhoneNumber = (value: string) => {
        if (setError === undefined) {
            return
        }
        if (!value) {
            setError(undefined)
            return
        }
        try {
            if (!phoneUtil.isValidNumber(phoneUtil.parseAndKeepRawInput(value))) {
                setError('Please enter a valid phone number')
            } else {
                setError(undefined)
            }
        } catch (err) {
            setError('Please enter a valid phone number')
        }
    }

    const {inputValue, handlePhoneValueChange, inputRef, country, setCountry} = usePhoneInput({
        defaultCountry: 'us',
        value: getValue,
        countries: defaultCountries,
        onChange: data => {
            onChange(data.phone)
            validatePhoneNumber(data.phone)
        },
    })

    return (
        <TextField
            variant="standard"
            label="Phone number"
            color="primary"
            placeholder="Phone number"
            value={inputValue}
            onChange={handlePhoneValueChange}
            type="tel"
            inputRef={inputRef}
            onBlur={() => setInteractedWith(true)}
            error={interactedWith && getError() !== undefined}
            helperText={interactedWith ? getError() : undefined}
            slotProps={{
                input: {
                    startAdornment: (
                        <InputAdornment position="start" style={{marginRight: '2px', marginLeft: '-8px'}}>
                            <Select
                                MenuProps={{
                                    PaperProps: {
                                        style: {
                                            height: '300px',
                                            width: '360px',
                                            top: '10px',
                                            left: '-34px',
                                        },
                                    },
                                    transformOrigin: {
                                        vertical: 'top',
                                        horizontal: 'left',
                                    },
                                }}
                                sx={{
                                    width: 'max-content',
                                    fieldset: {
                                        display: 'none',
                                    },
                                    '&.Mui-focused:has(div[aria-expanded="false"])': {
                                        fieldset: {
                                            display: 'block',
                                        },
                                    },
                                    '.MuiSelect-select': {
                                        padding: 2,
                                        paddingRight: '24px !important',
                                    },
                                    svg: {
                                        right: 0,
                                    },
                                }}
                                value={country.iso2}
                                onChange={e => setCountry(e.target.value as CountryIso2)}
                                renderValue={value => <FlagImage iso2={value} style={{display: 'flex'}} />}
                            >
                                {defaultCountries.map(c => {
                                    const country = parseCountry(c)
                                    return (
                                        <MenuItem key={country.iso2} value={country.iso2}>
                                            <FlagImage iso2={country.iso2} style={{marginRight: '8px'}} />
                                            <Typography marginRight="8px">{country.name}</Typography>
                                            <Typography color="gray">+{country.dialCode}</Typography>
                                        </MenuItem>
                                    )
                                })}
                            </Select>
                        </InputAdornment>
                    ),
                },
            }}
        />
    )
}
