import {createOtp} from '@/actions/otp'
import {InputElement} from '@/flow-definitions/iface'
import {useApplicationStore} from '@/store/applicationStore'
import {<PERSON>, Stack, TextField, Typography} from '@mui/material'
import {useRef, useState, type KeyboardEvent, type ClipboardEvent} from 'react'
import {useInputElementHelpers} from './inputElementHooks'

export default function InputOTPPhoneCode(props: {row: InputElement<string>}) {
    const {onChange} = useInputElementHelpers(props.row)
    const applicationBusy = useApplicationStore(state => state.data.applicationBusy)

    const [code, setCode] = useState(['', '', '', '', '', ''])
    const inputRefs = [
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
    ]

    const handleChange = (index: number, value: string) => {
        // Only allow numbers
        if (!/^\d*$/.test(value)) {
            return
        }

        const newCode = [...code]
        newCode[index] = value

        setCode(newCode)
        onChange(newCode.join(''))

        // Auto-focus next input
        if (value !== '' && index < 5) {
            inputRefs[index + 1].current?.focus()
        }
    }

    const handleKeyDown = (index: number, e: KeyboardEvent) => {
        if (e.key === 'Backspace' && code[index] === '' && index > 0) {
            inputRefs[index - 1].current?.focus()
        }
    }

    const handlePaste = (e: ClipboardEvent) => {
        e.preventDefault()
        const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 4)

        const newCode = [...code]
        pastedData.split('').forEach((char, index) => {
            if (index < 4) newCode[index] = char
        })
        setCode(newCode)

        // Focus last filled input or first empty input
        const focusIndex = Math.min(pastedData.length, 3)
        inputRefs[focusIndex].current?.focus()
    }

    return (
        <Stack spacing={2}>
            <Stack direction="row" spacing={2} justifyContent="center">
                {code.map((digit, index) => (
                    <TextField
                        key={index}
                        variant="standard"
                        inputRef={inputRefs[index]}
                        value={digit}
                        onChange={e => handleChange(index, e.target.value)}
                        onKeyDown={e => handleKeyDown(index, e)}
                        onPaste={e => handlePaste(e)}
                        slotProps={{
                            htmlInput: {
                                maxLength: 1,
                                style: {textAlign: 'center', fontSize: '1.5rem'},
                            },
                        }}
                        sx={{
                            width: '4rem',
                            '& .MuiOutlinedInput-root': {
                                height: '4rem',
                            },
                        }}
                        autoFocus={!!(index === 0)}
                    />
                ))}
            </Stack>

            <Stack
                direction="row"
                justifyContent="center"
                alignItems="center"
                sx={{'& button': {textTransform: 'initial'}}}
            >
                <Link
                    component="button"
                    variant="body2"
                    color="info"
                    disabled={applicationBusy}
                    onClick={async () => {
                        useApplicationStore.getState().actions.setBusy(true)
                        const result = await createOtp(useApplicationStore.getState().data.phoneNumber?.value || '')
                        if (!result.success) {
                            // TODO - error placeholder?
                        }
                        useApplicationStore.getState().actions.setBusy(false)
                    }}
                >
                    Resend code
                </Link>
                <Typography color="textSecondary" variant="body2">
                    &nbsp;or&nbsp;
                </Typography>
                <Link
                    component="button"
                    variant="body2"
                    color="info"
                    disabled={applicationBusy}
                    onClick={() => {
                        useApplicationStore.getState().actions.stepBackward()
                    }}
                >
                    Re-enter mobile phone number
                </Link>
            </Stack>
        </Stack>
    )
}
