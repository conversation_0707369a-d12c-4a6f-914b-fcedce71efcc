import {InputElement} from '@/flow-definitions/iface'
import {TextField} from '@mui/material'
import {useInputElementHelpers} from './inputElementHooks'
import {useState} from 'react'

export default function InputNumeric(props: {row: InputElement<string>}) {
    const {resolveContent, getValue, getError, onChange} = useInputElementHelpers(props.row)
    const [interactedWith, setInteractedWith] = useState(false)

    const getErrorFunc = () => {
        if (props.row.getError !== undefined) {
            return getError()
        }
        if (props.row.required && (getValue === undefined || getValue === '')) {
            return 'Required'
        }
        return undefined
    }

    return (
        <TextField
            fullWidth
            type="text" // TODO - type number is better UX (ie. on mobile it will modify the keyboard properly), however it causes some weird DOM rendering issues that ill save for later
            sx={{
                // For Chrome, Safari, Edge, Opera
                '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
                    display: 'none',
                },
                // For Firefox
                '& input[type=number]': {
                    MozAppearance: 'textfield',
                },
            }}
            label={resolveContent()}
            required={props.row.required}
            variant="standard"
            value={getValue}
            onBlur={() => setInteractedWith(true)}
            onChange={e => onChange(e.target.value)}
            error={interactedWith && getErrorFunc() !== undefined}
            helperText={interactedWith ? getErrorFunc() : props.row.optional ? 'Optional' : undefined}
        />
    )
}
