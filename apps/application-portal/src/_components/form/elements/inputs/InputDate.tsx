import {InputElement} from '@/flow-definitions/iface'
import {useInputElementHelpers} from './inputElementHooks'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import {TextField} from '@mui/material'
import {useState, type ChangeEvent, type KeyboardEvent} from 'react'

export default function InputDate(props: {row: InputElement<string>}) {
    const {resolveContent, getValue, getError, setError, onChange} = useInputElementHelpers(props.row)
    const [interactedWith, setInteractedWith] = useState(false)

    dayjs.extend(customParseFormat)

    const validateDate = (value: string): string | undefined => {
        if (!value) {
            return undefined
        }
        const date = dayjs(value, 'MM/DD/YYYY', true)
        if (!date.isValid()) {
            return 'Please enter a valid date'
        }
        return undefined
    }

    const handleDateChange = (e: ChangeEvent<HTMLInputElement>) => {
        const input = e.target
        const cursorPosition = input.selectionStart || 0
        let value = input.value.replace(/\D/g, '')
        if (value.length > 8) value = value.slice(0, 8)

        // Format with slashes
        let formattedValue = value
        if (value.length >= 4) {
            formattedValue = `${value.slice(0, 2)}/${value.slice(2, 4)}/${value.slice(4)}`
        } else if (value.length >= 2) {
            formattedValue = `${value.slice(0, 2)}/${value.slice(2)}`
        }

        onChange(formattedValue)
        setError(validateDate(formattedValue))

        // Calculate new cursor position after formatting
        setTimeout(() => {
            const addedSlashes = (formattedValue.match(/\//g) || []).length
            const previousSlashes = (input.value.slice(0, cursorPosition).match(/\//g) || []).length
            const newPosition = cursorPosition + (addedSlashes - previousSlashes)
            input.setSelectionRange(newPosition, newPosition)
        }, 0)
    }

    const handleDateKeydown = (e: KeyboardEvent<HTMLInputElement>) => {
        const input = e.target as HTMLInputElement
        const cursorPosition = input.selectionStart || 0

        if (e.key === 'Backspace') {
            e.preventDefault()

            // Get the position before the cursor, accounting for slashes
            const valueArray = input.value.split('')
            if (cursorPosition > 0) {
                // Remove the character at the cursor position - 1
                let newPosition = cursorPosition - 1
                // Skip over slashes when backspacing
                if (valueArray[newPosition] === '/') {
                    newPosition--
                }

                const newValue = valueArray
                    .filter((char, i) => i !== newPosition)
                    .join('')
                    .replace(/\D/g, '')

                // Format with slashes
                let formattedValue = newValue
                if (newValue.length >= 4) {
                    formattedValue = `${newValue.slice(0, 2)}/${newValue.slice(2, 4)}/${newValue.slice(4)}`
                } else if (newValue.length >= 2) {
                    formattedValue = `${newValue.slice(0, 2)}/${newValue.slice(2)}`
                }

                onChange(formattedValue)
                setError(validateDate(formattedValue))

                // Update cursor position
                setTimeout(() => {
                    input.setSelectionRange(newPosition, newPosition)
                }, 0)
            }
        }
    }

    return (
        <TextField
            fullWidth
            variant="standard"
            label={resolveContent()}
            value={getValue}
            onBlur={() => setInteractedWith(true)}
            onChange={handleDateChange}
            onKeyDown={handleDateKeydown}
            error={interactedWith && getError() !== undefined}
            helperText={interactedWith ? getError() : undefined}
            placeholder="MM/DD/YYYY"
            slotProps={{inputLabel: {shrink: true}}}
        />
    )
}
