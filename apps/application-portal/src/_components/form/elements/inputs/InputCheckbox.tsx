import {InputElement} from '@/flow-definitions/iface'
import {Checkbox, FormControlLabel, FormGroup, Typography} from '@mui/material'
import {useInputElementHelpers} from './inputElementHooks'

export default function InputCheckbox(props: {row: InputElement<boolean>}) {
    const {resolveContent, getValue, onChange} = useInputElementHelpers(props.row)

    return (
        <FormGroup>
            <FormControlLabel
                sx={{ml: 0}}
                control={<Checkbox sx={{pr: 2}} value={getValue} onChange={e => onChange(e.target.checked)} />}
                label={
                    <Typography variant="body1" fontSize={14} color="textSecondary">
                        {resolveContent()}
                    </Typography>
                }
            />
        </FormGroup>
    )
}
