import {DropdownElement} from '@/flow-definitions/iface'
import {MenuItem, TextField} from '@mui/material'
import {useInputElementHelpers} from './inputElementHooks'

export default function InputDropdown(props: {row: DropdownElement<string>}) {
    const {resolveContent, getValue, getError, onChange} = useInputElementHelpers(props.row)

    const getErrorFunc = () => {
        if (props.row.getError !== undefined) {
            return getError()
        }
        if (props.row.required && (getValue === undefined || getValue === '')) {
            return 'Required'
        }
        return undefined
    }

    const renderOptions = () => {
        let options = []
        for (const [key, value] of Object.entries(props.row.options)) {
            options.push(
                <MenuItem key={key} value={value}>
                    {key}
                </MenuItem>,
            )
        }
        return options
    }

    return (
        <TextField
            select
            fullWidth
            label={resolveContent()}
            required={props.row.required}
            variant="standard"
            value={getValue}
            onChange={e => onChange(e.target.value)}
            error={getErrorFunc() !== undefined}
            helperText={getErrorFunc()}
        >
            {renderOptions()}
        </TextField>
    )
}
