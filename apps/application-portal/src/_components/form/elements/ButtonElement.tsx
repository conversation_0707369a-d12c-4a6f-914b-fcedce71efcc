import {ButtonElement as ButtonElementType} from '@/flow-definitions/iface'
import {useApplicationStore} from '@/store/applicationStore'
import {Button} from '@mui/material'

export default function ButtonElement(props: {row: ButtonElementType}) {
    const {buttonVariant} = props.row
    const applicationBusy = useApplicationStore(state => state.data.applicationBusy)
    const isEnabled = useApplicationStore(props.row.isEnabled)
    const onClick = () => {
        const state = useApplicationStore.getState()
        props.row.onClick(state)
    }

    const resolveContent = (): string | JSX.Element | undefined => {
        if (typeof props.row.content === 'function') {
            return props.row.content(useApplicationStore.getState())
        }
        return props.row.content
    }

    return (
        <Button
            variant={buttonVariant}
            color="secondary"
            size="large"
            sx={{borderRadius: 'var(--Button-Corner-Radius, 48px)'}}
            type="submit"
            disabled={!isEnabled || applicationBusy}
            onClick={onClick}
            loading={isEnabled && applicationBusy}
        >
            {resolveContent()}
        </Button>
    )
}
