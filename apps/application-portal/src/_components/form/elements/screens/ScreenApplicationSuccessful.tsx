import {DisplayOnlyElement} from '@/flow-definitions/iface'
import Image from 'next/image'
import {<PERSON>ert, AlertTitle, Grid2, Stack, Typography} from '@mui/material'

export default function ScreenApplicationSuccessful(props: {row: DisplayOnlyElement}) {
    return (
        <Stack direction={'column'} spacing={2}>
            <Stack spacing={1.5}>
                <Typography variant="h1" fontSize={32} fontWeight={700}>
                    Congratulations!
                </Typography>
                <Typography variant="h2" color="textSecondary" fontSize={20} fontWeight={700}>
                    {/* TODO - prop */}
                    Your ADA Card has been approved
                </Typography>
            </Stack>

            {/* TODO - pull out into its own component */}
            <Grid2 container spacing={1} sx={{pl: '16px', pr: '16px', pb: '16px'}}>
                <Grid2 size={6}>
                    {/* TODO - prop */}
                    <Stack spacing={1}>
                        <Typography variant="h3" fontSize={24} fontWeight={700}>
                            ADA Card
                        </Typography>
                        <Stack spacing={0}>
                            <Typography
                                variant="h4"
                                color="textSecondary"
                                fontSize={16}
                                fontWeight={400}
                                lineHeight={'20px'}
                            >
                                Credit Limit
                            </Typography>
                            <Typography variant="body1" fontSize={40} fontWeight={600} lineHeight={'40px'}>
                                {/* TODO - comes from prequal response */}
                                $20,000
                            </Typography>
                        </Stack>
                    </Stack>
                </Grid2>
                {/* TODO pt is a hack because rotated elements dont cause layout adjustments, we should probably just use a pre-rotated png and keep it simple */}
                <Grid2 container size={6} alignContent={'center'} justifyContent={'center'} sx={{pt: '16px'}}>
                    {/* TODO - prop */}
                    {/* TODO - card-edge-gradient in theme */}
                    <Stack gap={2} alignItems={'center'}>
                        <Image
                            style={{
                                borderRadius: '2px',
                                border: '0.5px solid var(--card-edge-gradient, #5BAD5B)',
                                rotate: '-50deg',
                            }}
                            src="/assets/whitelabeling/ada-card.png"
                            alt="ADA Card"
                            objectFit="contain"
                            width={95}
                            height={60}
                        />
                        <Image
                            src="/assets/cardart-shadow.svg"
                            alt="card art shadow"
                            objectFit="contain"
                            width={57}
                            height={22}
                        ></Image>
                    </Stack>
                </Grid2>
                <Grid2 size={4}>
                    <Stack direction={'column'}>
                        <Typography
                            variant="h4"
                            color="textSecondary"
                            fontSize={16}
                            fontWeight={400}
                            lineHeight={'20px'}
                        >
                            Rewards
                        </Typography>
                        <Typography variant="body1" fontSize={20} fontWeight={600}>
                            {/* TODO - comes from prequal response */}
                            Up to 5X
                        </Typography>
                    </Stack>
                </Grid2>
                <Grid2 size={4}>
                    <Stack direction={'column'}>
                        <Typography
                            variant="h4"
                            color="textSecondary"
                            fontSize={16}
                            fontWeight={400}
                            lineHeight={'20px'}
                        >
                            Annual Fee
                        </Typography>
                        <Typography variant="body1" fontSize={20} fontWeight={600}>
                            {/* TODO - comes from prequal response */}
                            $0.00
                        </Typography>
                    </Stack>
                </Grid2>
                <Grid2 size={4}>
                    <Stack direction={'column'}>
                        <Typography
                            variant="h4"
                            color="textSecondary"
                            fontSize={16}
                            fontWeight={400}
                            lineHeight={'20px'}
                        >
                            APR
                        </Typography>
                        <Typography variant="body1" fontSize={20} fontWeight={600}>
                            {/* TODO - comes from prequal response */}
                            24.99%
                        </Typography>
                    </Stack>
                </Grid2>
            </Grid2>
            {/* TODO - comes off application response or something, shouldn't be hard-coded */}
            <Alert severity="info" icon={false}>
                <AlertTitle color="textPrimary" sx={{fontWeight: 600}}>
                    Your card will be delivered in 7-10 business days.
                </AlertTitle>
                <Typography variant="body1" fontSize={16} color="textPrimary">
                    In the meantime you will receive an email verification from us. Once you verify your email, you can
                    start using your card right away - even before it arrives in the mail!
                </Typography>
            </Alert>
        </Stack>
    )
}
