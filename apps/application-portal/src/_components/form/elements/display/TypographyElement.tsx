import {DisplayOnlyElement} from '@/flow-definitions/iface'
import {useApplicationStore} from '@/store/applicationStore'
import {Typography} from '@mui/material'

export default function TypographyElement(props: {row: DisplayOnlyElement}) {
    const {fontColor, fontSize, fontWeight, fontVariant} = props.row

    const useFontColor = fontColor || 'textSecondary'
    const useFontSize = fontSize || 16
    const useFontWeight = fontWeight || 400
    const useVariant = fontVariant || 'body1'

    const resolveContent = (): string | JSX.Element | undefined => {
        if (typeof props.row.content === 'function') {
            return props.row.content(useApplicationStore.getState())
        }
        return props.row.content
    }

    return (
        <Typography variant={useVariant} color={useFontColor} fontSize={useFontSize} fontWeight={useFontWeight}>
            {resolveContent()}
        </Typography>
    )
}
