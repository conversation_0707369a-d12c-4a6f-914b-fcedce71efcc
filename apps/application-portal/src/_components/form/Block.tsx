import {ApplicationBlockDefinition} from '@/flow-definitions/iface'
import Row from './Row'
import {Stack} from '@mui/material'

/**
 * A Block is a fundamental building block of an application form
 *
 * It consists of a collection of rows which contain the actual contents of the form,
 * this design is so you can group your rows up and space them apart uniquely.
 */
export default function Block(props: {block: ApplicationBlockDefinition}) {
    return (
        <Stack spacing={props.block.rowGap || 0}>
            {props.block.rows.map((row, index) => (
                <Row row={row} key={`block-row-${index}`} />
            ))}
        </Stack>
    )
}
