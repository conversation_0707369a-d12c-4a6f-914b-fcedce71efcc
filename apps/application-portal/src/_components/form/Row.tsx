import {
    ApplicationRowDefinition,
    ApplicationColumnDefinition,
    ApplicationElementType,
    DisplayOnlyElement,
    ButtonElement as ButtonElementType,
    InputElement,
    DropdownElement,
} from '@/flow-definitions/iface'
import {inLocalDev} from '@/utils'
import ButtonElement from './elements/ButtonElement'
import InputOTPPhoneCode from './elements/inputs/InputOTPPhoneCode'
import InputText from './elements/inputs/InputText'
import Stack from '@mui/material/Stack'
import InputDropdown from './elements/inputs/InputDropdown'
import ScreenPrequalSuccessful from './elements/screens/ScreenPrequalSuccessful'
import InputEmailElement from './elements/inputs/InputEmailElement'
import TypographyElement from './elements/display/TypographyElement'
import InputPhoneElement from './elements/inputs/InputPhoneElement'
import InputDate from './elements/inputs/InputDate'
import DividerElement from './elements/display/DividerElement'
import InputCheckbox from './elements/inputs/InputCheckbox'
import ScreenApplicationSuccessful from './elements/screens/ScreenApplicationSuccessful'
import InputNumeric from './elements/inputs/InputNumeric'

function isColumns(row: ApplicationRowDefinition): row is ApplicationColumnDefinition[] {
    return Array.isArray(row)
}

/**
 * A Row is a fundamental building block of an application form
 *
 * Each row corresponds to a horizontal line of the form, and can contain
 * many things (text, images, fields, etc), these are called elements
 *
 * Rows can be given a unique identifier in order to manipulate them via events
 * for example, update the validation state of a form field after submit is hit
 *
 * Rows can also contain columns, each column contains an element.  The columns will be
 * divided evenly amongst their respective row.
 */
export default function Row(props: {row: ApplicationRowDefinition}) {
    const renderElement = function (
        element: Exclude<ApplicationRowDefinition, ApplicationColumnDefinition[]>,
        columnIndex: number,
    ) {
        if (element instanceof DropdownElement) {
            switch (element.type) {
                case ApplicationElementType.InputDropdown:
                    return <InputDropdown row={element} key={`input-dropdown-${columnIndex}`} />
            }
        } else if (element instanceof InputElement) {
            switch (element.type) {
                case ApplicationElementType.InputCheckbox:
                    return <InputCheckbox row={element} key={`input-checkbox-${columnIndex}`} />
                case ApplicationElementType.InputText:
                    return <InputText row={element} key={`input-text-${columnIndex}`} />
                case ApplicationElementType.InputNumber:
                    return <InputNumeric row={element} key={`input-numeric-${columnIndex}`} />
                case ApplicationElementType.InputEmail:
                    return <InputEmailElement row={element} key={`input-email-${columnIndex}`} />
                case ApplicationElementType.InputDate:
                    return <InputDate row={element} key={`input-date-${columnIndex}`} />
                case ApplicationElementType.InputPhone:
                    return <InputPhoneElement row={element} key={`input-phone-${columnIndex}`} />
                case ApplicationElementType.InputOTPPhoneCode:
                    return <InputOTPPhoneCode row={element} key={`input-otp-${columnIndex}`} />
            }
        } else if (element instanceof ButtonElementType) {
            switch (element.type) {
                case ApplicationElementType.Button:
                    return <ButtonElement row={element} key={`button-${columnIndex}`} />
            }
        } else if (element instanceof DisplayOnlyElement) {
            switch (element.type) {
                case ApplicationElementType.Divider:
                    return <DividerElement row={element} key={`divider-${columnIndex}`} />
                case ApplicationElementType.Text:
                    return <TypographyElement row={element} key={`text-${columnIndex}`} />
                case ApplicationElementType.ScreenPrequalificationSuccessful:
                    return <ScreenPrequalSuccessful row={element} key={`screen-prequal-${columnIndex}`} />
                case ApplicationElementType.ScreenApplicationSuccessful:
                    return <ScreenApplicationSuccessful row={element} key={`screen-application-${columnIndex}`} />
            }
        }
        if (inLocalDev()) {
            return <p key={`unknown-row-${columnIndex}`}>Unknown row type: {element.type}</p>
        } else {
            return null
        }
    }

    const renderRow = function (row: ApplicationRowDefinition, columnIndex: number = 0) {
        if (isColumns(row)) {
            return (
                <Stack direction={'row'} spacing={2}>
                    {row.map((column, index) => renderElement(column, index))}
                </Stack>
            )
        }
        return renderElement(row, columnIndex)
    }

    return <>{renderRow(props.row)}</>
}
