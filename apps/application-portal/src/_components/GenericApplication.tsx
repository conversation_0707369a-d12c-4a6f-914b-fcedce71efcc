'use client'

import {Box, Grid2, Stack} from '@mui/material'
import Header from '@/_components/whitelabelling/Header'
import Footer from '@/_components/whitelabelling/Footer'

import {type Promotion} from '@/services/controllers/acquisition'
import {ApplicationBlockDefinition, ApplicationFlowDefinition} from '@/flow-definitions/iface'
import Block from './form/Block'
import {useEffect, useState} from 'react'
import {useApplicationStore} from '@/store/applicationStore'
import GranularStepper from './GranularStepper'
import React from 'react'
import {getProgramFlow} from '@/flow-definitions/programFlow'
import {useProgramContext} from './providers/ProgramContextProvider'

export default function GenericApplication(props: {promotion: Promotion}) {
    const [flow, setFlow] = useState<ApplicationFlowDefinition | undefined>(undefined)
    const {themeName} = useProgramContext()

    useEffect(() => {
        const programFlow = getProgramFlow(themeName)
        if (programFlow !== undefined) {
            useApplicationStore.getState().actions.setFlow(programFlow)
            setFlow(programFlow)
        }
    }, [themeName])

    const currentStep = useApplicationStore(state => state.data.currentStep)
    const currentSubstep = useApplicationStore(state => state.data.currentSubstep)

    const renderCurrentStep = function (flow: ApplicationFlowDefinition): JSX.Element {
        const stepDefinition = flow.steps[currentStep]

        let blocksToRender: ApplicationBlockDefinition[] = []
        let blockGap = 0
        if (stepDefinition.substeps) {
            if (currentSubstep < stepDefinition.substeps.length) {
                blocksToRender = stepDefinition.substeps[currentSubstep || 0].blocks || []
                blockGap = stepDefinition.substeps[currentSubstep || 0].blockGap || 0
            }
        } else if (stepDefinition.blocks) {
            blocksToRender = stepDefinition.blocks
            blockGap = stepDefinition.blockGap || 0
        }
        return (
            <Stack spacing={blockGap} height="100%">
                {blocksToRender.map((block, index) => {
                    const isSecondToLast = index === blocksToRender.length - 2
                    const shouldInsertSpacer = blocksToRender.length >= 2

                    return (
                        <React.Fragment key={index}>
                            <Block block={block} />
                            {isSecondToLast && shouldInsertSpacer && <Box sx={{flexGrow: 1}} />}
                        </React.Fragment>
                    )
                })}
            </Stack>
        )
    }

    const renderContent = () => {
        if (flow === undefined) {
            return null
        }
        return (
            <Stack p={2} flex={1} direction={'column'} spacing={2}>
                <GranularStepper flow={flow}></GranularStepper>
                {renderCurrentStep(flow)}
            </Stack>
        )
    }

    return (
        <Box sx={{display: 'flex', flexDirection: 'column', flex: 1, height: '100vh'}}>
            <Grid2 container wrap="nowrap" alignSelf="stretch" flex={1} flexDirection="column" alignItems="center">
                <Header />
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        maxWidth: 800,
                        width: '100%',
                        margin: '0 auto',
                        flex: 1,
                    }}
                >
                    {renderContent()}
                </Box>
                <Footer />
            </Grid2>
        </Box>
    )
}
