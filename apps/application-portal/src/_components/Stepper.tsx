import {Box, Grid2} from '@mui/material'

export default function Stepper(props: {active: number; total: number}) {
    return (
        <Grid2
            container
            gap={1}
            flexDirection="row"
            flex={0}
            sx={{mb: 2}}
            role="progressbar"
            aria-valuemin={1}
            aria-valuemax={props.total}
            aria-valuenow={props.active}
            aria-label="Application progress"
        >
            {Array.from({length: props.total}, (_, index) => {
                return (
                    <Box
                        key={index}
                        sx={{
                            backgroundColor:
                                props.active - 1 === index
                                    ? 'var(--mui-palette-primary-main)'
                                    : 'var(--mui-palette-action-disabled)',
                            height: 8,
                            borderRadius: '4px',
                            flex: 1,
                        }}
                        aria-hidden="true"
                    ></Box>
                )
            })}
        </Grid2>
    )
}
