import {useState} from 'react'
import {Box, Button, Typography, Stack, FormGroup, FormControlLabel, Checkbox, Link, Grid2} from '@mui/material'

interface Disclosure {
    id: string
    checked: boolean
    links: Array<{
        text: string
        href: string
        target?: string
        rel?: string
    }>
}

interface ImportantInformationProps {
    onNext: (data: {disclosures: Disclosure[]}) => void
    disclosures: Disclosure[]
}

export default function ImportantInformationInput({onNext, disclosures}: ImportantInformationProps) {
    const [acceptedDisclosures, setAcceptedDisclosures] = useState<Record<string, boolean>>(() => {
        const initialState: Record<string, boolean> = {}
        disclosures.forEach(disclosure => {
            initialState[disclosure.id] = disclosure.checked
        })
        return initialState
    })

    const handleCheckboxChange = (id: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
        setAcceptedDisclosures(prev => ({
            ...prev,
            [id]: event.target.checked,
        }))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        const updatedDisclosures = disclosures.map(disclosure => ({
            ...disclosure,
            checked: acceptedDisclosures[disclosure.id],
        }))
        onNext({disclosures: updatedDisclosures})
    }

    const allAccepted = Object.values(acceptedDisclosures).every(value => value)

    return (
        <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            }}
        >
            <Stack spacing={3} sx={{flex: 1}}>
                <Box>
                    <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                        Important Information
                    </Typography>
                    <Typography variant="body1" color="textSecondary" gutterBottom>
                        Tallied Technologies, Inc (Tallied). is providing you with a Tallied Internal Mastercard Card™
                        as an authorized user. Please review our Privacy Policy, Cottonwood Payments Privacy Policy,
                        Terms of Service, and Electronic Communications Disclosure and Agreement:
                    </Typography>
                </Box>

                <FormGroup>
                    {disclosures.map(disclosure => (
                        <FormControlLabel
                            key={disclosure.id}
                            control={
                                <Checkbox
                                    checked={acceptedDisclosures[disclosure.id]}
                                    onChange={handleCheckboxChange(disclosure.id)}
                                />
                            }
                            label={
                                <Typography variant="body1">
                                    I have reviewed and agree to{' '}
                                    {disclosure.links.map((link, index) => (
                                        <span key={link.text}>
                                            <Link href={link.href} target={link.target} rel={link.rel} color="primary">
                                                {link.text}
                                            </Link>
                                            {index < disclosure.links.length - 1 && (
                                                <span>{index === disclosure.links.length - 2 ? ', and ' : ', '}</span>
                                            )}
                                        </span>
                                    ))}
                                    .
                                </Typography>
                            }
                            sx={{mb: 2}}
                        />
                    ))}
                </FormGroup>

                <Grid2 container gap={3} flexDirection="column" sx={{mt: 'auto'}}>
                    <Typography color="textSecondary">
                        If you proceed by clicking the “NEXT” below, you have consented to Tallied’s Privacy Policy,
                        Cottonwood Payments Privacy Policy, Terms of Use, and Electronic Communications Disclosure and
                        Agreement, which you can review by clicking the links above.
                    </Typography>

                    <Button type="submit" variant="contained" fullWidth disabled={!allAccepted} size="large">
                        NEXT
                    </Button>
                    <Typography variant="body2" color="textSecondary" fontSize={12}>
                        Finwise Bank, a Utah-chartered bank, d/b/a Cottonwood Payments, issued a business credit card to
                        Tallied Technologies, Inc. (“Tallied”), pursuant to a license agreement with Mastercard
                        International. Tallied is allowed to provide you with a credit card for business purposes as an
                        authorized user.
                    </Typography>
                </Grid2>
            </Stack>
        </Box>
    )
}
