import {useState, useRef, type KeyboardEvent, type ClipboardEvent, type FormEvent} from 'react'
import {Box, Button, Typography, Stack, TextField} from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'

interface VerificationInputProps {
    phoneNumber: string
    submissionError?: string
    onNext: (code: string) => Promise<void>
    onPrevious: () => void
    onResendCode: () => Promise<void>
}

const formatPhoneNumber = (phone: string) => {
    return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`
}

export default function VerificationInput({
    phoneNumber,
    submissionError,
    onNext,
    onPrevious,
    onResendCode,
}: VerificationInputProps) {
    const [code, setCode] = useState(['', '', '', '', '', ''])
    const inputRefs = [
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
    ]
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleChange = (index: number, value: string) => {
        // Only allow numbers
        if (!/^\d*$/.test(value)) return

        const newCode = [...code]
        newCode[index] = value

        setCode(newCode)

        // Auto-focus next input
        if (value !== '' && index < 5) {
            inputRefs[index + 1].current?.focus()
        }
    }

    const handleKeyDown = (index: number, e: KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Backspace' && code[index] === '' && index > 0) {
            inputRefs[index - 1].current?.focus()
        }
    }

    const handlePaste = (e: ClipboardEvent) => {
        e.preventDefault()
        const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 4)

        const newCode = [...code]
        pastedData.split('').forEach((char, index) => {
            if (index < 4) newCode[index] = char
        })
        setCode(newCode)

        // Focus last filled input or first empty input
        const focusIndex = Math.min(pastedData.length, 3)
        inputRefs[focusIndex].current?.focus()
    }

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault()
        if (code.every(digit => digit !== '')) {
            setIsSubmitting(true)
            await onNext(code.join(''))
            setIsSubmitting(false)
        }
    }

    const handleResendCode = async () => {
        setIsSubmitting(true)
        await onResendCode()
        setIsSubmitting(false)
    }

    const renderError = () => {
        if (submissionError) {
            return (
                <Typography variant="body2" color="error" gutterBottom>
                    {submissionError}
                </Typography>
            )
        }
        return null
    }

    return (
        <Box
            component="form"
            onSubmit={handleSubmit}
            mx="auto"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            }}
        >
            <Stack spacing={3} sx={{flex: 1}}>
                <Box>
                    <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                        Verification Code
                    </Typography>

                    <Typography variant="body1" color="textSecondary" gutterBottom>
                        Enter the 6-digit code that was sent to:
                    </Typography>

                    <Typography variant="h6" gutterBottom fontWeight={600}>
                        {formatPhoneNumber(phoneNumber)}
                    </Typography>
                </Box>

                <Stack direction="row" spacing={2} justifyContent="center">
                    {code.map((digit, index) => (
                        <TextField
                            key={index}
                            inputRef={inputRefs[index]}
                            value={digit}
                            onChange={e => handleChange(index, e.target.value)}
                            onKeyDown={e => handleKeyDown(index, e)}
                            onPaste={handlePaste}
                            slotProps={{
                                htmlInput: {
                                    maxLength: 1,
                                    style: {textAlign: 'center', fontSize: '1.5rem'},
                                },
                            }}
                            sx={{
                                width: '4rem',
                                '& .MuiOutlinedInput-root': {
                                    height: '4rem',
                                },
                            }}
                            autoFocus={!!(index === 0)}
                        />
                    ))}
                </Stack>

                <Stack
                    direction="row"
                    spacing={1}
                    justifyContent="center"
                    alignItems="center"
                    sx={{'& button': {textTransform: 'initial'}}}
                >
                    <Button variant="text" onClick={handleResendCode} size="small" disabled={isSubmitting}>
                        Resend code
                    </Button>
                    <Typography color="textSecondary">or</Typography>
                    <Button variant="text" onClick={onPrevious} size="small" disabled={isSubmitting}>
                        Re-enter mobile phone number
                    </Button>
                </Stack>

                <Box sx={{flex: 1}} />

                {renderError()}

                <Box sx={{mt: 'auto'}}>
                    <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        fullWidth
                        sx={{mb: 2}}
                        disabled={!code.every(digit => digit !== '') || isSubmitting}
                        loading={isSubmitting}
                    >
                        NEXT
                    </Button>
                    <Button variant="text" fullWidth startIcon={<ArrowBackIcon />} onClick={onPrevious}>
                        Previous Page
                    </Button>
                </Box>
            </Stack>
        </Box>
    )
}
