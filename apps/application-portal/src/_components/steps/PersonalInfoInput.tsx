import {useState, type ChangeEvent, type FormEvent, type KeyboardEvent} from 'react'
import {Box, Button, Typography, Stack, TextField, Grid2} from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import {z} from 'zod'

// Helper to convert MM/DD/YYYY to YYYY-MM-DD
const convertDateFormat = (date: string): string => {
    const [month, day, year] = date.split('/')
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
}

// Helper to validate date format and range
const isValidDate = (dateStr: string): boolean => {
    const [month, day, year] = dateStr.split('/').map(Number)
    const date = new Date(year, month - 1, day)
    return (
        date.getMonth() === month - 1 &&
        date.getDate() === day &&
        date.getFullYear() === year &&
        date <= new Date() &&
        date.getFullYear() >= 1900
    )
}

const personalInfoSchema = z.object({
    dob: z
        .string()
        .regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Date must be in MM/DD/YYYY format')
        .refine(isValidDate, 'Please enter a valid date'),
    taxId: z.string().regex(/^\d{9}$/, 'SSN must be 9 digits'),
})

type PersonalInfoSchema = z.infer<typeof personalInfoSchema>

interface PersonalInfoInputProps {
    onNext: (data: {dob: string; taxId: string}) => void
    onPrevious: () => void
    dob: string
    taxId: string
    submissionError?: string
}

export default function PersonalInfoInput({
    onNext,
    onPrevious,
    dob = '',
    taxId = '',
    submissionError,
}: PersonalInfoInputProps) {
    const [formData, setFormData] = useState<PersonalInfoSchema>({
        dob: dob
            ? new Date(dob).toLocaleDateString('en-US', {
                  month: '2-digit',
                  day: '2-digit',
                  year: 'numeric',
              })
            : '',
        taxId: taxId,
    })

    const [errors, setErrors] = useState<Partial<Record<keyof PersonalInfoSchema, string>>>({})

    // Handle date of birth input with mask
    const handleDobChange = (e: ChangeEvent<HTMLInputElement>) => {
        const input = e.target
        const cursorPosition = input.selectionStart || 0
        let value = input.value.replace(/\D/g, '')
        if (value.length > 8) value = value.slice(0, 8)

        // Format with slashes
        let formattedValue = value
        if (value.length >= 4) {
            formattedValue = `${value.slice(0, 2)}/${value.slice(2, 4)}/${value.slice(4)}`
        } else if (value.length >= 2) {
            formattedValue = `${value.slice(0, 2)}/${value.slice(2)}`
        }

        setFormData(prev => ({...prev, dob: formattedValue}))
        validateField('dob', formattedValue)

        // Calculate new cursor position after formatting
        setTimeout(() => {
            const addedSlashes = (formattedValue.match(/\//g) || []).length
            const previousSlashes = (input.value.slice(0, cursorPosition).match(/\//g) || []).length
            const newPosition = cursorPosition + (addedSlashes - previousSlashes)
            input.setSelectionRange(newPosition, newPosition)
        }, 0)
    }

    // Handle backspace for DOB field
    const handleDobKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
        const input = e.target as HTMLInputElement
        const cursorPosition = input.selectionStart || 0

        if (e.key === 'Backspace') {
            e.preventDefault()

            // Get the position before the cursor, accounting for slashes
            const valueArray = input.value.split('')
            if (cursorPosition > 0) {
                // Remove the character at the cursor position - 1
                let newPosition = cursorPosition - 1
                // Skip over slashes when backspacing
                if (valueArray[newPosition] === '/') {
                    newPosition--
                }

                const newValue = valueArray
                    .filter((char, i) => i !== newPosition)
                    .join('')
                    .replace(/\D/g, '')

                // Format with slashes
                let formattedValue = newValue
                if (newValue.length >= 4) {
                    formattedValue = `${newValue.slice(0, 2)}/${newValue.slice(2, 4)}/${newValue.slice(4)}`
                } else if (newValue.length >= 2) {
                    formattedValue = `${newValue.slice(0, 2)}/${newValue.slice(2)}`
                }

                setFormData(prev => ({...prev, dob: formattedValue}))
                validateField('dob', formattedValue)

                // Update cursor position
                setTimeout(() => {
                    input.setSelectionRange(newPosition, newPosition)
                }, 0)
            }
        }
    }

    // Handle SSN input
    const handleSsnChange = (e: ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value.replace(/\D/g, '')
        if (value.length > 9) value = value.slice(0, 9)

        setFormData(prev => ({...prev, taxId: value}))
        validateField('taxId', value)
    }

    const validateField = (field: keyof PersonalInfoSchema, value: string) => {
        try {
            const fieldSchema = personalInfoSchema.shape[field]
            fieldSchema.parse(value)
            setErrors(prev => ({...prev, [field]: undefined}))
        } catch (error) {
            if (error instanceof z.ZodError) {
                setErrors(prev => ({
                    ...prev,
                    [field]: error.errors[0]?.message,
                }))
            }
        }
    }

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault()
        try {
            const validatedData = personalInfoSchema.parse(formData)
            onNext({
                dob: convertDateFormat(validatedData.dob),
                taxId: validatedData.taxId,
            })
        } catch (error) {
            if (error instanceof z.ZodError) {
                const newErrors: Partial<Record<keyof PersonalInfoSchema, string>> = {}
                error.errors.forEach(err => {
                    if (err.path[0]) {
                        newErrors[err.path[0] as keyof PersonalInfoSchema] = err.message
                    }
                })
                setErrors(newErrors)
            }
        }
    }

    const renderError = () => {
        if (submissionError) {
            return (
                <Typography variant="body2" color="error" gutterBottom>
                    {submissionError}
                </Typography>
            )
        }
        return null
    }

    return (
        <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            }}
        >
            <Stack spacing={3} sx={{flex: 1}}>
                <Box>
                    <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                        Your Info
                    </Typography>
                    <Typography variant="subtitle1" color="textSecondary" gutterBottom>
                        Just a few last details to verify your identity.
                    </Typography>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                        This information is encrypted.
                    </Typography>
                </Box>

                <Stack spacing={2.5}>
                    <TextField
                        fullWidth
                        label="Date of Birth"
                        value={formData.dob}
                        onChange={handleDobChange}
                        onKeyDown={handleDobKeyDown}
                        error={!!errors.dob}
                        helperText={errors.dob}
                        placeholder="MM/DD/YYYY"
                        slotProps={{inputLabel: {shrink: true}}}
                    />

                    <TextField
                        fullWidth
                        type="password"
                        label="SSN or ITIN"
                        value={formData.taxId}
                        onChange={handleSsnChange}
                        error={!!errors.taxId}
                        helperText={errors.taxId}
                        slotProps={{
                            inputLabel: {shrink: true},
                            input: {
                                autoComplete: 'off',
                            },
                        }}
                    />
                </Stack>

                <Box sx={{flex: 1}} />

                {renderError()}

                <Grid2 container gap={1} flexDirection="column" sx={{mt: 'auto'}}>
                    <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        fullWidth
                        disabled={
                            !formData.dob ||
                            !formData.taxId ||
                            !Object.entries(errors).every(([, value]) => value === undefined)
                        }
                    >
                        NEXT
                    </Button>
                    <Button variant="text" fullWidth startIcon={<ArrowBackIcon />} onClick={onPrevious}>
                        Previous Page
                    </Button>
                </Grid2>
            </Stack>
        </Box>
    )
}
