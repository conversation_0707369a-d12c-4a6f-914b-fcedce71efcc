import {useState, type ChangeEvent, type FormEvent} from 'react'
import {Box, Typography, TextField, Button, Stack, Grid2, Checkbox} from '@mui/material'
import {z} from 'zod'

const emailSchema = z.string().email({
    message: 'Please enter a valid email address',
})

interface EmailAndPhoneInputProps {
    emailAddress: string
    phoneNumber: string
    submissionError?: string
    onNext: (email: string, phone: string) => Promise<void>
}

const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const numbers = value.replace(/\D/g, '')

    // Format the number as user types
    if (numbers.length <= 3) {
        return numbers
    } else if (numbers.length <= 6) {
        return `(${numbers.slice(0, 3)}) ${numbers.slice(3)}`
    } else {
        return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6, 10)}`
    }
}

export default function EmailAndPhoneInput({
    emailAddress,
    phoneNumber,
    submissionError,
    onNext,
}: EmailAndPhoneInputProps) {
    const [email, setEmail] = useState(emailAddress)
    const [internalPhone, setInternalPhone] = useState(phoneNumber)
    const [phone, setPhone] = useState(formatPhoneNumber(phoneNumber))
    const [emailError, setEmailError] = useState<string | null>(null)
    const [phoneError, setPhoneError] = useState<string | null>(null)
    const [acknowledgedCharges, setAcknowledgedCharges] = useState(true)
    const [isSubmitting, setIsSubmitting] = useState(false)

    const validateEmail = (value: string) => {
        try {
            emailSchema.parse(value)
            setEmailError(null)
            return true
        } catch (err) {
            if (value) {
                setEmailError('Please enter a valid email address')
            } else {
                setEmailError(null)
            }
            return false
        }
    }

    const validatePhone = (value: string) => {
        try {
            const isValid = value.length === 10 && /^\d+$/.test(value)
            if (isValid) {
                setPhoneError(null)
                return true
            } else {
                setPhoneError('Please enter a valid phone number')
                return false
            }
        } catch (err) {
            if (value) {
                setPhoneError('Please enter a valid phone number')
            } else {
                setPhoneError(null)
            }
            return false
        }
    }

    const handleEmailChange = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value
        setEmail(value)
        validateEmail(value)
    }

    const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const fieldValue = event.target.value
        const rawValue = fieldValue.replace(/\D/g, '') // Remove all non-numeric characters
        setInternalPhone(rawValue)
        const formattedPhone = formatPhoneNumber(event.target.value)
        setPhone(formattedPhone)
        validatePhone(rawValue)
    }

    const handleSubmit = async (event: FormEvent) => {
        event.preventDefault()
        if (validateEmail(email)) {
            setIsSubmitting(true)
            await onNext(email, internalPhone)
            setIsSubmitting(false)
        }
    }

    const renderError = () => {
        if (submissionError) {
            return (
                <Typography variant="body2" color="error" gutterBottom>
                    {submissionError}
                </Typography>
            )
        }
        return null
    }

    return (
        <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            }}
        >
            <Stack spacing={3} sx={{flex: 1, display: 'flex', flexDirection: 'column'}}>
                <Box>
                    <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                        Your Info
                    </Typography>

                    <Typography variant="subtitle1" color="textSecondary" fontSize={20} fontWeight={700} gutterBottom>
                        Tell us about yourself.
                    </Typography>

                    <Typography variant="body2" color="textSecondary" fontSize={16} gutterBottom>
                        Provide your email address and telephone number so we can authenticate you.
                    </Typography>
                </Box>

                <TextField
                    fullWidth
                    label="Email Address"
                    variant="outlined"
                    value={email}
                    onChange={handleEmailChange}
                    error={!!emailError}
                    helperText={emailError}
                    autoFocus
                />

                <TextField
                    fullWidth
                    label="Phone number"
                    value={phone}
                    onChange={handlePhoneChange}
                    error={!!phoneError}
                    helperText={phoneError}
                    placeholder="(*************"
                />

                <Stack direction="row" spacing={2} alignItems={'center'} sx={{mb: 2}}>
                    <Checkbox
                        checked={acknowledgedCharges}
                        onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                            setAcknowledgedCharges(event.target.checked)
                        }
                    ></Checkbox>
                    <Typography color="textSecondary">
                        I am giving Tallied permission to use text messaging, artificial or prerecorded voice messages
                        and automatic dialing technology for informational and account service calls. Message and data
                        rates may apply.
                    </Typography>
                </Stack>

                {/* Spacer */}
                <Box sx={{flex: 1}} />

                {renderError()}

                <Grid2 container gap={1} flexDirection="column" sx={{mt: 'auto'}}>
                    <Typography color="textSecondary" fontSize={12}>
                        Click “NEXT” below and enter the verification code we sent to the telephone number you provided.
                    </Typography>

                    <Button
                        variant="contained"
                        fullWidth
                        type="submit"
                        size="large"
                        disabled={
                            !email || !!emailError || !phone || !!phoneError || !acknowledgedCharges || isSubmitting
                        }
                        loading={isSubmitting}
                    >
                        NEXT
                    </Button>
                </Grid2>
            </Stack>
        </Box>
    )
}
