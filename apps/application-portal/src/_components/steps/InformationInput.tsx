import {useState, type ChangeEvent, type FormEvent} from 'react'
import {Box, Button, Typography, Stack, TextField, MenuItem, Link, Grid2} from '@mui/material'
import {z} from 'zod'
import {stateNames} from '@/_staticData'

const informationSchema = z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    address_1: z.string().min(1, 'Street address is required'),
    address_2: z.string().min(0),
    zipCode: z.string().regex(/^\d{5}$/, 'Valid ZIP code is required'),
    city: z.string().min(1, 'City is required'),
    state: z.enum(stateNames, {errorMap: () => ({message: 'State is required'})}),
})

type InformationSchema = z.infer<typeof informationSchema>

interface InformationInputProps {
    onNext: (data: InformationSchema) => void
    firstName: string
    lastName: string
    address_1: string
    address_2: string
    zipCode: string
    city: string
    state: (typeof stateNames)[number]
}

export default function InformationInput({
    onNext,
    firstName,
    lastName,
    address_1,
    address_2,
    zipCode,
    city,
    state,
}: InformationInputProps) {
    const [formData, setFormData] = useState<InformationSchema>({
        firstName,
        lastName,
        address_1,
        address_2,
        zipCode,
        city,
        state,
    })

    const [errors, setErrors] = useState<Partial<Record<keyof InformationSchema, string>>>({})

    const handleChange = (field: keyof InformationSchema) => (event: ChangeEvent<HTMLInputElement>) => {
        const newValue = event.target.value
        setFormData(prev => ({
            ...prev,
            [field]: newValue,
        }))

        try {
            const fieldValidation = informationSchema.shape[field]
            fieldValidation.parse(newValue)

            setErrors(prev => ({
                ...prev,
                [field]: undefined,
            }))
        } catch (error) {
            if (error instanceof z.ZodError) {
                setErrors(prev => ({
                    ...prev,
                    [field]: error.errors[0]?.message,
                }))
            }
        }
    }

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault()
        try {
            const validatedData = informationSchema.parse(formData)
            onNext(validatedData)
        } catch (error) {
            if (error instanceof z.ZodError) {
                const newErrors: Partial<Record<keyof InformationSchema, string>> = {}
                error.errors.forEach(err => {
                    if (err.path[0]) {
                        newErrors[err.path[0] as keyof InformationSchema] = err.message
                    }
                })
                setErrors(newErrors)
            }
        }
    }

    return (
        <Box
            component="form"
            onSubmit={handleSubmit}
            mx="auto"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            }}
        >
            <Stack spacing={3} sx={{flex: 1}}>
                <Box>
                    <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                        Your Info
                    </Typography>
                    <Typography variant="body1" color="textSecondary" gutterBottom>
                        Tell us about yourself.
                    </Typography>
                </Box>

                <Stack spacing={2.5}>
                    <TextField
                        fullWidth
                        label="Legal First Name"
                        value={formData.firstName}
                        onChange={handleChange('firstName')}
                        error={!!errors.firstName}
                        helperText={errors.firstName}
                        slotProps={{
                            inputLabel: {
                                shrink: true,
                            },
                        }}
                        sx={{'& .MuiInputLabel-root': {color: 'text.secondary'}}}
                        autoFocus
                    />

                    <TextField
                        fullWidth
                        label="Legal Last Name"
                        value={formData.lastName}
                        onChange={handleChange('lastName')}
                        error={!!errors.lastName}
                        helperText={errors.lastName}
                        slotProps={{
                            inputLabel: {
                                shrink: true,
                            },
                        }}
                        sx={{'& .MuiInputLabel-root': {color: 'text.secondary'}}}
                    />

                    <TextField
                        fullWidth
                        label="Street Address"
                        value={formData.address_1}
                        onChange={handleChange('address_1')}
                        error={!!errors.address_1}
                        helperText={errors.address_1}
                        slotProps={{
                            inputLabel: {
                                shrink: true,
                            },
                        }}
                        sx={{'& .MuiInputLabel-root': {color: 'text.secondary'}}}
                    />

                    <Stack direction="row" spacing={2}>
                        <TextField
                            label="Apt, Ste, Fl"
                            value={formData.address_2}
                            onChange={handleChange('address_2')}
                            slotProps={{
                                inputLabel: {
                                    shrink: true,
                                },
                            }}
                            sx={{
                                width: '50%',
                                '& .MuiInputLabel-root': {color: 'text.secondary'},
                            }}
                        />
                        <TextField
                            label="Zip Code"
                            value={formData.zipCode}
                            onChange={handleChange('zipCode')}
                            error={!!errors.zipCode}
                            helperText={errors.zipCode}
                            slotProps={{
                                inputLabel: {
                                    shrink: true,
                                },
                            }}
                            sx={{
                                width: '50%',
                                '& .MuiInputLabel-root': {color: 'text.secondary'},
                            }}
                        />
                    </Stack>

                    <Stack direction="row" spacing={2}>
                        <TextField
                            fullWidth
                            label="City"
                            value={formData.city}
                            onChange={handleChange('city')}
                            error={!!errors.city}
                            helperText={errors.city}
                            slotProps={{
                                inputLabel: {
                                    shrink: true,
                                },
                            }}
                            sx={{'& .MuiInputLabel-root': {color: 'text.secondary'}}}
                        />
                        <TextField
                            select
                            label="State"
                            value={formData.state}
                            onChange={handleChange('state')}
                            error={!!errors.state}
                            helperText={errors.state}
                            slotProps={{
                                inputLabel: {
                                    shrink: true,
                                },
                            }}
                            sx={{
                                width: '120px',
                                '& .MuiInputLabel-root': {color: 'text.secondary'},
                            }}
                        >
                            {stateNames.map(option => (
                                <MenuItem key={option} value={option}>
                                    {option}
                                </MenuItem>
                            ))}
                        </TextField>
                    </Stack>
                </Stack>

                <Box sx={{flex: 1}} />

                <Grid2 container gap={1} flexDirection="column" sx={{mt: 'auto'}}>
                    <Typography color="textSecondary">
                        When you request a credit card as an authorized user, we will ask for your name, address, date
                        of birth, and other information that will allow us to verify identify your identity. We may also
                        ask to see your driver&apos;s license or other identifying documents. This information only will
                        be used in accordance with the&nbsp;
                        <Link href="/assets/patriot-act.pdf" target="_blank" color="primary">
                            USA PATRIOT Act
                        </Link>
                        .
                    </Typography>
                    <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        fullWidth
                        disabled={
                            !Object.entries(errors).every(([, value]) => value === undefined) ||
                            !Object.entries(formData).every(([key, value]) =>
                                key === 'address_2' ? true : Boolean(value),
                            )
                        }
                    >
                        NEXT
                    </Button>
                </Grid2>
            </Stack>
        </Box>
    )
}
