'use client'

import {Box, Grid2} from '@mui/material'
import Stepper from '@/_components/Stepper'
import EmailAndPhoneInput from '@/_components/steps/EmailAndPhoneInput'
import VerificationInput from '@/_components/steps/VerificationInput'
import InformationInput from '@/_components/steps/InformationInput'
import PersonalInfoInput from '@/_components/steps/PersonalInfoInput'
import {useReducer, useState} from 'react'
import {ApplicationState, ApplicationDecision} from '@/types/application'
import LoadingScreen from '@/_components/states/LoadingScreen'
import ApprovalScreen from '@/_components/states/ApprovalScreen'
import Header from '@/_components/whitelabelling/Header'
import Footer from '@/_components/whitelabelling/Footer'

import {stateNames} from '@/_staticData'
import ImportantInformationInput from '@/_components/steps/ImportantInformationInput'
import {type Promotion} from '@/services/controllers/acquisition'
import {createOtp, verifyOtp} from '@/actions/otp'
import {
    beginApplication,
    pollForCompletedApplicationStatus,
    processApplication,
    submitApplication,
} from '@/actions/acquisition'
import AlternateFinalScreen from './states/AlternateFinalScreen'

// Define types for each page's form data
type Disclosure = {
    id: string
    checked: boolean
    links: Array<{text: string; href: string; target?: string; rel?: string}>
}
type Page1Data = {disclosures: Disclosure[]}
type EmailAndPhoneSubmissionData = {emailAddress: string; phoneNumber: string; submissionError?: string}
type PhoneVerificationSubmissionData = {verificationCode: string; submissionError?: string}
type Page4Data = {
    firstName: string
    lastName: string
    address_1: string
    address_2: string
    zipCode: string
    city: string
    state: (typeof stateNames)[number]
}
type SensitiveInfoSubmissionData = {dob: string; taxId: string; submissionError?: string}

// Combined type for all pages
type FormPages = [
    Page1Data,
    EmailAndPhoneSubmissionData,
    PhoneVerificationSubmissionData,
    Page4Data,
    SensitiveInfoSubmissionData,
]

// Define the state interface
interface FormState {
    currentPage: number
    pages: FormPages
    meta: Record<string, string>
}

// Define action types
type FormAction =
    | {type: 'NEXT_PAGE'}
    | {type: 'PREV_PAGE'}
    | {type: 'UPDATE_PAGE_DATA'; pageIndex: number; data: Partial<FormPages[number]>}

// Add the disclosure configuration
const DISCLOSURE_CONFIG: Omit<Disclosure, 'checked'>[] = [
    {
        id: 'electronic',
        links: [
            {
                text: 'Electronic Communications Disclosure',
                href: 'https://docs.internal-consumer.program.preprod.tallied.io/electronic_communications_disclosure_and_agreement_tallied.pdf',
                target: '_blank',
            },
        ],
    },
    {
        id: 'privacy',
        links: [
            {text: "Tallied's Privacy Policy", href: 'https://www.tallied.io/legals/privacy-policy', target: '_blank'},
            {
                text: 'Cottonwood Payments Privacy Policy',
                href: 'https://********.fs1.hubspotusercontent-na1.net/hubfs/********/FinWise%20Bank%20dba%20Cottonwood%20Payments%20Privacy%20Notice%20_%2001-2025.pdf',
                target: '_blank',
            },
            {text: 'Terms of Service', href: 'https://www.tallied.io/legals/terms-of-service', target: '_blank'},
            {
                text: 'TCPA Consent',
                href: 'https://docs.internal-consumer.program.preprod.tallied.io/tcpa_consent.pdf',
                target: '_blank',
            },
        ],
    },
]

// Initial state
const initialState: FormState = {
    currentPage: 1,
    pages: [
        {disclosures: DISCLOSURE_CONFIG.map(config => ({...config, checked: true}))},
        {emailAddress: '', phoneNumber: ''},
        {verificationCode: ''},
        {firstName: '', lastName: '', address_1: '', address_2: '', zipCode: '', city: '', state: 'AA'},
        {dob: '', taxId: ''},
    ],
    meta: {},
}

// Reducer function
function formReducer(state: FormState, action: FormAction): FormState {
    switch (action.type) {
        case 'NEXT_PAGE':
            return {...state, currentPage: Math.min(state.currentPage + 1, 6)}
        case 'PREV_PAGE':
            return {...state, currentPage: Math.max(state.currentPage - 1, 1)}
        case 'UPDATE_PAGE_DATA': {
            const updatedPages = [...state.pages]
            updatedPages[action.pageIndex] = {...updatedPages[action.pageIndex], ...action.data}
            return {...state, pages: updatedPages as FormPages}
        }
        default:
            return state
    }
}

function generateSubmissionError(preface: string, returnedError?: string) {
    if (!returnedError || returnedError === '') {
        return preface
    }
    return `${preface}: ${returnedError}`
}

export default function ApplicationFlow(props: {promotion: Promotion}) {
    const [state, dispatch] = useReducer(formReducer, initialState)
    const [applicationState, setApplicationState] = useState<ApplicationState>(ApplicationState.STEPS)
    const [applicationDecision, setApplicationDecision] = useState<ApplicationDecision | null>(null)

    const handlePrevious = () => {
        dispatch({type: 'PREV_PAGE'})
    }

    const handlePhoneAndEmailSubmit = async (email: string, phoneNumber: string) => {
        dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 1, data: {emailAddress: email, phoneNumber: phoneNumber}})
        // Only proceed to the next page if we successfully sent the verification code!
        // NOTE - Twilio requires 12 digit phone numbers, append '+1'
        const result = await createOtp(`+1${phoneNumber}`)
        if (result.success) {
            dispatch({type: 'NEXT_PAGE'})
        } else {
            dispatch({
                type: 'UPDATE_PAGE_DATA',
                pageIndex: 1,
                data: {
                    emailAddress: email,
                    phoneNumber: phoneNumber,
                    submissionError: generateSubmissionError('Unable to send verification code', result.error),
                },
            })
            return
        }
    }

    const handleResendCode = async () => {
        // NOTE - Twilio requires 12 digit phone numbers, append '+1'
        const phoneNumber = `+1${state.pages[1].phoneNumber}`
        const result = await createOtp(phoneNumber)
        if (!result.success) {
            dispatch({
                type: 'UPDATE_PAGE_DATA',
                pageIndex: 2,
                data: {submissionError: generateSubmissionError('Unable to re-send verification code', result.error)},
            })
            return
        }
    }

    const handleVerificationSubmit = async (code: string) => {
        dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 2, data: {verificationCode: code}})
        const verificationResult = await verifyOtp(code)
        if (verificationResult.success) {
            dispatch({type: 'NEXT_PAGE'})
        } else {
            dispatch({
                type: 'UPDATE_PAGE_DATA',
                pageIndex: 2,
                data: {
                    verificationCode: code,
                    submissionError: generateSubmissionError('Unable to verify code', verificationResult.error),
                },
            })
            return
        }
    }

    const handleInformationSubmit = (data: Page4Data) => {
        dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 3, data})
        dispatch({type: 'NEXT_PAGE'})
    }

    const handlePersonalInfoSubmit = async (data: SensitiveInfoSubmissionData) => {
        dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 4, data: data})
        try {
            // NOTE - our API _also_ requires proper fully formatted phone numbers
            const phoneNumber = `+1${state.pages[1].phoneNumber}`
            // Send and initiate the application process, only proceed if we are successful.
            const applicationResult = await submitApplication(props.promotion.promotionId, {
                email: state.pages[1].emailAddress,
                primaryPhoneNumber: phoneNumber,
                firstName: state.pages[3].firstName,
                lastName: state.pages[3].lastName,
                addressLine1: state.pages[3].address_1,
                addressLine2: state.pages[3].address_2,
                postalCode: state.pages[3].zipCode,
                city: state.pages[3].city,
                state: state.pages[3].state,
                country: 'USA', // NOTE - defaulting to USA, uses ISO 3166-1
                dateOfBirth: data.dob,
                ssn: data.taxId,
            })
            if (applicationResult.success) {
                // start the actual approval process
                const initiationResult = await processApplication(props.promotion.promotionId)
                if (!initiationResult.success) {
                    data.submissionError = generateSubmissionError(
                        'Unable to process application',
                        initiationResult.error,
                    )
                    dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 4, data: data})
                    return
                }
            } else {
                data.submissionError = generateSubmissionError('Unable to submit application', applicationResult.error)
                dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 4, data: data})
                return
            }

            dispatch({type: 'NEXT_PAGE'})
            setApplicationState(ApplicationState.LOADING)

            const applicationResultResp = await pollForCompletedApplicationStatus(props.promotion.promotionId)
            if (!applicationResultResp.success) {
                console.log('TODO - handle non-happy path - unable to get application result in time')
                throw new Error('TODO!')
            }

            const creditLimit = applicationResultResp.data.decision?.modelOutput?.creditLimit?.amount
            const apr = applicationResultResp.data.decision?.purchaseApr
            if (creditLimit === undefined || apr === undefined) {
                console.log('TODO - handle non-happy path - missing credit limit or apr')
                throw new Error('TODO!')
            }

            const applicationDecision: ApplicationDecision = {
                status: 'APPROVED',
                creditLimit: creditLimit,
                apr: apr / 100.0,
            }
            setApplicationDecision(applicationDecision)
            setApplicationState(
                applicationDecision.status === 'APPROVED' ? ApplicationState.APPROVED : ApplicationState.DECLINED,
            )
        } catch (error) {
            setApplicationState(ApplicationState.DECLINED)
            setApplicationDecision(null)
        }
    }

    const handleImportantInformationSubmit = async (data: Page1Data) => {
        await beginApplication()
        dispatch({type: 'UPDATE_PAGE_DATA', pageIndex: 0, data})
        dispatch({type: 'NEXT_PAGE'})
    }

    if (applicationState === ApplicationState.LOADING) {
        return <LoadingScreen />
    }

    let finalPageComponent = undefined
    if (applicationState === ApplicationState.APPROVED && applicationDecision) {
        finalPageComponent = (
            <ApprovalScreen applicationDecision={applicationDecision} loginDomain={props.promotion.hostedDomain} />
        )
    } else if (
        applicationState === ApplicationState.DECLINED ||
        (applicationState === ApplicationState.APPROVED && !applicationDecision)
    ) {
        finalPageComponent = <AlternateFinalScreen />
    }

    if (finalPageComponent !== undefined) {
        return (
            <Box sx={{display: 'flex', flexDirection: 'column', flex: 1, height: '100vh'}}>
                <Grid2 container wrap="nowrap" alignSelf="stretch" flex={1} flexDirection="column" alignItems="center">
                    <Header />
                    <Box sx={{display: 'flex', flexDirection: 'column'}}>{finalPageComponent}</Box>
                    <Footer />
                </Grid2>
            </Box>
        )
    }

    return (
        <Box sx={{display: 'flex', flexDirection: 'column', flex: 1, height: '100vh'}}>
            <Grid2 container wrap="nowrap" alignSelf="stretch" flex={1} flexDirection="column" alignItems="center">
                <Header />
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        maxWidth: 800,
                        width: '100%',
                        margin: '0 auto',
                        flex: 1,
                    }}
                >
                    <Box p={2} sx={{flex: 1}}>
                        <Stepper total={state.pages.length} active={state.currentPage} />
                        {state.currentPage === 1 && (
                            <ImportantInformationInput onNext={handleImportantInformationSubmit} {...state.pages[0]} />
                        )}
                        {state.currentPage === 2 && (
                            <EmailAndPhoneInput
                                onNext={handlePhoneAndEmailSubmit}
                                emailAddress={state.pages[1].emailAddress}
                                phoneNumber={state.pages[1].phoneNumber}
                                submissionError={state.pages[1].submissionError}
                            />
                        )}
                        {state.currentPage === 3 && (
                            <VerificationInput
                                phoneNumber={state.pages[1].phoneNumber}
                                onNext={handleVerificationSubmit}
                                onPrevious={handlePrevious}
                                onResendCode={handleResendCode}
                                submissionError={state.pages[2].submissionError}
                            />
                        )}
                        {state.currentPage === 4 && (
                            <InformationInput onNext={handleInformationSubmit} {...state.pages[3]} />
                        )}
                        {state.currentPage === 5 && (
                            <PersonalInfoInput
                                onNext={handlePersonalInfoSubmit}
                                onPrevious={handlePrevious}
                                submissionError={state.pages[4].submissionError}
                                {...state.pages[4]}
                            />
                        )}
                    </Box>
                </Box>
                <Footer />
            </Grid2>
        </Box>
    )
}
