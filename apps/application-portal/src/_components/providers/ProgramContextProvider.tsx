'use client'

import React, {createContext, useContext} from 'react'
import {CssBaseline, ThemeProvider as MUIThemeProvider} from '@mui/material'
import {getTheme} from '@/theme'
import {WhitelabelConfig} from '@/whitelabelling/whitelabelling'

interface ProgramContextValue {
    themeName: string
    whitelabelConfig: WhitelabelConfig
}

const ProgramContext = createContext<ProgramContextValue | null>(null)

export function useProgramContext() {
    const context = useContext(ProgramContext)
    if (!context) {
        throw new Error('useProgramTheme must be used within ProgramThemeProvider')
    }
    return context
}

interface ProgramContextProviderProps {
    themeName: string
    whitelabelConfig: WhitelabelConfig
    children: React.ReactNode
}

export default function ProgramContextProvider({themeName, whitelabelConfig, children}: ProgramContextProviderProps) {
    const theme = getTheme(themeName)

    return (
        <ProgramContext.Provider value={{themeName, whitelabelConfig: whitelabelConfig}}>
            <MUIThemeProvider theme={theme}>
                <CssBaseline />
                {children}
            </MUIThemeProvider>
        </ProgramContext.Provider>
    )
}
