import {Box, Typography, Grid2} from '@mui/material'

export default function AlternateFinalScreen() {
    return (
        <Box sx={{p: 3, maxWidth: '600px', mx: 'auto'}}>
            <Box sx={{mb: 4}}>
                <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                    Information Received
                </Typography>
            </Box>

            <Grid2 container gap={2} flexDirection="column" sx={{mt: 'auto'}}>
                <Box
                    sx={{
                        bgcolor: 'var(--mui-palette-primary-light)',
                        p: 3,
                        borderRadius: 3,
                    }}
                >
                    <Typography variant="body1" sx={{color: '#605D64'}}>
                        Thank you for your interest; you will receive an email shortly.
                    </Typography>
                </Box>
            </Grid2>
        </Box>
    )
}
