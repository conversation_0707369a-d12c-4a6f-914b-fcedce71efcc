'use client'

import {<PERSON>, Typo<PERSON>, Grid2, <PERSON><PERSON>, Link, <PERSON>ack} from '@mui/material'
import Image from 'next/image'
import {ApplicationState, ApplicationDecision} from '@/types/application'
import {formatCurrency} from '@tallied-technologies/common'
import {useEffect, useState} from 'react'

type ApprovalScreenProps = {
    applicationDecision: ApplicationDecision
    loginDomain?: string
}

export default function ApprovalScreen({applicationDecision, loginDomain}: ApprovalScreenProps) {
    const [loginUrl, setLoginUrl] = useState<string>(`https://${loginDomain}/login`)

    useEffect(() => {
        // has to be done in a useEffect, otherwise `window` is undefined
        // https://nextjs-faq.com/browser-api-client-component
        const usingHttps = typeof window !== 'undefined' && window.location.protocol === 'https:'
        if (!usingHttps) {
            setLoginUrl(`http://${loginDomain}/login`)
        }
    }, [])

    return (
        <Box sx={{p: 3, maxWidth: '600px', mx: 'auto'}}>
            <Box sx={{mb: 4}}>
                <Typography variant="h4" component="h1" gutterBottom color="primary" fontSize={32} fontWeight={700}>
                    Congratulations!
                </Typography>
                <Typography variant="body1" color="textSecondary" fontSize={20} fontWeight={700} gutterBottom>
                    Your Tallied Internal Mastercard Card will be sent to your address in 7-10 business days
                </Typography>
            </Box>

            <Grid2 container spacing={2} sx={{mb: 4, px: 5}}>
                <Grid2 flex={1}>
                    <Box>
                        <Box sx={{mb: 3}}>
                            <Typography
                                variant="body1"
                                color="textSecondary"
                                fontSize={16}
                                fontWeight={400}
                                sx={{mb: 1}}
                            >
                                Credit Limit
                            </Typography>
                            <Typography variant="h4" fontSize={20} fontWeight={600}>
                                {formatCurrency(applicationDecision.creditLimit / 100)}
                            </Typography>
                        </Box>

                        <Box sx={{mb: 3}}>
                            <Typography
                                variant="body1"
                                color="textSecondary"
                                fontSize={16}
                                fontWeight={400}
                                sx={{mb: 1}}
                            >
                                APR
                            </Typography>
                            <Typography variant="h4" fontSize={20} fontWeight={600}>
                                {applicationDecision.apr}% APR
                            </Typography>
                        </Box>

                        <Box>
                            <Typography
                                variant="body1"
                                color="textSecondary"
                                fontSize={16}
                                fontWeight={400}
                                sx={{mb: 1}}
                            >
                                Annual Fee
                            </Typography>
                            <Typography variant="h4" fontSize={20} fontWeight={600}>
                                $0.00
                            </Typography>
                        </Box>
                    </Box>
                </Grid2>
                <Grid2 flex={1} sx={{display: 'flex', alignItems: 'flex-start', position: 'relative'}}>
                    <Image src="/assets/Card.png" alt="Tallied Card" fill={true} objectFit="contain" priority />
                    <Typography variant="caption">Not your assigned card number</Typography>
                </Grid2>
            </Grid2>

            <Grid2 container gap={2} flexDirection="column" sx={{mt: 'auto'}}>
                <Box
                    sx={{
                        bgcolor: 'var(--mui-palette-primary-light)',
                        p: 3,
                        borderRadius: 3,
                    }}
                >
                    <Typography variant="body1" sx={{color: '#605D64'}}>
                        You will receive an email verification from us. Once you verify your email, you can start using
                        your virtual card right away!
                    </Typography>
                </Box>
                {loginDomain && (
                    <Button
                        component={Link}
                        href={loginUrl}
                        variant="contained"
                        size="large"
                        fullWidth
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        LOGIN NOW
                    </Button>
                )}
            </Grid2>
        </Box>
    )
}
