'use server'

import {randomUUID} from 'crypto'
import {createSuccessResult, createErrorResult, handleActionError, type Result} from './types'
import {Configuration, DefaultApi} from '@/services/controllers/otp'
import {getSessionData, updateSessionData} from '@/lib/cookieSession'
import {decorateApi} from '@/services'
import {APIError} from '@/services/APIError'
import {getDomainDiscoveryHeaders} from './domainDiscovery'

async function getApiContext() {
    const discovery = await getDomainDiscoveryHeaders()
    return {
        api: new DefaultApi(new Configuration({basePath: discovery?.apiHostname})),
    }
}

export async function createOtp(phoneNumber: string): Promise<Result<string>> {
    try {
        const {api} = await getApiContext()

        const resp = await decorateApi(api.postOtp.bind(api), {
            idempotencyKey: randomUUID(),
            postOtpRequest: {
                phoneNumber: phoneNumber,
            },
        })
        if (resp.otpId) {
            await updateSessionData({otpId: resp.otpId})
            return createSuccessResult(resp.otpId)
        }
        return createErrorResult('No OTP ID returned from API')
    } catch (error) {
        if (APIError.isAPIError(error) && error.isBadRequest()) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function verifyOtp(code: string): Promise<Result<boolean>> {
    try {
        const {api} = await getApiContext()

        const sessionData = await getSessionData()
        if (!sessionData.otpId) {
            return createErrorResult('No OTP ID found in session')
        }
        await decorateApi(api.postOtpOtpId.bind(api), {
            idempotencyKey: randomUUID(),
            otpId: sessionData.otpId,
            requestOtpIdModel: {
                code: code,
            },
        })
        await updateSessionData({phoneVerified: true})
        return createSuccessResult(true)
    } catch (error) {
        return handleActionError(error)
    }
}
