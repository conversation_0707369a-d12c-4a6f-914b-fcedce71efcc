'use server'

import {getSessionData, updateSessionData, wipeSessionData} from '@/lib/cookieSession'
import {decorateApi} from '@/services'
import {Application, Promotion, QuestionIds, SubmissionStatus} from '@/services/controllers/acquisition'
import {randomUUID} from 'crypto'
import {createErrorResult, createSuc<PERSON>R<PERSON>ult, handleActionError, Result} from './types'
import {withErrorHandling, withRetry} from '@/services/apiWrapper'
import {Configuration, DefaultApi} from '@/services/controllers/acquisition'
import {getDomainDiscoveryHeaders} from './domainDiscovery'

async function getApiContext() {
    const discovery = await getDomainDiscoveryHeaders()
    return {
        api: new DefaultApi(new Configuration({basePath: discovery?.apiHostname})),
    }
}

export async function getPromotion(promotionId: string): Promise<Promotion> {
    const {api} = await getApiContext()

    return withErrorHandling(function fetchPromotion() {
        return decorateApi(api.getPromotionById.bind(api), {
            promotionId,
        })
    }, 'fetch promotion data')
}

export async function beginApplication(): Promise<void> {
    await wipeSessionData()
}

export interface ApplicationQuestionnaire {
    email: string
    primaryPhoneNumber: string
    firstName: string
    lastName: string
    addressLine1: string
    addressLine2: string
    postalCode: string
    city: string
    state: string
    country: string
    dateOfBirth: string
    ssn: string
}

export async function submitApplication(
    promotionId: string,
    questionnaireAnswers: ApplicationQuestionnaire,
): Promise<Result<boolean>> {
    try {
        const {api} = await getApiContext()

        const sessionData = await getSessionData()
        if (!sessionData.otpId) {
            return createErrorResult('No OTP ID found in session')
        }
        if (!sessionData.phoneVerified) {
            return createErrorResult('Phone was not verified')
        }
        const currentTime = new Date()
        const resp = await decorateApi(api.postApplication.bind(api), {
            promotionId,
            idempotencyKey: randomUUID(),
            applicationSubmissionModel: {
                questionnaire: [
                    {
                        questionId: QuestionIds.Email,
                        answer: questionnaireAnswers.email,
                    },
                    {
                        questionId: QuestionIds.PrimaryPhoneNumber,
                        answer: questionnaireAnswers.primaryPhoneNumber,
                    },
                    {
                        questionId: QuestionIds.FirstName,
                        answer: questionnaireAnswers.firstName,
                    },
                    {
                        questionId: QuestionIds.LastName,
                        answer: questionnaireAnswers.lastName,
                    },
                    {
                        questionId: QuestionIds.AddressLine1,
                        answer: questionnaireAnswers.addressLine1,
                    },
                    {
                        questionId: QuestionIds.AddressLine2,
                        answer: questionnaireAnswers.addressLine2,
                    },
                    {
                        questionId: QuestionIds.PostalCode,
                        answer: questionnaireAnswers.postalCode,
                    },
                    {
                        questionId: QuestionIds.City,
                        answer: questionnaireAnswers.city,
                    },
                    {
                        questionId: QuestionIds.State,
                        answer: questionnaireAnswers.state,
                    },
                    {
                        questionId: QuestionIds.Country,
                        answer: questionnaireAnswers.country,
                    },
                    {
                        questionId: QuestionIds.DateOfBirth,
                        answer: questionnaireAnswers.dateOfBirth,
                    },
                    {
                        questionId: QuestionIds.Ssn,
                        answer: questionnaireAnswers.ssn,
                    },
                ],
                agreements: {
                    // TODO - check that all of these things are actually checked, make future notes accordingly for how this API can be improved
                    esign: currentTime,
                    tcpa: currentTime,
                    privacyPolicy: currentTime,
                    patriotActNotice: currentTime,
                    cardHolderAgreement: currentTime,
                },
                metadata: {
                    promotionId: promotionId,
                },
            },
        })
        // TODO - handle there not being an applicationId
        await updateSessionData({applicationId: resp.applicationId})
        return createSuccessResult(true)
    } catch (error) {
        return handleActionError(error)
    }
}

export async function processApplication(promotionId: string): Promise<Result<boolean>> {
    try {
        const {api} = await getApiContext()

        const sessionData = await getSessionData()
        if (!sessionData.applicationId) {
            return createErrorResult('No Application ID found in session')
        }
        await decorateApi(api.postDecisions.bind(api), {
            promotionId,
            idempotencyKey: randomUUID(),
            decisionSubmissionModel: {
                applicationId: sessionData.applicationId,
            },
        })
        return createSuccessResult(true)
    } catch (error) {
        return handleActionError(error)
    }
}

export async function getApplicationStatus(promotionId: string): Promise<Result<Application | undefined>> {
    try {
        const {api} = await getApiContext()
        const sessionData = await getSessionData()
        if (!sessionData.applicationId) {
            return createErrorResult('No Application ID found in session')
        }

        const application = await decorateApi(api.getApplicationById.bind(api), {
            promotionId,
            applicationId: sessionData.applicationId,
        })

        return createSuccessResult(application)
    } catch (error) {
        return handleActionError(error)
    }
}

export async function pollForCompletedApplicationStatus(promotionId: string): Promise<Result<Application>> {
    try {
        const {api} = await getApiContext()

        const sessionData = await getSessionData()
        if (!sessionData.applicationId) {
            return createErrorResult('No Application ID found in session')
        }

        const resp = await withRetry(
            async () => {
                return decorateApi(api.getApplicationById.bind(api), {
                    promotionId,
                    applicationId: sessionData.applicationId,
                })
            },
            {
                maxRetries: 8,
                timeout: 60 * 1000,
                shouldRetry: (application, error) => {
                    if (
                        application?.status === undefined ||
                        application?.status === SubmissionStatus.Processing ||
                        application?.status === SubmissionStatus.Unprocessed
                    ) {
                        return true
                    }
                    return false
                },
            },
        )
        return createSuccessResult(resp)
    } catch (error) {
        return handleActionError(error)
    }
}
