import {APIError} from './APIError'

type Error<PERSON>andler = (error: APIError) => void | Promise<void>
type ErrorContext = string | ErrorHandler

type ApiFunction<T, Args extends any[]> = (...args: Args) => Promise<T>

// TODO - these common utilities should go somewhere common (duplicated across apps right now)

export async function withErrorHandling<T, <PERSON>rg<PERSON> extends any[]>(
    fn: ApiFunction<T, Args>,
    errorContext: ErrorContext,
    ...args: Args
): Promise<T> {
    try {
        const result = await fn(...args)
        return result
    } catch (error) {
        // Create APIError instance
        const apiError = new APIError(error)
        const errorMeta = {
            error,
            message: apiError.message,
            timestamp: new Date().toISOString(),
            operation: typeof errorContext === 'string' ? errorContext : 'execute operation',
            status: apiError.status,
            functionName: fn.name,
        }

        // Log the error with context
        console.error('API Operation Failed:', errorMeta)

        // If errorContext is a function, call it with the APIError
        if (typeof errorContext === 'function') {
            await Promise.resolve(errorContext(Object.assign(apiError, {meta: errorMeta})))
        }

        // Always throw the error after handling
        throw apiError
    }
}

/**
 * Retries a function on 500+ errors with exponential backoff, or based on a provided `shouldRetry` function.
 * @param fn - The function to retry
 * @param options - Retry options
 * @returns Promise resolving to the function's return value
 */
export async function withRetry<T>(
    fn: () => Promise<T>,
    options: {
        maxRetries?: number
        baseDelay?: number
        maxDelay?: number
        timeout?: number
        shouldRetry?: (resp?: T, error?: APIError) => boolean
    } = {},
): Promise<T> {
    const {maxRetries = 3, baseDelay = 1000, maxDelay = 10000, timeout = 30000} = options

    const startTime = Date.now()
    let retryCount = 0

    while (true) {
        const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay)
        try {
            const resp = await fn()
            if (options.shouldRetry && options.shouldRetry(resp, undefined)) {
                // allows us to retry on successful responses as well (ie. polling for a specific value)
                await new Promise(resolve => setTimeout(resolve, delay))
                retryCount++
                continue
            }
            return resp
        } catch (error) {
            if (retryCount >= maxRetries || Date.now() - startTime > timeout) {
                // conditions to always give up on
                throw error
            } else if (options.shouldRetry && APIError.isAPIError(error) && !options.shouldRetry(undefined, error)) {
                // if the custom shouldRetry function returns false
                throw error
            } else if (APIError.isAPIError(error) && error.status < 500) {
                // If the error isn't a 5XX (or above), give up as well
                throw error
            }
            await new Promise(resolve => setTimeout(resolve, delay))
            retryCount++
        }
    }
}
