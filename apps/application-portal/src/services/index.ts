import {APIError, ResponseError} from './APIError'

export class FetchError extends Error {
    override name: 'FetchError' = 'FetchError'
    constructor(
        public cause: Error,
        msg?: string,
    ) {
        super(msg)
    }
}

export const appendAuthHeader = async () => {
    const accessToken = process.env.GLOBAL_API_PAT

    return {
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
    }
}

export async function decorateApi<ParamArgs extends object, RequestOptions extends RequestInit, ReturnedType>(
    api: (params: ParamArgs, requestInit?: RequestInit) => Promise<ReturnedType>,
    params?: Partial<ParamArgs> & {
        idempotencyKey?: string
    },
    requestInit?: RequestOptions & {baseURL?: string},
) {
    const authHeader = await appendAuthHeader()

    const decoratedParams = {
        ...params,
    } as ParamArgs

    const decoratedRequestInit = {
        ...authHeader,
        ...requestInit,
        headers: {
            ...authHeader.headers,
            ...(requestInit?.headers || {}),
            'content-type': 'application/json',
        },
    }

    if (params?.idempotencyKey) {
        ;(decoratedRequestInit.headers as Record<string, string>)['Idempotency-Key'] = params.idempotencyKey
    }

    try {
        return await api(decoratedParams, decoratedRequestInit)
    } catch (error) {
        console.error('API call failed:', error)
        if (error instanceof ResponseError || (error && typeof error === 'object' && 'response' in error)) {
            throw new APIError(error)
        }
        throw error
    }
}
