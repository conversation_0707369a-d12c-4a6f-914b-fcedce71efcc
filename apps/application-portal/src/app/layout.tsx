import type {Metadata} from 'next'
import {AppRouterCacheProvider} from '@mui/material-nextjs/v15-appRouter'
import {getDomainDiscoveryFromHeaders} from '@tallied-technologies/common'
import {headers} from 'next/headers'
import ProgramContextProvider from '@/_components/providers/ProgramContextProvider'
import {getWhitelabelConfig} from '@/whitelabelling/whitelabelling'

export let metadata: Metadata = {
    title: 'Tallied Credit Application',
    description: 'For internal use only. Credit Application.',
}

function BaseLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang="en">
            <body>
                <AppRouterCacheProvider>{children}</AppRouterCacheProvider>
            </body>
        </html>
    )
}

export default async function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    const headersList = await headers()
    const domainDetails = await getDomainDiscoveryFromHeaders(headersList)
    const whitelabelConfig = await getWhitelabelConfig(domainDetails?.themeName ?? 'tallied')

    if (domainDetails === undefined) {
        // TODO - nicer UX
        return (
            <BaseLayout>
                <div>Something went wrong</div>
            </BaseLayout>
        )
    }

    return (
        <BaseLayout>
            <ProgramContextProvider
                themeName={domainDetails.themeName ?? 'tallied'}
                whitelabelConfig={whitelabelConfig}
            >
                {children}
            </ProgramContextProvider>
        </BaseLayout>
    )
}
