import ApplicationFlow from '@/_components/ApplicationFlow'
import GenericApplication from '@/_components/GenericApplication'
import {getPromotion} from '@/actions/acquisition'
import {getProgramFlow} from '@/flow-definitions/programFlow'
import {getDomainDiscoveryFromHeaders} from '@tallied-technologies/common'
import {headers} from 'next/headers'
import {Suspense} from 'react'

async function PromotionDetails({params}: {params: Promise<{promotionId: string}>}) {
  const headersList = await headers()
  const domainDetails = await getDomainDiscoveryFromHeaders(headersList)

  if (domainDetails === undefined) {
    // TODO - nicer UX
    return <div>Something went wrong</div>
  }

  const {promotionId} = await params
  let promotion
  // TODO - a nicer error page would be nice
  try {
    promotion = await getPromotion(promotionId)
  } catch (err) {
    return <div>Error fetching promotion</div>
  }
  if (!promotion || domainDetails?.themeName === undefined) {
    return <div>Promotion not found</div>
  }

  // Valid promotion
  if (domainDetails.themeName === undefined || domainDetails.themeName === 'tallied') {
    return <ApplicationFlow promotion={promotion} />
  }

  const programFlow = getProgramFlow(domainDetails.themeName)
  if (programFlow === undefined) {
    return <div>Promotion not configured</div>
  }
  return <GenericApplication promotion={promotion} />
}

export default async function Page({params}: {params: Promise<{promotionId: string}>}) {
  return (
    <div>
      <Suspense fallback={<div>Loading promotion...</div>}>
        <PromotionDetails params={params} />
      </Suspense>
    </div>
  )
}
