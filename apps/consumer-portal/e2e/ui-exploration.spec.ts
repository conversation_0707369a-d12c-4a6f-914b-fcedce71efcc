import { test, expect, Page } from '@playwright/test';
import { loginToPortal, VIEWPORTS, waitForPageStable, TIMEOUTS } from './test-utils';

interface UIElement {
  type: string;
  selector: string;
  text?: string;
  href?: string;
  page: string;
  visible: boolean;
  enabled: boolean;
}

interface ExplorationResult {
  page: string;
  url: string;
  elements: UIElement[];
  screenshots: string[];
}

async function exploreInteractiveElements(page: Page, pageName: string): Promise<UIElement[]> {
  const elements: UIElement[] = [];

  // Wait for page to stabilize
  await waitForPageStable(page, TIMEOUTS.medium);

  // Common selectors for interactive elements
  const selectors = [
    'a[href]',           // Links
    'button',            // Buttons
    '[role="button"]',   // Elements with button role
    '[role="tab"]',      // Tabs
    'input',             // Input fields
    'select',            // Select dropdowns
    '[onclick]',         // Elements with click handlers
    '[data-testid]',     // Test ID elements
    '.fa-',              // FontAwesome icons (often clickable)
    '[aria-label]',      // Accessible elements
    '[role="menuitem"]', // Menu items
    '[role="link"]',     // Link roles
  ];

  for (const selector of selectors) {
    try {
      const locators = page.locator(selector);
      const count = await locators.count();

      for (let i = 0; i < count; i++) {
        const element = locators.nth(i);

        try {
          const isVisible = await element.isVisible({ timeout: 1000 });
          const isEnabled = await element.isEnabled({ timeout: 1000 });

          if (isVisible) {
            const text = await element.textContent();
            const href = await element.getAttribute('href');
            const tagName = await element.evaluate(el => el.tagName.toLowerCase());
            const role = await element.getAttribute('role');
            const ariaLabel = await element.getAttribute('aria-label');
            const testId = await element.getAttribute('data-testid');

            // Determine element type
            let type = tagName;
            if (role) type = role;
            if (tagName === 'a') type = 'link';
            if (tagName === 'button') type = 'button';
            if (selector.includes('fa-')) type = 'icon';

            elements.push({
              type,
              selector: `${tagName}${testId ? `[data-testid="${testId}"]` : ''}${ariaLabel ? `[aria-label="${ariaLabel}"]` : ''}`,
              text: text?.trim() || ariaLabel || testId || '',
              href: href || undefined,
              page: pageName,
              visible: isVisible,
              enabled: isEnabled
            });
          }
        } catch (error) {
          // Skip elements that can't be evaluated
          continue;
        }
      }
    } catch (error) {
      // Skip selectors that fail
      continue;
    }
  }

  return elements;
}

async function exploreModalsAndDrawers(page: Page, pageName: string): Promise<UIElement[]> {
  const modalElements: UIElement[] = [];

  // Look for modal/drawer triggers
  const modalTriggers = [
    'text="My Linked Accounts"',
    'text="Settings"',
    'text="Make a Payment"',
    'text="Manage Cards"',
    'button:has-text("Get Cashback")',
    'button:has-text("Travel Rewards")',
    '.fa-credit-card',
    '.fa-cog',
    '.fa-gear',
    '.fa-settings'
  ];

  for (const trigger of modalTriggers) {
    try {
      const element = page.locator(trigger).first();
      if (await element.isVisible({ timeout: 2000 })) {
        // Try to click and see what opens
        await element.click();
        await page.waitForTimeout(1000);

        // Look for modal/drawer content
        const modalSelectors = [
          '[role="dialog"]',
          '.MuiDrawer-root',
          '.MuiModal-root',
          '.modal',
          '.drawer'
        ];

        for (const modalSelector of modalSelectors) {
          const modal = page.locator(modalSelector);
          if (await modal.isVisible({ timeout: 1000 })) {
            // Explore elements within the modal
            const modalContent = await exploreInteractiveElements(page, `${pageName}-modal`);
            modalElements.push(...modalContent);

            // Try to close the modal
            const closeButtons = [
              'button[aria-label*="close" i]',
              '.fa-xmark',
              'button:has(.fa-xmark)',
              'button:has-text("Cancel")',
              'button:has-text("Close")'
            ];

            for (const closeBtn of closeButtons) {
              const closeElement = page.locator(closeBtn);
              if (await closeElement.isVisible({ timeout: 1000 })) {
                await closeElement.click();
                await page.waitForTimeout(500);
                break;
              }
            }
            break;
          }
        }
      }
    } catch (error) {
      // Continue with next trigger
      continue;
    }
  }

  return modalElements;
}

test.describe('Comprehensive UI Exploration', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should explore home page elements', async ({ page }) => {
    console.log('🔍 Exploring Home/Dashboard page...');
    await page.goto('/');
    await waitForPageStable(page, TIMEOUTS.medium);

    const homeElements = await exploreInteractiveElements(page, 'home');
    await page.screenshot({ path: 'test-results/exploration-home.png', fullPage: true });

    console.log(`\n🔗 HOME PAGE`);
    console.log(`Found ${homeElements.length} interactive elements`);

    const visibleElements = homeElements.filter(el => el.visible && el.enabled);
    console.log('\nVisible Interactive Elements:');
    visibleElements.forEach(el => {
      const description = el.text || el.href || el.selector;
      console.log(`  • ${el.type}: ${description}`);
    });

    expect(visibleElements.length).toBeGreaterThan(5);
  });

  test('should explore payments page elements', async ({ page }) => {
    console.log('🔍 Exploring Payments page...');
    await page.goto('/payments');
    await waitForPageStable(page, TIMEOUTS.medium);

    const paymentsElements = await exploreInteractiveElements(page, 'payments');
    await page.screenshot({ path: 'test-results/exploration-payments.png', fullPage: true });

    console.log(`\n🔗 PAYMENTS PAGE`);
    console.log(`Found ${paymentsElements.length} interactive elements`);

    const visibleElements = paymentsElements.filter(el => el.visible && el.enabled);
    console.log('\nVisible Interactive Elements:');
    visibleElements.forEach(el => {
      const description = el.text || el.href || el.selector;
      console.log(`  • ${el.type}: ${description}`);
    });

    expect(visibleElements.length).toBeGreaterThan(5);
  });

  test('should explore rewards page elements', async ({ page }) => {
    console.log('🔍 Exploring Rewards page...');
    await page.goto('/rewards');
    await waitForPageStable(page, TIMEOUTS.medium);

    const rewardsElements = await exploreInteractiveElements(page, 'rewards');
    await page.screenshot({ path: 'test-results/exploration-rewards.png', fullPage: true });

    console.log(`\n🔗 REWARDS PAGE`);
    console.log(`Found ${rewardsElements.length} interactive elements`);

    const visibleElements = rewardsElements.filter(el => el.visible && el.enabled);
    console.log('\nVisible Interactive Elements:');
    visibleElements.forEach(el => {
      const description = el.text || el.href || el.selector;
      console.log(`  • ${el.type}: ${description}`);
    });

    expect(visibleElements.length).toBeGreaterThan(3);
  });
});
