import { test } from '@playwright/test';
import { loginToPortal, VIEWPORTS, switchToTab, verifyPdfLink, downloadAndVerifyPdfStructure, waitForPageStable } from './test-utils';

test.describe('PDF Content Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should verify statement PDF download and structure', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);

    await switchToTab(page, 'statements');

    // Look for statement PDF links with date information
    const hasValidStructure = await downloadAndVerifyPdfStructure(page);

    if (!hasValidStructure) {
      console.log('No statement PDF found or PDF structure verification failed');
    } else {
      console.log('✓ PDF structure verification completed successfully');
    }
  });

  test('should verify PDF links are present and accessible', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);

    await switchToTab(page, 'statements');

    // Look for statement PDF links
    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasValidPdfLinks = await verifyPdfLink(page, statementPdfSelector);

    if (!hasValidPdfLinks) {
      console.log('No PDF links found - test account may not have statements yet');
    }
  });
});
