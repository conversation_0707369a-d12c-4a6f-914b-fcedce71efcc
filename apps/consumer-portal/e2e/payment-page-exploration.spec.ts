import { test, expect } from '@playwright/test';
import { loginToPortal, VIEWPORTS, navigateToPage, waitForPageStable, TIMEOUTS } from './test-utils';

test.describe('Payment Page Exploration', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should explore payments page elements', async ({ page }) => {
    // Set shorter timeout for this exploration test
    test.setTimeout(15000); // 15 seconds max

    // Navigate to payments page
    await navigateToPage(page, 'payments');
    await waitForPageStable(page, TIMEOUTS.short); // Use shorter timeout

    console.log('🔍 Exploring Payments page...');

    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'test-results/payments-page-exploration.png', fullPage: true });

    // Try to find My Linked Accounts with improved selectors and shorter timeouts
    console.log('Looking for My Linked Accounts...');

    // More specific selectors based on actual component structure
    const selectors = [
      'p:text-is("My Linked Accounts")', // Exact text match
      'h6:text-is("My Linked Accounts")', // Header with exact text
      'button:has-text("My Linked Accounts")',
      '[aria-label*="My Linked Accounts"]',
      'text="My Linked Accounts" >> ..' // Parent of text element
    ];

    let foundElement = false;

    for (const selector of selectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();

        if (count > 0) {
          console.log(`Selector "${selector}" found ${count} elements`);

          // Try only the first few elements to avoid long loops
          const maxElements = Math.min(count, 3);

          for (let i = 0; i < maxElements; i++) {
            try {
              const element = elements.nth(i);
              const isVisible = await element.isVisible({ timeout: 2000 }); // Short timeout

              if (isVisible) {
                const text = await element.textContent();
                const tagName = await element.evaluate(el => el.tagName);
                console.log(`  Element ${i}: ${tagName} - visible: ${isVisible} - text: "${text?.trim()}"`);

                if (text?.includes('My Linked Accounts')) {
                  console.log(`  Attempting to click element ${i}...`);

                  try {
                    // Try to find a clickable parent if this element isn't clickable
                    let clickableElement = element;

                    // If it's just text, try to find a clickable parent
                    if (tagName === 'P' || tagName === 'H6' || tagName === 'SPAN') {
                      const parentButton = element.locator('xpath=ancestor::button[1]');
                      const parentDiv = element.locator('xpath=ancestor::div[@role="button" or contains(@class, "clickable") or @onclick][1]');

                      if (await parentButton.count() > 0) {
                        clickableElement = parentButton;
                        console.log('    Using parent button');
                      } else if (await parentDiv.count() > 0) {
                        clickableElement = parentDiv;
                        console.log('    Using parent div');
                      }
                    }

                    await clickableElement.click({ timeout: 3000 }); // Short click timeout
                    await page.waitForTimeout(1000); // Shorter wait

                    // Check if drawer opened with shorter timeout
                    const drawer = page.locator('[role="dialog"]:not([aria-label*="My Account"]):not([aria-label*="Documents"])').first();
                    const drawerVisible = await drawer.isVisible({ timeout: 2000 });
                    console.log(`  Drawer visible after click: ${drawerVisible}`);

                    if (drawerVisible) {
                      console.log('✓ Successfully opened My Linked Accounts drawer');
                      foundElement = true;
                      return; // Success!
                    }
                  } catch (clickError) {
                    console.log(`  Click attempt failed: ${clickError.message}`);
                    // Continue to next element
                  }
                }
              }
            } catch (elementError) {
              console.log(`  Element ${i} error: ${elementError.message}`);
              // Continue to next element
            }
          }
        }
      } catch (selectorError) {
        console.log(`Selector "${selector}" failed: ${selectorError.message}`);
        // Continue to next selector
      }
    }

    if (!foundElement) {
      console.log('ℹ Could not find or click My Linked Accounts element');
      // Don't fail the test, just log the issue
    }
  });
});
