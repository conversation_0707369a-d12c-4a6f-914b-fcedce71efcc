'use server'

import {validateSession} from '@/auth'
import {decorateApi} from '@/services'
import StatementApi, {type GetStatements200Response} from '@/services/controllers/statements'
import {withErrorHandling} from '@/services/apiWrapper'

export async function getStatements(): Promise<GetStatements200Response> {
    await validateSession()
    return withErrorHandling(function fetchStatements() {
        return decorateApi(StatementApi.getStatements)
    }, 'fetch statements')
}
