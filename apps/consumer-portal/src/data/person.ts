'use server'

import {validateSession} from '@/auth'
import {decorateApi} from '@/services'
import Person<PERSON><PERSON>, {type Person} from '@/services/controllers/person'
import {withErrorHandling} from '@/services/apiWrapper'

export async function getPersonDetails(): Promise<Person> {
    const {user} = await validateSession()

    return withErrorHandling(function fetchPersonDetails() {
        // Validate that user has a person
        if (!user.personId) {
            throw 'User does not have an associated person'
        }

        return decorateApi(PersonApi.retrievePerson, {personId: user.personId})
    }, 'fetch person data')
}
