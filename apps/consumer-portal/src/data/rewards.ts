'use server'

import {validateSession} from '@/auth'
import {decorateApi} from '@/services'
import RewardsApi, {type GetRewardsBalance200Response} from '@/services/controllers/rewards'
import {withErrorHandling} from '@/services/apiWrapper'

export async function getRewardsBalance(): Promise<GetRewardsBalance200Response> {
    await validateSession()
    return withErrorHandling(function fetchRewardsBalance() {
        return decorateApi(RewardsApi.getRewardsBalance)
    }, 'fetch rewards balance')
}
