'use server'

import {validateSession} from '@/auth'
import {decorateApi} from '@/services'
import type {
    GetTransactionEvents200Response,
    GetTransactionEventsRequest,
} from '@/services/controllers/transactionEvents'
import TransactionEventsApi from '@/services/controllers/transactionEvents'
import {withErrorHandling} from '@/services/apiWrapper'

export async function getTransactions(
    params?: Partial<GetTransactionEventsRequest>,
): Promise<GetTransactionEvents200Response> {
    await validateSession()
    return withErrorHandling(function fetchTransactions() {
        return decorateApi(TransactionEventsApi.getTransactionEvents, params)
    }, 'fetch transactions')
}
