'use server'

import {validateSession} from '@/auth'
import {decorateApi} from '@/services'
import PaymentsApi, {
    type GetPaymentMethods200Response,
    type GetPaymentMethodsRequest,
    type PaymentsConfiguration,
    type GetPaymentsRequest,
    type GetPayments200Response,
} from '@/services/controllers/payments'
import {withErrorHandling} from '@/services/apiWrapper'

export async function getPaymentAccounts(
    params?: Partial<GetPaymentMethodsRequest>,
): Promise<GetPaymentMethods200Response> {
    await validateSession()
    return withErrorHandling(async function fetchPaymentMethods() {
        const payments = await decorateApi(PaymentsApi.getPaymentMethods, params)
        // Mask the routing number and account number.
        payments.paymentMethods.forEach(payment => {
            payment.achDetails = {
                bankAccountType: payment.achDetails?.bankAccountType || 'CHECKING',
                routingNumber: `****${payment.achDetails?.routingNumber?.slice(-4)}`,
                accountNumber: `****${payment.achDetails?.accountNumber?.slice(-4)}`,
            }
        })
        return payments
    }, 'fetch payment methods')
}

export async function getPaymentConfig(): Promise<PaymentsConfiguration> {
    await validateSession()
    return withErrorHandling(function fetchPaymentConfig() {
        return decorateApi(PaymentsApi.getDefaultPaymentsConfiguration)
    }, 'fetch payment configuration')
}

export async function getPayments(params?: Partial<GetPaymentsRequest>): Promise<GetPayments200Response> {
    await validateSession()
    return withErrorHandling(function fetchPayments() {
        return decorateApi(PaymentsApi.getPayments, params)
    }, 'fetch payments')
}
