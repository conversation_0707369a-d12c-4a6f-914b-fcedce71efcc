'use client'

import {useEffect} from 'react'
import {Typography, Button, Box} from '@mui/material'
import ContentOuterWrapper from '@/components/layout/ContentOuterWrapper'
import ContentWrapper from '@/components/layout/ContentWrapper'

export default function Error({error, reset}: {error: Error & {digest?: string}; reset: () => void}) {
    useEffect(() => {
        console.error(error)
    }, [error])

    return (
        <ContentOuterWrapper>
            <ContentWrapper>
                <Box alignContent="center" justifyItems="center">
                    <Typography variant="h3" gutterBottom>
                        Something went wrong!
                    </Typography>
                    <Button variant="contained" color="primary" onClick={() => reset()}>
                        Try again
                    </Button>
                </Box>
            </ContentWrapper>
        </ContentOuterWrapper>
    )
}
