'use client'

import {useMemo, useState} from 'react'
import {
    Drawer,
    useMediaQuery,
    type Theme,
    Grid,
    IconButton,
    Typography,
    Fab,
    Button,
    Badge,
    Icon,
    Box,
} from '@mui/material'
import {useUXActions} from '@/components/Context/UXActions'
import CardList from './_components/CardList'
import type {Card} from '@/services/controllers/cards'
import CancelCard from '@/components/Cards/CancelCard'
import RequestCard from '@/components/Cards/RequestCard'
import ActivateCard from '@/components/Cards/ActivateCard'
import ReplaceCard from '@/components/Cards/ReplaceCard'
import {useCardsWithOwners} from '@/hooks/useCardsWithOwners'
import RemoveAuthUser from '@/components/Cards/RemoveAuthUser'

export default function ManageCards() {
    const {setLeftDrawerState} = useUXActions()
    const [open, setOpen] = useState(false)

    // Use the custom hook to fetch cards with owners
    const {data: cards, isLoading, isError} = useCardsWithOwners()

    function handleOpen() {
        setOpen(true)
        setLeftDrawerState(true)
    }

    function handleClose() {
        setOpen(false)
        setLeftDrawerState(false)
    }

    // Only count pending activations if we have cards data and no error
    const pendingActivationCount = cards?.filter(card => card.state === 'pendingActivation')?.length ?? 0

    return (
        <>
            <Badge badgeContent={isLoading ? undefined : pendingActivationCount} color="secondary">
                <Button
                    variant="contained"
                    color="secondary"
                    size="small"
                    sx={{
                        background: 'var(--mui-palette-secondary-light)',
                        color: 'var(--mui-palette-secondary-dark)',
                    }}
                    onClick={handleOpen}
                    disabled={isLoading || isError}
                    disableElevation
                >
                    My Cards
                </Button>
            </Badge>
            <CardDrawer open={open} handleClose={handleClose} isError={isError} />
        </>
    )
}

function CardDrawer({open, handleClose, isError}: {open: boolean; handleClose: () => void; isError: boolean}) {
    const fullScreen = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    const [action, setAction] = useState<null | 'activate' | 'replace' | 'cancel' | 'request' | 'removeAuthUser'>(null)
    const [actionCardId, setActionCardId] = useState<Card['cardId']>()

    const {data: cards} = useCardsWithOwners()

    const actionCard = useMemo(() => {
        if (cards) {
            return cards.find(card => card.cardId === actionCardId)
        }
        return undefined
    }, [actionCardId, cards])

    const [authUserId, setAuthUserId] = useState<string | undefined>(undefined)

    function handleAction(
        action?: 'activate' | 'replace' | 'cancel' | 'request' | 'removeAuthUser',
        referenceId?: string,
    ) {
        // Set action state, ensuring removeAuthUser is handled
        if (action === 'removeAuthUser') {
            setAction(action)
            setAuthUserId(referenceId)
        } else {
            setAction(action || null)
            setAuthUserId(undefined)
        }

        // Set the card ID from the reference ID if provided
        setActionCardId(referenceId)
    }

    const extraSx = fullScreen
        ? {
              width: 375,
          }
        : {
              height: '100%',
          }

    return (
        <Drawer
            open={open}
            anchor={fullScreen ? 'left' : 'bottom'}
            sx={{flexShrink: 0, '& .MuiDrawer-paper': {...extraSx, boxSizing: 'border-box', p: 2}}}
            onClose={handleClose}
            variant="persistent"
        >
            <Grid container flex={1} flexDirection="column" gap={2}>
                <>
                    {action === null && (
                        <DrawerContentsWrapper handleClose={handleClose} handleAction={handleAction}>
                            {isError ? (
                                <Box display="flex" justifyContent="center" alignItems="center" height="200px">
                                    <Typography color="error">Failed to load cards. Please try again later.</Typography>
                                </Box>
                            ) : (
                                <CardList onAction={handleAction} />
                            )}
                        </DrawerContentsWrapper>
                    )}
                    {action === 'activate' && (
                        <>
                            <DrawerContentsWrapper handleClose={handleClose} handleAction={handleAction}>
                                <CardList onAction={handleAction} />
                            </DrawerContentsWrapper>
                            <ActivateCard cardId={actionCard?.cardId} onClose={handleAction} />
                        </>
                    )}
                    {action === 'cancel' && actionCard && <CancelCard card={actionCard} onFinish={handleAction} />}
                    {action === 'request' && <RequestCard onFinish={handleAction} />}
                    {action === 'replace' && actionCard && <ReplaceCard card={actionCard} onFinish={handleAction} />}
                    {action === 'removeAuthUser' && authUserId && (
                        <RemoveAuthUser authUserId={authUserId} onFinish={handleAction} />
                    )}
                </>
            </Grid>
        </Drawer>
    )
}

function DrawerContentsWrapper({
    children,
    handleClose,
    handleAction,
}: {
    children: Readonly<React.ReactNode>
    handleClose: () => void
    handleAction: (action: 'request') => void
}) {
    const fullScreen = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    if (fullScreen) {
        return (
            <>
                <Grid container alignItems="center">
                    <IconButton onClick={handleClose}>
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" />
                    </IconButton>
                    <Typography variant="h6" flex={1}>
                        Cards
                    </Typography>
                    <Fab
                        color="primary"
                        size="small"
                        aria-label="Request a new card"
                        onClick={() => handleAction('request')}
                    >
                        <Icon baseClassName="fas" className="fa-solid fa-plus" />
                    </Fab>
                </Grid>
                {children}
            </>
        )
    } else {
        return (
            <>
                <Grid container alignItems="center">
                    <Typography variant="h6" flex={1}>
                        My Cards
                    </Typography>
                    <IconButton onClick={handleClose}>
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" />
                    </IconButton>
                </Grid>
                {children}
                <Grid container sx={{padding: '20px 24px'}}>
                    <Button fullWidth variant="contained" onClick={() => handleAction('request')}>
                        Request a new card
                    </Button>
                </Grid>
            </>
        )
    }
}
