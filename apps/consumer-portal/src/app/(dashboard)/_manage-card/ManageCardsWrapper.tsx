import {getDomainDiscoveryFromHeaders} from '@tallied-technologies/common'
import BasisTheoryWrapperContext from './_components/context'
import {headers} from 'next/headers'

export default async function ManageCardsWrapper({children}: {children: Readonly<React.ReactNode>}) {
    const headersList = await headers()
    const discovery = await getDomainDiscoveryFromHeaders(headersList)

    // For card activate endpoint
    // const BASIS_THEORY_CARD_API_PROXY_KEY = cardApiProxyKey || ''
    const BASIS_THEORY_REVEALER_PROXY_KEY = discovery?.cvcRevealerProxyKey || ''
    const BASIS_THEORY_API_KEY = discovery?.uiPublicAppKey || ''

    return (
        <BasisTheoryWrapperContext apiKey={BASIS_THEORY_API_KEY} revealerProxyKey={BASIS_THEORY_REVEALER_PROXY_KEY}>
            {children}
        </BasisTheoryWrapperContext>
    )
}
