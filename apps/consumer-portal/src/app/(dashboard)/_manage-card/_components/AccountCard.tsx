'use client'

import {useState, useRef} from 'react'
import type {<PERSON><PERSON><PERSON>, ReactNode, SetStateAction} from 'react'
import {
    Box,
    Grid,
    Typo<PERSON>,
    <PERSON>ton,
    Chip,
    IconButton,
    ListItem,
    ToggleButtonGroup,
    ToggleButton,
    Link as MUILink,
    TextField,
    InputAdornment,
    Snackbar,
    Alert,
    useTheme,
    Icon,
} from '@mui/material'
import type {Card, State, Type} from '@/services/controllers/cards'
import Image from 'next/image'
import {
    useBasisTheory,
    TextElement,
    CardNumberElement,
    CardExpirationDateElement,
} from '@basis-theory/basis-theory-react'
import type {
    CardNumberElement as CardNumberElementType,
    TextElement as TextElementType,
    CardExpirationDateElement as CardExpirationDateElementType,
} from '@basis-theory/basis-theory-react/types'
import {startBasisTheorySession} from '@/actions/cards'
import {useBasisTheoryContext} from './context'
import {useLockCard, useUnlockCard, useUpdateCardSpendControls} from '@/hooks/useCardData'
import AmountWithCadenceInput, {type AmountWithCadenceValue, type Cadence} from '@/components/AmountWithCadenceInput'
import {useProgramTheme} from '@/components/Providers/ProgramThemeProvider'

interface AccountCardProps {
    card: Card
    handleActivate?: () => void
    handleReplace?: () => void
    handleCancel?: () => void
    isAuthorizedUser?: boolean
}

// Define the SpendControl type if not already defined in the cards types
interface SpendControl {
    amount: number | null
    cadence: Cadence | null
    remaining: number | null
}

// Helper to format spend limit display
function formatSpendLimit(amount: number | undefined | null, cadence: string | undefined | null): ReactNode {
    if (!amount || amount === 0 || !cadence) {
        return (
            <Typography fontSize={16} fontWeight={400} lineHeight="160%" fontStyle="italic">
                No Spend Limit
            </Typography>
        )
    }
    return (
        <Typography
            fontSize={16}
            fontWeight={400}
            lineHeight="160%"
            fontStyle="italic"
        >{`Spend Limit $${Number(amount / 100).toLocaleString('en-US', {minimumFractionDigits: 2})}/${cadence}`}</Typography>
    )
}

function SpendLimitProgress({
    currentBalance,
    spendLimit,
    remaining,
    cadence,
}: {
    currentBalance: number
    spendLimit: number
    remaining: number
    cadence: string
}) {
    const usagePercentage = spendLimit > 0 ? Math.min(100, (currentBalance / spendLimit) * 100) : 0

    const getProgressBarColor = () => {
        if (usagePercentage >= 100) return 'var(--mui-palette-error-main)'
        if (usagePercentage > 66.66) return 'var(--mui-palette-warning-main)'
        return 'var(--mui-palette-primary-main)'
    }

    const getBackgroundColor = () => {
        if (usagePercentage >= 100) return 'var(--mui-palette-error-light)'
        if (usagePercentage > 66.66) return 'var(--mui-palette-warning-light)'
        return 'var(--mui-palette-primary-light)'
    }

    const getAvailableText = () => {
        switch (cadence) {
            case 'monthly':
                return 'Available to Spend This Month'
            case 'weekly':
                return 'Available to Spend This Week'
            case 'daily':
                return 'Available to Spend Today'
            default:
                return `Available to Spend This ${cadence}`
        }
    }

    return (
        <Grid container flexDirection="column" gap={1}>
            <Grid container alignItems="center" gap={1}>
                <Typography fontSize={12} color="textSecondary">
                    Current Balance
                </Typography>
                <Typography fontSize={12} fontWeight={600}>
                    ${(currentBalance / 100).toFixed(2)}
                </Typography>
            </Grid>
            <Box
                sx={{
                    width: '100%',
                    height: '8px',
                    backgroundColor: getBackgroundColor(),
                    borderRadius: '4px',
                    overflow: 'hidden',
                }}
            >
                <Box
                    sx={{
                        width: `${usagePercentage}%`,
                        height: '100%',
                        backgroundColor: getProgressBarColor(),
                        transition: 'width 0.3s ease, background-color 0.3s ease',
                    }}
                />
            </Box>
            <Grid container alignItems="center" gap={1}>
                <Typography fontSize={12} color="textSecondary">
                    {getAvailableText()}
                </Typography>
                <Typography fontSize={12} fontWeight={600}>
                    ${(remaining / 100).toFixed(2)}
                </Typography>
            </Grid>
        </Grid>
    )
}

export function AccountCard({
    card,
    handleActivate,
    handleReplace,
    handleCancel,
    isAuthorizedUser = false,
}: AccountCardProps) {
    const {programName} = useProgramTheme()
    const {palette} = useTheme()
    const {revealerProxyKey} = useBasisTheoryContext()
    const [nickName, setNickName] = useState<string | null>(null)
    const [editNickName, setEditNickName] = useState(false)
    const [editingSpendLimit, setEditingSpendLimit] = useState(false)

    const [basisTheoryErrorMessage, setBasisTheoryErrorMessage] = useState<null | string>(null)

    const panRef = useRef<CardNumberElementType>(null)
    const cvcRef = useRef<TextElementType>(null)
    const expirationRef = useRef<CardExpirationDateElementType>(null)

    const [showPan, setShowPan] = useState(false)

    const {bt, error} = useBasisTheory()
    if (error) {
        console.error(error)
    }

    // Use the mutation hooks for optimistic UI updates
    const {mutate: lockCardMutation, isPending: isLocking} = useLockCard(card)
    const {mutate: unlockCardMutation, isPending: isUnlocking} = useUnlockCard(card)

    const {mutate: updateCardSpendControlsMutation, isPending: isUpdatingSpendControls} =
        useUpdateCardSpendControls(card)

    // Get current spend control values (assuming first control is the one we use)
    const currentSpendControl: SpendControl = (() => {
        // Handle both Array and Set types for spendControls
        if (card.spendControls) {
            // Convert to array if it's a Set
            const spendControlsArray = Array.from(card.spendControls)

            if (spendControlsArray.length > 0) {
                const firstControl = spendControlsArray[0]
                return {
                    amount: firstControl.limit.amount || null,
                    cadence: firstControl.cadence.interval || null,
                    remaining: firstControl.remaining.amount || null,
                }
            }
        }

        // Default return if no valid spend controls
        return {amount: null, cadence: null, remaining: null}
    })()
    const [spendLimitValue, setSpendLimitValue] = useState<AmountWithCadenceValue>({
        amount: currentSpendControl.amount || null,
        cadence: currentSpendControl.cadence || 'monthly',
    })

    function updateCardLock() {
        try {
            if (card.state === 'locked') {
                unlockCardMutation()
            } else {
                lockCardMutation()
            }
        } catch (err) {
            console.error('Unable to update lock status', err)
        }
    }

    async function clickShowPan() {
        if (!bt) {
            console.log('bt is not initialized yet')
        }
        const panElement = panRef.current!
        const CVCElement = cvcRef.current!
        const expirationElement = expirationRef.current!

        if (showPan) {
            setShowPan(false)
            panElement.clear()
            return
        }
        try {
            const session = await bt?.sessions.create()

            if (session) {
                await startBasisTheorySession(card.cardId, session.nonce)

                const tokenResponse = await bt?.tokens.retrieve(card.token, {apiKey: session.sessionKey})

                if (tokenResponse) {
                    let cvvValue = tokenResponse.data.cvc ?? ''
                    if (!tokenResponse?.data.cvc) {
                        try {
                            const cvvResponse = await bt?.proxy.get({
                                headers: {
                                    'BT-PROXY-KEY': revealerProxyKey,
                                },
                                apiKey: session.sessionKey,
                                path: `cards/${card.cardId}/authorization-data`,
                            })
                            cvvValue = cvvResponse.cvv
                        } catch (err) {
                            console.error(err)
                            setBasisTheoryErrorMessage('Unable to retrieve security code at this time.')
                        }
                    }

                    panElement.setValue(tokenResponse.data.number)
                    CVCElement.setValue(cvvValue)
                    expirationElement.setValue({
                        month: tokenResponse.data.expiration_month,
                        year: tokenResponse.data.expiration_year,
                    })

                    setShowPan(true)
                }
            }
        } catch (error) {
            console.error(error)

            setBasisTheoryErrorMessage('Unable to reveal card details at this time.')
        }
    }

    const aspectRatio = 1.586
    const cardWidth = 81

    // Handle spend limit save
    const handleSaveSpendLimit = async () => {
        await updateCardSpendControlsMutation(
            spendLimitValue.amount && spendLimitValue.amount > 0
                ? [{amount: spendLimitValue.amount, cadence: spendLimitValue.cadence}]
                : undefined,
        )

        setEditingSpendLimit(false)
    }

    return (
        <ListItem disableGutters>
            <Grid container flexDirection="column" flex={1} gap={1} sx={{py: 1}}>
                <Grid container flex={1} gap={1}>
                    <Grid container flexDirection="column" alignItems="center" sx={{flex: '0 0 100px'}} gap={1}>
                        <Image
                            src={getCardAsset(card.type, programName)}
                            alt="Card Art"
                            width={cardWidth}
                            height={cardWidth / aspectRatio}
                            style={{objectFit: 'fill', filter: card.state === 'canceled' ? 'grayScale(1)' : ''}}
                        />
                        <Box>
                            <CardLockActions value={card.state} handleChange={updateCardLock} />
                        </Box>
                    </Grid>
                    <Grid
                        container
                        flexDirection="column"
                        justifyContent="flex-start"
                        flex={1}
                        sx={{flexFlow: 'column'}}
                    >
                        <Grid container flexDirection="row" justifyContent="space-between">
                            <Typography
                                flex={1}
                                fontSize={12}
                                fontWeight={400}
                                color="textSecondary"
                                alignContent="center"
                                sx={{width: 'max-content'}}
                            >
                                {card.type === 'virtual' ? 'Virtual ' : ''}Card Number
                            </Typography>
                            <CardStatusChip status={card.state} />
                        </Grid>
                        <Grid container flexDirection="row" alignItems="flex-start" wrap="nowrap">
                            <Typography
                                component={'div'}
                                fontSize={16}
                                fontWeight={600}
                                lineHeight="160%"
                                sx={{mr: 1, minWidth: 185}}
                            >
                                {card.state === 'canceled' ? (
                                    <Typography fontFamily="Inconsolata">{`•••• •••• •••• ${card.lastFour ? card.lastFour : '••••'}`}</Typography>
                                ) : (
                                    <CardNumberElement
                                        ref={panRef}
                                        id={`c${card.cardId.slice(0, 8)}-pan`}
                                        bt={bt}
                                        placeholder={`•••• •••• •••• ${card.lastFour ? card.lastFour : '••••'}`}
                                        readOnly
                                        enableCopy
                                        iconPosition="none"
                                        style={{
                                            invalid: {color: palette.text.primary},
                                            base: {
                                                '::placeholder': {color: palette.text.primary},
                                                fontFamily: 'Inconsolata',
                                            },
                                            fonts: [
                                                'https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900&display=swap',
                                            ],
                                        }}
                                        copyIconStyles={{
                                            size: '18px',
                                            color: palette.text.secondary,
                                            successColor: palette.success.dark,
                                        }}
                                    />
                                )}
                            </Typography>
                            <Snackbar
                                open={!!basisTheoryErrorMessage}
                                anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                                autoHideDuration={3000}
                                onClose={() => setBasisTheoryErrorMessage(null)}
                            >
                                <Alert severity="error">{basisTheoryErrorMessage}</Alert>
                            </Snackbar>
                            {card.state !== 'canceled' && card.state !== 'pendingFulfillment' && (
                                <IconButton
                                    size="small"
                                    onClick={clickShowPan}
                                    sx={{transform: showPan ? '' : 'translateX(-40px)'}}
                                >
                                    {showPan ? (
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-eye-slash"
                                            sx={{height: 16, width: 16, fontSize: 16}}
                                        />
                                    ) : (
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-eye"
                                            sx={{height: 16, width: 16, fontSize: 16}}
                                        />
                                    )}
                                </IconButton>
                            )}
                        </Grid>
                        <Box display={showPan ? 'block' : 'none'}>
                            <Grid container flexDirection="row" gap={2}>
                                <Grid container flexDirection="column">
                                    <Typography fontSize={12} color="textSecondary">
                                        Exp Date
                                    </Typography>
                                    <Typography
                                        component={'div'}
                                        fontSize={16}
                                        fontWeight={600}
                                        lineHeight="160%"
                                        sx={{minWidth: 60}}
                                    >
                                        <CardExpirationDateElement
                                            ref={expirationRef}
                                            id={`c${card.cardId.slice(0, 8)}-expiration`}
                                            bt={bt}
                                            readOnly
                                            style={{
                                                base: {
                                                    fontFamily: 'Inconsolata',
                                                },
                                                fonts: [
                                                    'https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900&display=swap',
                                                ],
                                            }}
                                        />
                                    </Typography>
                                </Grid>
                                <Grid container flexDirection="column">
                                    <Typography fontSize={12} color="textSecondary">
                                        Security Code
                                    </Typography>
                                    <Typography component={'div'} fontSize={16} fontWeight={600} lineHeight="160%">
                                        <TextElement
                                            ref={cvcRef}
                                            id={`c${card.cardId.slice(0, 8)}-cvc`}
                                            bt={bt}
                                            readOnly
                                            style={{
                                                base: {
                                                    fontFamily: 'Inconsolata',
                                                },
                                                fonts: [
                                                    'https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900&display=swap',
                                                ],
                                            }}
                                        />
                                    </Typography>
                                </Grid>
                            </Grid>
                        </Box>
                        {editNickName ? (
                            <NickNameInput
                                nickName={nickName}
                                setNickName={setNickName}
                                setEditNickName={setEditNickName}
                            />
                        ) : card.state === 'canceled' ? null : (
                            <>
                                {!!nickName ? (
                                    <Grid>
                                        <Typography fontSize={16} fontWeight={600} lineHeight="160%" sx={{mr: 1}}>
                                            {nickName}
                                        </Typography>
                                    </Grid>
                                ) : null}
                                <Grid container flexDirection="row" gap={2}>
                                    <MUILink
                                        fontSize={12}
                                        fontWeight={400}
                                        textTransform="initial"
                                        onClick={() => setEditNickName(true)}
                                    >
                                        {!!nickName ? 'edit nickname' : 'add nickname'}
                                    </MUILink>
                                    {!!nickName ? (
                                        <MUILink
                                            fontSize={12}
                                            fontWeight={400}
                                            textTransform="initial"
                                            onClick={() => setNickName(null)}
                                        >
                                            remove nickname
                                        </MUILink>
                                    ) : null}
                                </Grid>
                            </>
                        )}
                    </Grid>
                </Grid>
                {/* Add Spend Control section after the nickname section */}
                {card.authorizedUserId && card.state !== 'canceled' && (
                    <>
                        {editingSpendLimit ? (
                            <Grid container flexDirection="row" alignItems="center" gap={2} flexWrap="nowrap">
                                <AmountWithCadenceInput value={spendLimitValue} onChange={setSpendLimitValue} />
                                <IconButton
                                    onClick={handleSaveSpendLimit}
                                    sx={{ml: 1}}
                                    size="small"
                                    aria-label="Save Spend Limit"
                                >
                                    <Icon baseClassName="fas" className="fa-solid fa-check" />
                                </IconButton>
                            </Grid>
                        ) : (
                            <Grid container flexDirection="row" alignItems="center">
                                <Box>{formatSpendLimit(currentSpendControl.amount, currentSpendControl.cadence)}</Box>
                                {!isAuthorizedUser && (
                                    <IconButton
                                        onClick={() => setEditingSpendLimit(true)}
                                        size="small"
                                        aria-label="Edit Spend Limit"
                                    >
                                        <Icon baseClassName="fas" className="fa-solid fa-pen" fontSize="small" />
                                    </IconButton>
                                )}
                            </Grid>
                        )}
                    </>
                )}
                {isAuthorizedUser && (
                    <Grid container flexDirection="column" gap={2}>
                        <Typography fontSize={12} color="textSecondary">
                            Spend Limit
                        </Typography>
                        <SpendLimitProgress
                            currentBalance={
                                currentSpendControl.amount && currentSpendControl.remaining
                                    ? currentSpendControl.amount - currentSpendControl.remaining
                                    : 0
                            }
                            spendLimit={currentSpendControl.amount || 0}
                            remaining={currentSpendControl.remaining || 0}
                            cadence={currentSpendControl.cadence || 'monthly'}
                        />
                    </Grid>
                )}

                {card.state === 'canceled' ? null : card.type === 'physical' ? (
                    card.state === 'pendingActivation' ? (
                        <Grid container justifyContent="flex-end" flex={1} flexDirection="row">
                            <Button variant="contained" color="primary" onClick={handleActivate}>
                                Activate My Card
                            </Button>
                        </Grid>
                    ) : card.state === 'pendingFulfillment' ? (
                        <Grid container justifyContent="flex-end" flex={1}>
                            <Button variant="contained" color="primary" onClick={handleActivate} disabled={true}>
                                Activate My Card
                            </Button>
                        </Grid>
                    ) : card.state === 'active' ? (
                        <Grid container justifyContent="space-between" flex={1} flexDirection="row">
                            <Button variant="text" color="error" onClick={handleCancel}>
                                Cancel Card
                            </Button>
                            <Button variant="contained" color="secondary" onClick={handleReplace}>
                                Replace Card
                            </Button>
                        </Grid>
                    ) : (
                        <Grid container justifyContent="flex-start" flex={1} flexDirection="row">
                            <Button variant="text" color="error" onClick={handleCancel}>
                                Cancel Card
                            </Button>
                        </Grid>
                    )
                ) : card.type === 'virtual' ? (
                    <Grid container justifyContent="space-between" flex={1} flexDirection="row">
                        <Button variant="text" color="error" onClick={handleCancel}>
                            Cancel Card
                        </Button>
                        <Button variant="contained" color="secondary" onClick={handleReplace}>
                            Replace Card
                        </Button>
                    </Grid>
                ) : null}
            </Grid>
        </ListItem>
    )
}

function getCardAsset(cardType: Type, program: string) {
    if (cardType === 'physical') {
        if (program === 'tallied') {
            return '/assets/Tallied-Card-Physical.png'
        } else {
            return '/assets/ADA-Card-Physical.png'
        }
    } else {
        if (program === 'tallied') {
            return '/assets/Tallied-Card-Virtual.png'
        } else {
            return '/assets/ADA-Card-Virtual.png'
        }
    }
}

function CardLockActions({value, handleChange}: {value: State; handleChange: () => void}) {
    const derivedState = value === 'locked' ? 'locked' : 'unlocked'
    const unavailableState = value === 'canceled' || value === 'pendingActivation' || value === 'pendingFulfillment'

    return (
        <Box sx={{position: 'relative'}}>
            <ToggleButtonGroup
                size="small"
                value={derivedState}
                aria-label={`Card is ${derivedState}`}
                sx={{
                    backgroundColor: theme =>
                        derivedState === 'locked' ? theme.palette.error.light : theme.palette.primary.light,
                    padding: '2px',
                    opacity: unavailableState ? 0.6 : 1,
                    filter: unavailableState ? 'grayScale(1)' : '',
                    '& .MuiToggleButtonGroup-firstButton': {
                        borderRadius: 1,
                    },
                    '& .MuiToggleButtonGroup-lastButton': {
                        borderRadius: 1,
                    },
                    position: 'relative',
                    zIndex: 1,
                }}
                onChange={() => {
                    handleChange()
                }}
                exclusive
            >
                <ToggleButton
                    value="unlocked"
                    aria-label="Unlock Card"
                    sx={{
                        height: 22,
                        backgroundColor: 'transparent',
                        color: theme => theme.palette.grey[500],
                        border: '1px solid transparent',
                        borderRadius: 1,
                        mr: 1,
                        position: 'relative',
                        zIndex: 2,
                        '&.Mui-selected': {
                            color: theme => theme.palette.common.white,
                            background: 'transparent',
                        },
                    }}
                    disabled={unavailableState}
                >
                    <Icon
                        baseClassName="fas"
                        className="fa-solid fa-lock-open"
                        sx={{height: 16, width: 14, fontSize: 14}}
                    />
                </ToggleButton>
                <ToggleButton
                    value="locked"
                    aria-label="Lock Card"
                    sx={{
                        height: 22,
                        backgroundColor: 'transparent',
                        color: theme => theme.palette.grey[500],
                        border: '1px solid transparent',
                        borderRadius: 1,
                        position: 'relative',
                        zIndex: 2,
                        '&.Mui-selected': {
                            color: theme => theme.palette.common.white,
                            background: 'transparent',
                        },
                    }}
                    disabled={unavailableState}
                >
                    <Icon baseClassName="fas" className="fa-solid fa-lock" sx={{height: 16, width: 14, fontSize: 14}} />
                </ToggleButton>

                {/* Sliding indicator */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: '2px',
                        left: derivedState === 'unlocked' ? '4px' : 'calc(50% + 2px)',
                        width: 'calc(50% - 4px)',
                        height: 'calc(100% - 4px)',
                        borderRadius: 1,
                        backgroundColor: theme =>
                            derivedState === 'locked' ? theme.palette.error.main : theme.palette.primary.main,
                        transition: 'left 0.3s ease, background-color 0.3s ease',
                        zIndex: 1,
                        pointerEvents: 'none',
                        willChange: 'left, background-color',
                    }}
                />
            </ToggleButtonGroup>
        </Box>
    )
}

function CardStatusChip({status}: {status: State}) {
    switch (status) {
        case 'active':
        case 'locked':
            return <Chip color="success" size="small" variant="outlined" label="Active" />
        case 'pendingActivation':
        case 'pendingFulfillment':
            return <Chip color="default" size="small" variant="outlined" label="Pending" />
        case 'canceled':
            return <Chip color="error" size="small" variant="outlined" label="Cancelled" />
        default:
            return null
    }
}

function NickNameInput({
    nickName,
    setNickName,
    setEditNickName,
}: {
    nickName: string | null
    setNickName: Dispatch<SetStateAction<string | null>>
    setEditNickName: Dispatch<SetStateAction<boolean>>
}) {
    return (
        <Grid container sx={{pt: 1, pb: 2}} gap={1}>
            <TextField
                variant="outlined"
                label="nickname"
                value={nickName === null ? '' : nickName}
                onChange={evt => setNickName(evt.currentTarget.value)}
                slotProps={{
                    input: {
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton onClick={() => setNickName('')}>
                                    <Icon baseClassName="fas" className="fa-solid fa-xmark" />
                                </IconButton>
                            </InputAdornment>
                        ),
                    },
                }}
                sx={{
                    flex: 1,
                }}
                autoComplete="false"
                size="small"
            />
            <Button
                variant="contained"
                startIcon={<Icon baseClassName="fas" className="fa-solid fa-check" />}
                onClick={() => setEditNickName(false)}
                color="primary"
                sx={{
                    minWidth: 'fit-content',
                    '& .MuiButton-icon': {
                        m: 0,
                    },
                }}
                size="small"
            ></Button>
        </Grid>
    )
}
