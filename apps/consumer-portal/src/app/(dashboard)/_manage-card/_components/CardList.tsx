'use client'

import {
    List,
    Grid,
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Typography,
    Icon,
    Divider,
    Box,
    Button,
} from '@mui/material'
import {AccountCard} from './AccountCard'
import {useCardsWithOwners, type CardWithOwner} from '@/hooks/useCardsWithOwners'

type CardAction = 'activate' | 'replace' | 'cancel' | 'removeAuthUser'

interface CardListProps {
    onAction: (action: CardAction, cardId: string) => void
}

interface CardGroup {
    title: string
    cards: CardWithOwner[]
    sortOrder: number // Add sort order to help with sorting
    isForAuthorizedUser?: boolean
}

export default function CardList({onAction}: CardListProps) {
    const {data: cards, isLoading, isError} = useCardsWithOwners()

    if (isLoading) {
        return <div>Loading...</div>
    }

    if (isError) {
        return <div>Error loading cards</div>
    }

    if (!cards) {
        return null
    }

    const activeCards = cards.filter(card => card.state !== 'canceled')
    const cancelledCards = cards.filter(card => card.state === 'canceled')

    // Split active cards into my cards and authorized user cards
    const myCards = activeCards
        .filter(card => !card.authorizedUserId)
        .sort((a, b) => new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime())

    // Group authorized user cards by authorizedUserId
    const authorizedUserGroups = activeCards
        .filter(card => card.authorizedUserId && card.ownerDetails)
        .reduce<CardGroup[]>((groups, card) => {
            const existingGroup = groups.find(group => group.title === `${card.ownerDetails!.firstName}'s Cards`)
            if (existingGroup) {
                existingGroup.cards.push(card)
            } else {
                groups.push({
                    title: `${card.ownerDetails!.firstName}'s Cards`,
                    cards: [card],
                    sortOrder: 1,
                    isForAuthorizedUser: true,
                })
            }
            return groups
        }, [])

    // Sort cards within each authorized user group by createdOn
    authorizedUserGroups.forEach(group => {
        group.cards.sort((a, b) => new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime())
    })

    // Sort authorized user groups by first name
    const sortedAuthorizedGroups = authorizedUserGroups.sort((a, b) => a.title.localeCompare(b.title))

    return (
        <Grid container alignSelf="stretch" gap={1} sx={{width: '100%', maxWidth: 800}}>
            <Accordion disableGutters elevation={0} defaultExpanded sx={{width: '100%'}}>
                <AccordionSummary expandIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-down" />}>
                    <Typography>My Cards</Typography>
                </AccordionSummary>
                <AccordionDetails sx={{p: 0}}>
                    <List sx={{width: '100%'}}>
                        {myCards.map((card, index) => (
                            <Box key={card.cardId}>
                                <AccountCard
                                    card={card}
                                    handleActivate={() => onAction('activate', card.cardId)}
                                    handleReplace={() => onAction('replace', card.cardId)}
                                    handleCancel={() => onAction('cancel', card.cardId)}
                                />
                                {index < myCards.length - 1 && <Divider />}
                            </Box>
                        ))}
                    </List>
                </AccordionDetails>
            </Accordion>
            {sortedAuthorizedGroups.map(group => (
                <Accordion key={group.title} disableGutters elevation={0} sx={{width: '100%'}}>
                    <AccordionSummary expandIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-down" />}>
                        <Typography>
                            {group.title} ({group.cards.length})
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails sx={{p: 0, justifyItems: 'center'}}>
                        <List sx={{width: '100%'}}>
                            {group.cards.map((card, index) => (
                                <Box key={card.cardId}>
                                    <AccountCard
                                        card={card}
                                        handleActivate={() => onAction('activate', card.cardId)}
                                        handleReplace={() => onAction('replace', card.cardId)}
                                        handleCancel={() => onAction('cancel', card.cardId)}
                                    />
                                    {index < group.cards.length - 1 && <Divider />}
                                </Box>
                            ))}
                        </List>
                        {group.cards[0].authorizedUserId && (
                            <Button
                                variant="outlined"
                                color="error"
                                startIcon={<Icon baseClassName="fas" className="fa-solid fa-trash" />}
                                onClick={() => onAction('removeAuthUser', group.cards[0].authorizedUserId!)}
                                sx={{mt: 2, mb: 1}}
                            >
                                Remove User
                            </Button>
                        )}
                    </AccordionDetails>
                </Accordion>
            ))}
            {cancelledCards.length > 0 && (
                <Accordion disableGutters elevation={0} sx={{width: '100%'}}>
                    <AccordionSummary expandIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-down" />}>
                        <Typography color="textSecondary">Cancelled Cards</Typography>
                    </AccordionSummary>
                    <AccordionDetails sx={{p: 0}}>
                        <List sx={{width: '100%'}}>
                            {cancelledCards.map((card, index) => (
                                <Box key={card.cardId}>
                                    <AccountCard card={card} />
                                    {index < cancelledCards.length - 1 && <Divider />}
                                </Box>
                            ))}
                        </List>
                    </AccordionDetails>
                </Accordion>
            )}
        </Grid>
    )
}
