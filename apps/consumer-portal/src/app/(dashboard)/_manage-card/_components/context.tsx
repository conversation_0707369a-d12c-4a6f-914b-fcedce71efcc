'use client'

import {createContext, useContext} from 'react'
import {useBasisTheory, BasisTheoryProvider} from '@basis-theory/basis-theory-react'

interface BasisTheoryContextValue {
    apiKey: string
    revealerProxyKey: string
}

const BasisTheoryContext = createContext<BasisTheoryContextValue>({apiKey: '', revealerProxyKey: ''})

export default function BasisTheoryWrapperContext({
    children,
    apiKey,
    revealerProxyKey,
}: {
    children: React.ReactNode
    apiKey: string
    revealerProxyKey: string
}) {
    const {bt} = useBasisTheory(apiKey, {elements: true})

    return (
        <BasisTheoryProvider bt={bt}>
            <BasisTheoryContext.Provider value={{apiKey, revealerProxyKey}}>{children}</BasisTheoryContext.Provider>
        </BasisTheoryProvider>
    )
}

export function useBasisTheoryContext() {
    const context = useContext(BasisTheoryContext)

    if (context === undefined) {
        throw new Error('useBasisTheoryContext must be used within a BasisTheoryContext.Provider')
    }

    return context
}
