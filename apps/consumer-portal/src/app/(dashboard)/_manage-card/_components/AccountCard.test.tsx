import {render, screen, fireEvent, waitFor} from '@/test-utils'
import {vi, describe, it, expect, beforeEach} from 'vitest'
import {AccountCard} from './AccountCard'
import * as CardDataHooks from '@/hooks/useCardData'
import type {UseMutationResult} from '@tanstack/react-query'
import type {Card, SpendControlResponse, State} from '@/services/controllers/cards'
import type {Result} from '@/actions/types'

// Mock the basis theory hooks
vi.mock('@basis-theory/basis-theory-react', () => ({
    useBasisTheory: () => ({
        bt: null,
        error: null,
    }),
    TextElement: () => <div data-testid="text-element" />,
    CardNumberElement: () => <div data-testid="card-number-element" />,
    CardExpirationDateElement: () => <div data-testid="card-expiration-element" />,
}))

// Mock the context
vi.mock('./context', () => ({
    useBasisTheoryContext: () => ({
        revealerProxyKey: 'test-key',
    }),
}))

// Mock Next.js Image component
vi.mock('next/image', () => ({
    default: ({src, alt, ...props}: any) => <img src={src} alt={alt} {...props} />,
}))

describe('AccountCard Component', () => {
    // Mock card data with all required properties
    const mockActiveCard: Card = {
        cardId: 'card-123',
        state: 'active' as State,
        lastFour: '1234',
        type: 'physical',
        shippedTo: undefined,
        token: 'token-123',
        creditAccountId: 'account-123',
        expiration: {
            month: 12,
            year: 2025,
        },
        createdOn: new Date('2023-01-01T00:00:00Z'),
        updatedOn: new Date('2023-01-01T00:00:00Z'),
    }

    const mockSpendControl: SpendControlResponse = {
        limit: {
            amount: 200000, // $2000.00 in cents
            currency: 'USD',
        },
        cadence: {
            interval: 'monthly',
        },
        remaining: {
            amount: 200000,
            currency: 'USD',
        },
    }

    const mockSpendControlNoLimit: SpendControlResponse = {
        limit: {
            amount: 0,
            currency: 'USD',
        },
        cadence: {
            interval: 'monthly',
        },
        remaining: {
            amount: 0,
            currency: 'USD',
        },
    }

    const mockAuthorizedUserCard: Card = {
        ...mockActiveCard,
        authorizedUserId: 'auth-123',
        spendControls: new Set([mockSpendControl]),
    }

    const mockAuthorizedUserNoLimitCard: Card = {
        ...mockActiveCard,
        authorizedUserId: 'auth-123',
        spendControls: new Set([mockSpendControlNoLimit]),
    }

    const mockLockedCard: Card = {
        ...mockActiveCard,
        state: 'locked',
    }

    // Mock mutation functions
    const mockLockCardMutation = vi.fn()
    const mockUnlockCardMutation = vi.fn()
    const mockUpdateSpendControlsMutation = vi.fn()

    // Create mock of UseMutationResult based on state
    type MockMutationContext = {previousCardData: Card | undefined; previousCards: unknown}
    type SpendControlMutationVariables =
        | {amount: number; currency?: string; cadence: 'monthly' | 'daily' | 'weekly'}[]
        | undefined

    // Create type-safe mutation mocks
    const createMockMutation = <TVariables extends void | SpendControlMutationVariables>(
        mutateFunc: typeof mockLockCardMutation,
    ): UseMutationResult<Result<Card>, Error, TVariables, MockMutationContext> => ({
        mutate: mutateFunc as any,
        mutateAsync: vi.fn().mockResolvedValue({success: true, data: mockActiveCard}),
        isPending: false,
        isSuccess: true,
        isError: false,
        isIdle: false,
        isPaused: false,
        status: 'success',
        data: {success: true, data: mockActiveCard},
        error: null,
        failureCount: 0,
        failureReason: null,
        reset: vi.fn(),
        context: {previousCardData: undefined, previousCards: undefined},
        variables: undefined as TVariables,
        submittedAt: 0,
    })

    beforeEach(() => {
        vi.clearAllMocks()

        // Default to successful mutations with proper typing
        vi.spyOn(CardDataHooks, 'useLockCard').mockImplementation(card =>
            createMockMutation<void>(mockLockCardMutation),
        )
        vi.spyOn(CardDataHooks, 'useUnlockCard').mockImplementation(card =>
            createMockMutation<void>(mockUnlockCardMutation),
        )
        vi.spyOn(CardDataHooks, 'useUpdateCardSpendControls').mockImplementation(card =>
            createMockMutation<SpendControlMutationVariables>(mockUpdateSpendControlsMutation),
        )
    })

    it('renders an active card correctly', () => {
        render(<AccountCard card={mockActiveCard} />)

        // Check that the card is displayed with active state
        expect(screen.getByText('Card Number')).toBeInTheDocument()
        expect(screen.getByText('Active')).toBeInTheDocument()
    })

    it('renders a locked card correctly', () => {
        render(<AccountCard card={mockLockedCard} />)

        // Card should still show as "Active" in the status chip
        expect(screen.getByText('Active')).toBeInTheDocument()

        // But the toggle should show as locked
        const lockButton = screen.getByRole('button', {name: 'Lock Card'})
        expect(lockButton.classList.contains('Mui-selected')).toBe(true)
    })

    it('calls lockCard mutation when toggling from active to locked', async () => {
        render(<AccountCard card={mockActiveCard} />)

        // Find and click the lock button
        const lockButton = screen.getByRole('button', {name: 'Lock Card'})
        fireEvent.click(lockButton)

        // Verify the lock mutation was called
        expect(mockLockCardMutation).toHaveBeenCalledTimes(1)
    })

    it('calls unlockCard mutation when toggling from locked to active', async () => {
        render(<AccountCard card={mockLockedCard} />)

        // Find and click the unlock button
        const unlockButton = screen.getByRole('button', {name: 'Unlock Card'})
        fireEvent.click(unlockButton)

        // Verify the unlock mutation was called
        expect(mockUnlockCardMutation).toHaveBeenCalledTimes(1)
    })

    it('applies visual feedback for card state (locked vs active)', async () => {
        const {rerender} = render(<AccountCard card={mockActiveCard} />)

        // Check that the toggle group has the correct aria-label for active state
        let toggleGroup = screen.getByRole('group', {name: 'Card is unlocked'})
        expect(toggleGroup).toBeInTheDocument()

        // Rerender with locked card
        rerender(<AccountCard card={mockLockedCard} />)

        // Check that the toggle group has the correct aria-label for locked state
        toggleGroup = screen.getByRole('group', {name: 'Card is locked'})
        expect(toggleGroup).toBeInTheDocument()
    })

    describe('Spend Controls', () => {
        it('does not show spend controls for non-authorized user cards', () => {
            render(<AccountCard card={mockActiveCard} />)

            expect(screen.queryByText(/No Spend Limit/)).not.toBeInTheDocument()
            expect(screen.queryByText(/\$2,000.00\/monthly/)).not.toBeInTheDocument()
        })

        it('shows spend limit for authorized user card', () => {
            render(<AccountCard card={mockAuthorizedUserCard} />)

            expect(screen.getByText('Spend Limit $2,000.00/monthly')).toBeInTheDocument()
        })

        it('shows "No Spend Limit" when authorized user card has no limit', () => {
            render(<AccountCard card={mockAuthorizedUserNoLimitCard} />)

            expect(screen.getByText('No Spend Limit')).toBeInTheDocument()
        })

        it('allows editing spend limit', async () => {
            render(<AccountCard card={mockAuthorizedUserCard} />)

            // Click edit button
            const editButton = screen.getByRole('button', {name: /edit spend limit/i})
            fireEvent.click(editButton)

            // Verify AmountWithCadenceInput is shown
            const amountInput = screen.getByRole('spinbutton', {name: /amount/i})
            expect(amountInput).toBeInTheDocument()
            expect(amountInput).toHaveValue(2000) // $2000.00

            // Change amount
            fireEvent.change(amountInput, {target: {value: '3000'}})

            // Change cadence
            const weeklyButton = screen.getByRole('button', {name: /week/i})
            fireEvent.click(weeklyButton)

            // Save changes
            const saveButton = screen.getByRole('button', {name: /save spend limit/i})
            fireEvent.click(saveButton)

            // Verify mutation was called with correct values
            expect(mockUpdateSpendControlsMutation).toHaveBeenCalledWith([
                {
                    amount: 300000, // $3000.00 in cents
                    cadence: 'weekly',
                },
            ])
        })

        it('allows removing spend limit', async () => {
            render(<AccountCard card={mockAuthorizedUserCard} />)

            // Click edit button
            const editButton = screen.getByRole('button', {name: /edit spend limit/i})
            fireEvent.click(editButton)

            // Clear amount
            const amountInput = screen.getByRole('spinbutton', {name: /amount/i})
            fireEvent.change(amountInput, {target: {value: ''}})

            // Save changes
            const saveButton = screen.getByRole('button', {name: /save spend limit/i})
            fireEvent.click(saveButton)

            // Verify mutation was called with undefined (removing spend controls)
            expect(mockUpdateSpendControlsMutation).toHaveBeenCalledWith(undefined)
        })

        it('does not show edit button for canceled cards', () => {
            const canceledCard: Card = {...mockAuthorizedUserCard, state: 'canceled' as State}
            render(<AccountCard card={canceledCard} />)

            expect(screen.queryByRole('button', {name: /edit spend limit/i})).not.toBeInTheDocument()
        })
    })
})
