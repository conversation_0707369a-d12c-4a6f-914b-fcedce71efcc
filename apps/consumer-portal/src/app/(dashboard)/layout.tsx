import {DataCache} from '@/components/Context/DataCache'
import {getCards, getAuthorizedUsers, getPaymentAccounts, getUserDetails} from '@/data'

export default async function DashboardLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    const [user, cards, authorizedUsers, paymentMethods] = await Promise.all([
        getUserDetails(),
        getCards(),
        getAuthorizedUsers(),
        getPaymentAccounts({state: 'active'}),
    ])

    return (
        <DataCache user={user} cards={cards} authorizedUsers={authorizedUsers} paymentMethods={paymentMethods}>
            {children}
        </DataCache>
    )
}
