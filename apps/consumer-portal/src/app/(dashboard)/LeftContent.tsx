import {Card, CardContent, Grid} from '@mui/material'
import CardBalance, {LoadingCardBalance} from './_components/CardBalance'
import StatementBalanceCard, {LoadingStatementBalance} from './_components/StatementBalance'
import {getBalance, getAutopayConfig, getRewardsBalance} from '@/data'
import WelcomeBanner, {LoadingWelcomeBanner} from '@/components/WelcomeBanner'
import ManageCards from './_manage-card/ManageCards'
import ManageCardsWrapper from './_manage-card/ManageCardsWrapper'
import {APIError} from '@/services/APIError'
import RewardsBalance from './_components/RewardsBalance'

export default async function LeftContent() {
    try {
        const [balances, autopayConfig, rewardsBalance] = await Promise.all([
            getBalance(),
            getAutopayConfig({state: 'active'}),
            getRewardsBalance(),
        ])

        return (
            <Grid container gap={4} flexDirection="column" alignItems="stretch" alignSelf="stretch">
                <Grid container flexDirection="column" gap={2}>
                    <WelcomeBanner>
                        <ManageCardsWrapper>
                            <ManageCards />
                        </ManageCardsWrapper>
                    </WelcomeBanner>
                    <CardBalance balances={balances} />
                </Grid>
                <StatementBalanceCard
                    balances={balances}
                    autoPayEnabled={autopayConfig.length > 0}
                    autoPayId={autopayConfig[0]?.autopayConfigurationId}
                />
                <RewardsBalance rewardBalance={rewardsBalance.accrued - rewardsBalance.redeemed} />
            </Grid>
        )
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return (
                <Grid container>
                    <WelcomeBanner>&nbsp;</WelcomeBanner>
                    <Card>
                        <CardContent>There was a problem getting your balances please try again.</CardContent>
                    </Card>
                </Grid>
            )
        }
        throw error
    }
}

export function LoadingLeftContent() {
    return (
        <Grid container gap={4} flexDirection="column" alignItems="stretch" alignSelf="stretch">
            <Grid container flexDirection="column" gap={1}>
                <LoadingWelcomeBanner />
                <LoadingCardBalance />
            </Grid>
            <LoadingStatementBalance />
        </Grid>
    )
}
