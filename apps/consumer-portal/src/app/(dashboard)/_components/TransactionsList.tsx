import {useMemo} from 'react'
import Transaction from '@/components/Transaction'
import CollapsibleSection from '@/components/CollapsibleSection'
import type {GetTransactionEvents200ResponseEventsInner} from '@/services/controllers/transactionEvents'
import {Box} from '@mui/material'

interface TransactionListProps {
    transactionEvents: GetTransactionEvents200ResponseEventsInner[]
}

export default function TransactionList({transactionEvents}: TransactionListProps) {
    const {pendingTransactions, postedTransactions} = useMemo(() => {
        const pending: GetTransactionEvents200ResponseEventsInner[] = []
        const posted: GetTransactionEvents200ResponseEventsInner[] = []

        if (!transactionEvents || transactionEvents.length === 0) {
            return {pendingTransactions: pending, postedTransactions: posted}
        }

        // Dedupe transactions by eventId
        const uniqueTransactions = Array.from(
            transactionEvents
                .reduce((map, transaction) => {
                    const existing = map.get(transaction.eventId)

                    if (!existing) {
                        map.set(transaction.eventId, transaction)
                        return map
                    }

                    // Rule 1: If both have postedOn dates, take the later one
                    if (existing.postedOn && transaction.postedOn) {
                        if (transaction.postedOn > existing.postedOn) {
                            map.set(transaction.eventId, transaction)
                        }
                        return map
                    }

                    // Rule 2: If only one has postedOn, prefer that one
                    if (transaction.postedOn && !existing.postedOn) {
                        map.set(transaction.eventId, transaction)
                        return map
                    }
                    if (!transaction.postedOn && existing.postedOn) {
                        return map
                    }

                    // Rule 3: If neither has postedOn, compare transactedOn
                    if (transaction.transactedOn > existing.transactedOn) {
                        map.set(transaction.eventId, transaction)
                        return map
                    }

                    // Rule 4: If all else is equal, prefer 'posted' status
                    if (transaction.state === 'posted' && existing.state !== 'posted') {
                        map.set(transaction.eventId, transaction)
                    }

                    return map
                }, new Map())
                .values(),
        )

        // Split and sort transactions
        uniqueTransactions.forEach(transaction => {
            if (transaction.state === 'pending') {
                pending.push(transaction)
            } else if (transaction.state === 'posted') {
                posted.push(transaction)
            }
        })

        // Sort transactions
        pending.sort((left, right) => (left.transactedOn <= right.transactedOn ? 1 : -1))
        posted.sort((left, right) => (left.postedOn! <= right.postedOn! ? 1 : -1))

        return {pendingTransactions: pending, postedTransactions: posted}
    }, [transactionEvents])

    if (!transactionEvents || transactionEvents.length === 0) {
        return (
            <div>
                <p>There are no transactions given the provided filters, if any are applied.</p>
            </div>
        )
    }

    return (
        <Box sx={{width: '100%'}}>
            {pendingTransactions.length > 0 && (
                <CollapsibleSection title="Pending" count={pendingTransactions.length}>
                    {pendingTransactions.map(transaction => (
                        <Transaction key={transaction.eventId} {...transaction} />
                    ))}
                </CollapsibleSection>
            )}

            {postedTransactions.length > 0 && (
                <CollapsibleSection title="Posted" count={postedTransactions.length}>
                    {postedTransactions.map(transaction => (
                        <Transaction key={transaction.eventId} {...transaction} />
                    ))}
                </CollapsibleSection>
            )}
        </Box>
    )
}
