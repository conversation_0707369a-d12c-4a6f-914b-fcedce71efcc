'use client'

import {useState} from 'react'
import {Grid, Typography, IconButton, Tooltip, ClickAwayListener, Skeleton, Icon, Box} from '@mui/material'
import Image from 'next/image'
import {formatCurrency, flipNumberSign} from '@tallied-technologies/common'
import type {Type} from '@/services/controllers/cards'
import type {Balances} from '@/services/controllers/balances'
import {useProgramTheme} from '@/components/Providers/ProgramThemeProvider'

interface CardBalanceProps {
    balances: Balances
}

export default function CardBalance({balances}: CardBalanceProps) {
    const {programName} = useProgramTheme()
    const [tooltipOpen, setTooltipOpen] = useState(false)

    function openTooltip() {
        setTooltipOpen(true)
    }

    function closeTooltip() {
        setTooltipOpen(false)
    }

    const aspectRatio = 87.0 / 55.36
    const cardWidth = 90.5

    return (
        <Grid container flexDirection="row" gap={2}>
            <Box sx={{position: 'relative'}}>
                <Image
                    src="assets/Card-Shadow.svg"
                    alt="Card Shadow"
                    width={90}
                    height={22}
                    style={{
                        transform: 'translate(-7px, 83px)',
                        position: 'absolute',
                    }}
                />
                <Image
                    src={getCardAsset('physical', programName)}
                    alt="Card Art"
                    priority
                    width={cardWidth}
                    height={cardWidth / aspectRatio}
                    style={{
                        display: 'inline-flex',
                        flexDirection: 'column',
                        alignItems: 'flex-start',
                        gap: 10,
                        objectFit: 'fill',
                        marginTop: 12,
                        transform: 'rotate(-49.032deg)',
                        border: '0.5 solid #5ead5b',
                    }}
                />
            </Box>
            <Grid container flex={1} flexDirection="column">
                <Typography
                    fontSize={14}
                    lineHeight="20px"
                    color="textSecondary"
                    display="flex"
                    sx={{alignItems: 'center'}}
                >
                    Current Balance
                    <ClickAwayListener onClickAway={closeTooltip}>
                        <Tooltip
                            open={tooltipOpen}
                            onClose={closeTooltip}
                            disableFocusListener
                            disableHoverListener
                            placement="right-start"
                            slotProps={{
                                popper: {
                                    modifiers: [
                                        {
                                            name: 'offset',
                                            options: {
                                                offset: [0, -25],
                                            },
                                        },
                                    ],
                                    sx: {display: 'flex', alignItems: 'center'},
                                },
                                tooltip: {
                                    sx: {
                                        display: 'flex',
                                        maxWidth: 94,
                                        padding: [0.5, 1],
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    },
                                },
                            }}
                            title={
                                <Typography variant="caption" fontSize={10} fontStyle="italic" textAlign="center">
                                    These amounts do not include pending transactions
                                </Typography>
                            }
                        >
                            <IconButton onClick={openTooltip}>
                                {tooltipOpen ? (
                                    <Icon
                                        baseClassName="far"
                                        className="far fa-xmark"
                                        sx={{
                                            fontSize: 14,
                                            color: 'var(--mui-palette-common-white)',
                                            backgroundColor: 'var(--mui-palette-secondary-main)',
                                            borderRadius: '50%',
                                        }}
                                        data-testid="CancelIcon"
                                    />
                                ) : (
                                    <Icon
                                        baseClassName="fas"
                                        className="fa-solid fa-circle-info"
                                        sx={{
                                            fontSize: 14,
                                            color: 'var(--mui-palette-secondary-main)',
                                            backgroundColor: 'transparent',
                                        }}
                                        data-testid="InfoIcon"
                                    />
                                )}
                            </IconButton>
                        </Tooltip>
                    </ClickAwayListener>
                </Typography>
                <Typography fontSize={24} fontWeight={600} gutterBottom>
                    {formatCurrency(flipNumberSign(balances.outstandingBalance?.amount ?? 0) / 100)}
                </Typography>
                <Typography fontSize={14} lineHeight="20px" color="textSecondary">
                    Available Credit: {formatCurrency((balances.availableCredit?.amount ?? 0) / 100)}
                </Typography>
            </Grid>
        </Grid>
    )
}

export function LoadingCardBalance() {
    return (
        <Grid container flexDirection="row" gap={2}>
            <Skeleton variant="rectangular" height={96} width={343} />
        </Grid>
    )
}

function getCardAsset(cardType: Type, program: string) {
    if (cardType === 'physical') {
        if (program === 'tallied') {
            return '/assets/Tallied-Card-Physical.png'
        } else {
            return '/assets/ADA-Card-Physical.png'
        }
    } else {
        if (program === 'tallied') {
            return '/assets/Tallied-Card-Virtual.png'
        } else {
            return '/assets/ADA-Card-Virtual.png'
        }
    }
}
