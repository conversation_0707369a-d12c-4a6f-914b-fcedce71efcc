'use client'
import {useState} from 'react'
import {Grid, Button, Menu, MenuItem, Checkbox, ListItemText, Icon, Typography} from '@mui/material'
import type {Card} from '@/services/controllers/cards'

export default function TransactionFilters({cards = []}: {cards?: Card[]}) {
    // null == 'all cards' otherwise an array of cardIds as a inclusive filter
    const [selectedCard, setSelectedCard] = useState<Array<Card['cardId']> | null>(null)
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
    const open = Boolean(anchorEl)

    // Do not show a selector if there is only one card
    if (!cards || cards.length <= 1) return null

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget)
    }

    const handleClose = () => {
        setAnchorEl(null)
    }

    const handleCardToggle = (cardId: Card['cardId']) => {
        if (selectedCard === null) {
            // If "All Cards" was selected, start with all cards except the toggled one
            setSelectedCard(cards.filter(card => card.cardId !== cardId).map(card => card.cardId))
        } else if (selectedCard.includes(cardId)) {
            // If unchecking a card, ensure at least one remains selected
            const newSelection = selectedCard.filter(id => id !== cardId)
            if (newSelection.length === 0) {
                // If this would uncheck the last card, keep it checked
                return
            }
            setSelectedCard(newSelection)
        } else {
            // Add the card to selection
            const newSelection = [...selectedCard, cardId]
            // If all cards are now selected, set to null (all cards)
            if (newSelection.length === cards.length) {
                setSelectedCard(null)
            } else {
                setSelectedCard(newSelection)
            }
        }
    }

    const getCardDisplayName = (card: Card) => {
        const type = card.type === 'physical' ? 'Physical Card' : 'Virtual Card'
        return (
            <Grid container alignItems="center" spacing={1}>
                <Icon
                    className={`fas ${card.state === 'locked' ? 'fa-lock' : 'fa-unlock'}`}
                    sx={{
                        fontSize: 14,
                        color:
                            card.state === 'locked'
                                ? 'var(--mui-palette-error-main)'
                                : 'var(--mui-palette-action-active)',
                    }}
                />
                <Typography>
                    {type} ({card.lastFour})
                </Typography>
            </Grid>
        )
    }

    const isAllSelected = selectedCard === null
    const isCardSelected = (cardId: Card['cardId']) => selectedCard === null || selectedCard.includes(cardId)

    const getButtonText = () => {
        if (isAllSelected) {
            return `All Cards (${cards.length})`
        }
        const selectedCards = cards.filter(card => selectedCard?.includes(card.cardId))
        return selectedCards.map(card => card.lastFour).join(', ')
    }

    return (
        <Grid container sx={{width: '100%', px: 2}}>
            <Button
                variant="text"
                onClick={handleClick}
                endIcon={<Icon className={`fas ${open ? 'fa-caret-up' : 'fa-caret-down'}`} />}
                fullWidth
                sx={{
                    minWidth: 200,
                    width: 300,
                    px: 2,
                    justifyContent: 'space-between',
                    textTransform: 'none',
                    backgroundColor: 'var(--mui-palette-secondary-light)',
                    color: 'var(--mui-palette-text-primary)',
                    borderWidth: 1,
                    borderStyle: 'solid',
                    borderColor: open ? 'transparent' : 'var(--mui-palette-secondary-dark)',
                }}
                aria-expanded={open}
                aria-haspopup="menu"
                aria-label="Select cards to filter"
            >
                {getButtonText()}
            </Button>

            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                slotProps={{
                    paper: {
                        sx: {
                            maxHeight: 300,
                            width: 300,
                        },
                    },
                }}
            >
                {cards.map(card => (
                    <MenuItem key={card.cardId} onClick={() => handleCardToggle(card.cardId)} dense>
                        <Checkbox
                            checked={isCardSelected(card.cardId)}
                            disabled={
                                selectedCard !== null && selectedCard.length === 1 && selectedCard.includes(card.cardId)
                            }
                            tabIndex={-1}
                            disableRipple
                        />
                        <ListItemText primary={getCardDisplayName(card)} />
                    </MenuItem>
                ))}
            </Menu>
        </Grid>
    )
}
