import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen} from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CardBalance from './CardBalance'
import type {Balances} from '@/services/controllers/balances'

// Mock next/image since we don't need actual image loading in tests
vi.mock('next/image', () => ({
    default: ({alt, ...props}: any) => <img alt={alt} {...props} />,
}))

describe('CardBalance', () => {
    const mockBalances: Balances = {
        previousStatementBalance: {
            amount: 4000,
            currency: 'USD',
        },
        outstandingStatementBalance: {
            amount: 4000,
            currency: 'USD',
        },
        previousMinimumPayment: {
            amount: 4000,
            currency: 'USD',
        },
        outstandingMinimumPayment: {
            amount: 4000,
            currency: 'USD',
        },
        outstandingBalance: {
            amount: -4000,
            currency: 'USD',
        },
        availableCredit: {
            amount: 4000,
            currency: 'USD',
        },
        creditLimit: {
            amount: 4000,
            currency: 'USD',
        },
    }

    const mockCardArt = {
        src: '/mock-card.png',
        height: 100,
        width: 100,
    }

    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders card balance information correctly', () => {
        render(<CardBalance CardArt={mockCardArt} balances={mockBalances} />)

        expect(screen.getByText('Current Balance')).toBeInTheDocument()
        expect(screen.getByText('$40.00')).toBeInTheDocument()
        expect(screen.getByText(/Available Credit: \$40.00/)).toBeInTheDocument()
    })

    it('shows tooltip when info icon is clicked', async () => {
        const user = userEvent.setup()
        render(<CardBalance CardArt={mockCardArt} balances={mockBalances} />)

        const infoButton = screen.getByRole('button')
        await user.click(infoButton)

        expect(screen.getByText(/These amounts do not include pending transactions/)).toBeInTheDocument()
    })

    it('hides tooltip when clicking away', async () => {
        const user = userEvent.setup()
        render(
            <div>
                <CardBalance CardArt={mockCardArt} balances={mockBalances} />
                <div data-testid="outside">Click outside</div>
            </div>,
        )

        // Open tooltip
        const infoButton = screen.getByRole('button')
        await user.click(infoButton)

        // Verify tooltip is shown
        const tooltip = screen.getByText(/These amounts do not include pending transactions/)
        expect(tooltip).toBeInTheDocument()

        // Click outside
        const outsideElement = screen.getByTestId('outside')
        await user.click(outsideElement)

        // Verify tooltip is hidden
        expect(tooltip).not.toBeVisible()
    })

    it('toggles between info and cancel icons', async () => {
        const user = userEvent.setup()
        render(<CardBalance CardArt={mockCardArt} balances={mockBalances} />)

        // Initially shows info icon
        expect(screen.getByTestId('InfoIcon')).toBeInTheDocument()

        // Click to show tooltip and cancel icon
        const button = screen.getByRole('button')
        await user.click(button)
        expect(screen.getByTestId('CancelIcon')).toBeInTheDocument()
    })
})
