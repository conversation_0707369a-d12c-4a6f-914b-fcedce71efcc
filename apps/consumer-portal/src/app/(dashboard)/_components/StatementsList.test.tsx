import {describe, it, expect, vi} from 'vitest'
import {render, screen} from '@testing-library/react'
import Statements from './StatementsList'
import {getStatements} from '@/data'
import type {Statement} from '@/services/controllers/statements'
import {APIError} from '@/services/APIError'

// Mock the data fetching
vi.mock('@/data', () => ({
    getStatements: vi.fn(),
}))

describe('Statements', () => {
    const mockStatement: Statement = {
        dateUploaded: '1710366181987',
        displayTitle: 'April 1 - April 30, 2024',
        link: {
            href: 'https://program-bucket.s3.amazonaws.com/path/to/document.pdf?AWSAccessKeyId=AKIAIOSFODNN7EXAMPLE&Expires=1677640200&Signature=VyzYq3X7m6Ra0SQEXAMPLE',
            title: 'File Content',
        },
        statementBalance: {
            amount: 4000,
            currency: 'USD',
        },
        statementEndDate: '2023-08-31T14:15:22Z' as unknown as Date,
        statementPeriod: 'August 2023',
        statementStartDate: '2023-08-01T14:15:22Z' as unknown as Date,
        title: 'statement20240401',
    }

    it('renders error message when fetch fails', async () => {
        vi.mocked(getStatements).mockRejectedValue(
            new APIError({
                message: 'Failed to fetch statements',
                status: 500,
            }),
        )

        const Component = await Statements()
        render(Component)

        expect(screen.getByText(/Unable to get statements at the moment/)).toBeInTheDocument()
    })

    it('renders no statements message when statements array is empty', async () => {
        vi.mocked(getStatements).mockResolvedValue({
            statements: [],
        })

        const Component = await Statements()
        render(Component)

        expect(screen.getByText('You have no statements yet.')).toBeInTheDocument()
    })

    it('renders no statements message when statements is undefined', async () => {
        vi.mocked(getStatements).mockResolvedValue({
            statements: undefined,
        })

        const Component = await Statements()
        render(Component)

        expect(screen.getByText('You have no statements yet.')).toBeInTheDocument()
    })

    it('renders list of statements correctly', async () => {
        vi.mocked(getStatements).mockResolvedValue({
            statements: [mockStatement],
        })

        const Component = await Statements()
        render(Component)

        // Check statement period is displayed
        expect(screen.getByText('August 2023')).toBeInTheDocument()

        // Check display title is shown
        expect(screen.getByText('April 1 - April 30, 2024')).toBeInTheDocument()

        // Check statement balance is formatted correctly
        expect(screen.getByText('$40.00')).toBeInTheDocument()

        // Verify link properties
        const link = screen.getByRole('link')
        expect(link).toHaveAttribute('href', mockStatement?.link?.href)
        expect(link).toHaveAttribute('target', '_blank')
        expect(link).toHaveAttribute('rel', 'noopener noreferrer')
    })

    it('renders multiple statements in order', async () => {
        const secondStatement: Statement = {
            ...mockStatement,
            displayTitle: 'March 1 - March 31, 2024',
            statementPeriod: 'March 2024',
            title: 'statement20240301',
        }

        vi.mocked(getStatements).mockResolvedValue({
            statements: [mockStatement, secondStatement],
        })

        const Component = await Statements()
        render(Component)

        const periods = screen.getAllByText(/(August 2023|March 2024)/)
        expect(periods).toHaveLength(2)
        expect(periods[0]).toHaveTextContent('August 2023')
        expect(periods[1]).toHaveTextContent('March 2024')
    })

    it('handles missing link href by redirecting to 404', async () => {
        const statementWithoutLink: Statement = {
            ...mockStatement,
            link: {
                ...mockStatement.link,
                href: undefined,
            },
        }

        vi.mocked(getStatements).mockResolvedValue({
            statements: [statementWithoutLink],
        })

        const Component = await Statements()
        render(Component)

        // Use getByTestId since empty href may not register as link role
        const link = screen.getByRole('link')
        expect(link).toHaveAttribute('href', '/404')
    })
})
