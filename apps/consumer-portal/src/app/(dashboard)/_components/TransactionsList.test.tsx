import {describe, it, expect, vi} from 'vitest'
import {screen} from '@testing-library/react'
import TransactionList from './TransactionsList'
import type {GetTransactionEvents200ResponseEventsInner} from '@/services/controllers/transactionEvents'
import {render} from '@/test-utils'

// Mock the child components
vi.mock('@/components/Transaction', () => ({
    default: ({merchantInfo}: {merchantInfo: GetTransactionEvents200ResponseEventsInner['merchantInfo']}) => (
        <div data-testid="transaction">{merchantInfo?.descriptor}</div>
    ),
}))

vi.mock('@/components/CollapsibleSection', () => ({
    default: ({title, children, count}: {title: string; children: React.ReactNode; count: number}) => (
        <div data-testid="collapsible-section" data-title={title} data-count={count}>
            {children}
        </div>
    ),
}))

describe('TransactionList', () => {
    const mockTransactions: Partial<GetTransactionEvents200ResponseEventsInner>[] = [
        {
            eventId: '1',
            transactionId: '1',
            amount: {amount: 1000, currency: 'USD'},
            state: 'pending',
            merchantInfo: {descriptor: 'Test Merchant'},
            transactedOn: new Date('2024-03-20'),
        },
        {
            eventId: '2',
            transactionId: '2',
            amount: {amount: 2000, currency: 'USD'},
            state: 'posted',
            merchantInfo: {descriptor: 'Another Merchant'},
            transactedOn: new Date('2024-03-19'),
            postedOn: new Date('2024-03-19'),
        },
    ]

    it('renders error message when fetch fails', () => {
        render(<TransactionList transactionEvents={[]} />)
        expect(
            screen.getByText('There are no transactions given the provided filters, if any are applied.'),
        ).toBeInTheDocument()
    })

    it('renders no transactions message when events array is empty', () => {
        render(<TransactionList transactionEvents={[]} />)
        expect(
            screen.getByText('There are no transactions given the provided filters, if any are applied.'),
        ).toBeInTheDocument()
    })

    it('renders no transactions message when events is undefined', () => {
        render(<TransactionList transactionEvents={[]} />)
        expect(
            screen.getByText('There are no transactions given the provided filters, if any are applied.'),
        ).toBeInTheDocument()
    })

    it('renders pending and posted sections with correct transactions', () => {
        render(<TransactionList transactionEvents={mockTransactions as GetTransactionEvents200ResponseEventsInner[]} />)

        const sections = screen.getAllByTestId('collapsible-section')
        expect(sections).toHaveLength(2)
        expect(sections[0]).toHaveAttribute('data-title', 'Pending')
        expect(sections[1]).toHaveAttribute('data-title', 'Posted')

        const transactions = screen.getAllByTestId('transaction')
        expect(transactions).toHaveLength(2)
        expect(transactions[0]).toHaveTextContent('Test Merchant')
        expect(transactions[1]).toHaveTextContent('Another Merchant')
    })

    it('handles sections with no transactions', () => {
        render(<TransactionList transactionEvents={[]} />)
        expect(
            screen.getByText('There are no transactions given the provided filters, if any are applied.'),
        ).toBeInTheDocument()
    })
})
