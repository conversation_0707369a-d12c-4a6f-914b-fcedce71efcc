import {Card, <PERSON>rid, <PERSON>pography, Button, Skeleton, Icon} from '@mui/material'
import Link from 'next/link'
import {formatCurrency, flipNumberSign} from '@tallied-technologies/common'
import type {Payment} from '@/services/controllers/payments'
import type {Balances} from '@/services/controllers/balances'

// The due date currently is hard coded to the 21st of the month.
// There is no plan for different billing due dates to be unique per program or account

const DEFAULT_DUE_DATE = 21
function getNextPaymentDate() {
    let today = new Date()
    today = new Date(today.getFullYear(), today.getMonth(), today.getDate())

    let nextPaymentDate = new Date(today.getFullYear(), today.getMonth(), DEFAULT_DUE_DATE)

    // If we're past the 21st of this month, move to next month
    if (today.getDate() > DEFAULT_DUE_DATE) {
        nextPaymentDate = new Date(today.getFullYear(), today.getMonth() + 1, DEFAULT_DUE_DATE)
    }

    return nextPaymentDate
}

function formatDueDate() {
    let today = new Date()
    today = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const nextPayment = getNextPaymentDate()

    // Format the date
    const month = nextPayment.toLocaleString('en-US', {month: 'short'})

    // Calculate days difference
    const diffDays = nextPayment.getDate() - today.getDate()

    let daysText
    if (diffDays === 0) {
        daysText = "that's today!"
    } else if (diffDays === 1) {
        daysText = "that's tomorrow!"
    } else {
        daysText = `that's in ${diffDays} days!`
    }

    return `Due ${month} 21 - ${daysText}`
}

function formatPaymentDate(createdOn: string | Date) {
    return new Date(createdOn).toLocaleDateString('en-US', {month: '2-digit', day: '2-digit', year: 'numeric'})
}

function shouldShowWarning() {
    const today = new Date()
    today.setMinutes(today.getMinutes() + today.getTimezoneOffset())
    const nextPayment = getNextPaymentDate()

    const diffDays = nextPayment.getDate() - today.getDate()
    return diffDays <= 5 && diffDays >= 0
}

interface StatementBalanceCardProps {
    autoPayEnabled?: boolean // Autopay is not yet a thing on the backend. This will be defaulted to false
    autoPayId?: string
    paymentData?: Payment
    balances: Balances
}

export default function StatementBalanceCard(props: StatementBalanceCardProps) {
    const {autoPayEnabled = false, autoPayId, paymentData, balances} = props

    return (
        <Card sx={{p: 2}}>
            {/* Current Bill Info */}
            <Grid container flexDirection="column" gap={2} alignItems="flex-start" alignSelf="stretch">
                {/* Payment Info */}
                <Grid container flexDirection="column" gap="12px" alignItems="flex-start" alignSelf="stretch">
                    {/* Balance / Autopay */}
                    <Grid
                        container
                        justifyContent="space-between"
                        alignItems="flex-start"
                        alignSelf="stretch"
                        flexWrap="nowrap"
                    >
                        {/* Remaining Statement Balance */}
                        <Grid container flexDirection="column" alignItems="flex-start" gap="4px">
                            <Typography fontSize={14} lineHeight="24px" color="textSecondary">
                                Remaining Statement Balance
                            </Typography>
                            <Typography fontSize={32} lineHeight="32px" fontWeight={600} color="textPrimary">
                                {formatCurrency(
                                    flipNumberSign(balances.outstandingStatementBalance?.amount ?? 0) / 100,
                                )}
                            </Typography>
                        </Grid>

                        {/* Auto Pay */}
                        <Grid
                            container
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="flex-end"
                            gap="2px"
                            sx={{py: 0.5}}
                        >
                            {autoPayEnabled ? (
                                <>
                                    <Grid container alignItems="center" gap="4px" justifyContent="flex-end">
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-check-circle"
                                            color="success"
                                        />
                                        <Typography fontSize={12} fontWeight={600} color="success">
                                            Auto-pay ON
                                        </Typography>
                                    </Grid>
                                    <Typography
                                        component={Link}
                                        href={`/payments?q=edit-autopay&apcid=${autoPayId}`}
                                        fontSize={12}
                                    >
                                        manage
                                    </Typography>
                                </>
                            ) : (
                                <>
                                    <Grid container alignItems="center" gap="4px" justifyContent="flex-end">
                                        <Typography fontSize={12} fontWeight={600}>
                                            Auto-pay OFF
                                        </Typography>
                                    </Grid>
                                    <Typography component={Link} href="/payments?q=autopay" fontSize={12}>
                                        set up
                                    </Typography>
                                </>
                            )}
                        </Grid>
                    </Grid>
                    <Typography fontSize={14} lineHeight="16px" color="textSecondary">
                        Minimum Payment: {formatCurrency((balances.outstandingMinimumPayment?.amount ?? 0) / 100)}
                    </Typography>

                    {/* Due */}
                    {paymentData && paymentData?.amount.amount && paymentData?.createdOn ? (
                        <Grid container alignItems="center" gap={1}>
                            <Icon
                                baseClassName="fas"
                                className="fa-solid fa-check-circle"
                                color="success"
                                sx={{height: 16, width: 16}}
                            />
                            <Typography fontSize={14} fontWeight={600} lineHeight="24px">
                                Last payment of {formatCurrency(paymentData.amount.amount / 100)} on{' '}
                                {formatPaymentDate(paymentData.createdOn)}
                            </Typography>
                        </Grid>
                    ) : shouldShowWarning() && balances.outstandingStatementBalance?.amount !== 0 ? (
                        <Grid container alignItems="center" gap={1}>
                            <Icon
                                baseClassName="fas"
                                className="fa-solid fa-exclamation-triangle"
                                color="error"
                                sx={{height: 16, width: 16}}
                            />
                            <Typography fontSize={14} fontWeight={600} lineHeight="24px">
                                {formatDueDate()}
                            </Typography>
                        </Grid>
                    ) : null}
                </Grid>
                <Button component={Link} href="/payments" fullWidth variant="contained" color="secondary">
                    Make a payment
                </Button>
            </Grid>
        </Card>
    )
}

export function LoadingStatementBalance() {
    return (
        <Card sx={{p: 2}}>
            <Skeleton variant="rectangular" height={176} width={311} />
        </Card>
    )
}
