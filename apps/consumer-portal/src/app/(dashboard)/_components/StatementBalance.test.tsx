import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest'
import {render, screen} from '@testing-library/react'
import StatementBalanceCard from './StatementBalance'
import type {Balances} from '@/services/controllers/balances'
import type {Payment} from '@/services/controllers/payments'

// Mock next/link since we don't need actual navigation in tests
vi.mock('next/link', () => ({
    default: ({children, href}: {children: React.ReactNode; href: string}) => (
        <a href={href} data-testid="link">
            {children}
        </a>
    ),
}))

describe('StatementBalanceCard', () => {
    const mockBalances: Balances = {
        previousStatementBalance: {
            amount: -4000,
            currency: 'USD',
        },
        outstandingStatementBalance: {
            amount: -4000,
            currency: 'USD',
        },
        previousMinimumPayment: {
            amount: 4000,
            currency: 'USD',
        },
        outstandingMinimumPayment: {
            amount: 2500,
            currency: 'USD',
        },
        outstandingBalance: {
            amount: -4000,
            currency: 'USD',
        },
        availableCredit: {
            amount: 4000,
            currency: 'USD',
        },
        creditLimit: {
            amount: 4000,
            currency: 'USD',
        },
    }

    const mockPayment: Payment = {
        paymentId: '123',
        amount: {
            amount: 5000,
            currency: 'USD',
        },
        createdOn: '2024-03-15T12:00:00Z' as unknown as Date,
        creditAccountId: '123',
        description: 'Statement Balance',
        paymentMethodId: '123',
        state: 'completed',
        strategy: 'holdForCompletion',
        updatedOn: '2024-03-15T12:00:01Z' as unknown as Date,
    }

    beforeEach(() => {
        // Reset date to a known point for consistent testing
        vi.useFakeTimers()
        vi.setSystemTime(new Date('2024-03-15'))
    })

    afterEach(() => {
        vi.useRealTimers()
    })

    it('renders statement balance information correctly', () => {
        render(<StatementBalanceCard balances={mockBalances} />)

        expect(screen.getByText('Remaining Statement Balance')).toBeInTheDocument()
        expect(screen.getByText('$40.00')).toBeInTheDocument()
        expect(screen.getByText('Minimum Payment: $25.00')).toBeInTheDocument()
    })

    it('displays auto-pay OFF by default', () => {
        render(<StatementBalanceCard balances={mockBalances} />)

        expect(screen.getByText('Auto-pay OFF')).toBeInTheDocument()
        expect(screen.getByText('set up')).toBeInTheDocument()
    })

    it('displays auto-pay ON when enabled', () => {
        render(<StatementBalanceCard balances={mockBalances} autoPayEnabled={true} />)

        expect(screen.getByText('Auto-pay ON')).toBeInTheDocument()
        expect(screen.getByText('manage')).toBeInTheDocument()
    })

    it('shows last payment information when payment data is provided', () => {
        render(<StatementBalanceCard balances={mockBalances} paymentData={mockPayment} />)

        expect(screen.getByText(/Last payment of \$50.00 on 03\/15\/2024/)).toBeInTheDocument()
    })

    // it('shows "that\'s today!" when due date is today', () => {
    //     vi.setSystemTime(new Date('2024-03-21'))
    //     render(<StatementBalanceCard balances={mockBalances} />)

    //     expect(screen.getByText(/Due Mar 21 - that's today!/)).toBeInTheDocument()
    // })

    // it('shows "that\'s tomorrow!" when due date is tomorrow', () => {
    //     vi.setSystemTime(new Date('2024-03-20'))
    //     render(<StatementBalanceCard balances={mockBalances} />)

    //     expect(screen.getByText(/Due Mar 21 - that's tomorrow!/)).toBeInTheDocument()
    // })

    // it("shows next month's due date when past current month's due date", () => {
    //     vi.setSystemTime(new Date('2024-03-22'))
    //     render(<StatementBalanceCard balances={mockBalances} />)

    //     expect(screen.getByText(/Due Apr 21/)).toBeInTheDocument()
    // })

    it('renders make payment button with correct link', () => {
        render(<StatementBalanceCard balances={mockBalances} />)

        const paymentButton = screen.getByRole('link', {name: /make a payment/i})
        expect(paymentButton).toHaveAttribute('href', '/payments')
    })
})
