import {Grid, Typography, <PERSON>ton, Icon, Avatar} from '@mui/material'
import Link from 'next/link'

export default function RewardsBalance({rewardBalance}: {rewardBalance: number}) {
    return (
        <Grid container justifyContent="space-between" alignItems="center" sx={{width: 'auto'}}>
            <Grid container alignItems="flex-end" alignSelf="stretch" gap={2} sx={{pb: 2, width: 'auto'}}>
                <Avatar sx={{width: 40, height: 40, backgroundColor: 'secondary.main'}}>
                    <span className="fa-stack">
                        <i className="fa-solid fa-box fa-stack-1x" style={{fontSize: '24px'}} />
                        <i
                            className="fa-solid fa-dollar fa-stack-1x fa-inverse"
                            style={{fontSize: '10px', color: 'var(--mui-palette-secondary-main)', top: 2}}
                        />
                    </span>
                </Avatar>
                <Grid container flexDirection="column" alignItems="flex-start" gap={1} sx={{width: 'max-content'}}>
                    <Typography variant="h6" fontSize={14} color="textSecondary" lineHeight="20px">
                        Rewards Balance
                    </Typography>
                    <Typography variant="h4" fontSize={24} fontWeight={600} color="textPrimary" lineHeight="40px">
                        {rewardBalance.toLocaleString()}
                    </Typography>
                </Grid>
            </Grid>
            <Button
                component={Link}
                href="/rewards"
                variant="contained"
                color="secondary"
                size="small"
                disableElevation
                sx={{background: 'var(--mui-palette-secondary-light)', color: 'var(--mui-palette-secondary-dark)'}}
            >
                Redeem
            </Button>
        </Grid>
    )
}
