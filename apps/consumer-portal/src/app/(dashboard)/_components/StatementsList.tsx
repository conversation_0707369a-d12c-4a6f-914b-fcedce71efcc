import {List, Link, CardContent} from '@mui/material'
import {TableListItem} from '@/components/TableRowItem'
import {formatCurrency} from '@tallied-technologies/common'
import {getStatements} from '@/data'
import {APIError} from '@/services/APIError'

export default async function Statements() {
    try {
        const statements = await getStatements()

        if (!statements.statements || statements.statements.length === 0) {
            return <CardContent>You have no statements yet.</CardContent>
        }

        return (
            <List disablePadding component="ol" sx={{width: '100%'}}>
                {statements?.statements?.map(statement => {
                    return (
                        <Link
                            key={statement.title}
                            href={statement.link?.href ?? '/404'}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{textDecoration: 'none', color: 'inherit'}}
                        >
                            <TableListItem
                                leftPrimaryText={statement.statementPeriod!}
                                leftSecondaryText={statement.displayTitle}
                                rightPrimaryText={formatCurrency((statement.statementBalance?.amount ?? 0) / 100)}
                            />
                        </Link>
                    )
                })}
            </List>
        )
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return <CardContent>Unable to get statements at the moment please try again</CardContent>
        }
        throw error
    }
}
