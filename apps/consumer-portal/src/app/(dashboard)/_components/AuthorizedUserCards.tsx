'use client'

import type {Card} from '@/services/controllers/cards'
import {AccordionSummary, AccordionDetails, Accordion, Box, Divider, Grid, List, Typography, Icon} from '@mui/material'
import {AccountCard} from '../_manage-card/_components/AccountCard'
import {useMemo, useState} from 'react'
import CancelCard from '@/components/Cards/CancelCard'
import ActivateCard from '@/components/Cards/ActivateCard'
import ReplaceCard from '@/components/Cards/ReplaceCard'
import {useUserData} from '@/hooks/useUserData'

export default function AuthorizedUserCards({cards}: {cards?: Card[]}) {
    const {data: userData} = useUserData()
    const [action, setAction] = useState<null | 'activate' | 'replace' | 'cancel'>(null)
    const [actionCardId, setActionCardId] = useState<Card['cardId']>()

    const actionCard = useMemo(() => {
        if (cards) {
            return cards.find(card => card.cardId === actionCardId)
        }
        return undefined
    }, [actionCardId, cards])

    if (!cards) return null

    const activeCards = cards.filter(card => card.state !== 'canceled')
    const cancelledCards = cards.filter(card => card.state === 'canceled')

    function handleAction(action?: 'activate' | 'replace' | 'cancel', referenceId?: string) {
        setAction(action || null)

        // Set the card ID from the reference ID if provided
        setActionCardId(referenceId)
    }

    return (
        <Grid container sx={{width: '100%', px: 2}}>
            <List sx={{width: '100%'}}>
                {activeCards
                    .sort((left, right) => {
                        if (left.type === right.type) return 0
                        return left.type === 'virtual' ? 1 : -1
                    })
                    .map((card, index) => (
                        <Box key={card.cardId}>
                            <AccountCard
                                card={card}
                                handleActivate={() => handleAction('activate', card.cardId)}
                                handleReplace={() => handleAction('replace', card.cardId)}
                                handleCancel={() => handleAction('cancel', card.cardId)}
                                isAuthorizedUser={userData?.isAuthorizedUser}
                            />
                            {index < cards.length - 1 && <Divider />}
                        </Box>
                    ))}
            </List>
            {action === 'activate' && <ActivateCard cardId={actionCard?.cardId} onClose={handleAction} />}
            {action === 'cancel' && actionCard && <CancelCard card={actionCard} onFinish={handleAction} />}
            {action === 'replace' && actionCard && <ReplaceCard card={actionCard} onFinish={handleAction} />}
            {cancelledCards.length > 0 && (
                <Accordion disableGutters elevation={0} sx={{width: '100%'}}>
                    <AccordionSummary
                        sx={{p: 0}}
                        expandIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-down" />}
                    >
                        <Typography color="textSecondary">Cancelled Cards</Typography>
                    </AccordionSummary>
                    <AccordionDetails sx={{p: 0}}>
                        <List sx={{width: '100%'}}>
                            {cancelledCards.map((card, index) => (
                                <Box key={card.cardId}>
                                    <AccountCard card={card} />
                                    {index < cancelledCards.length - 1 && <Divider />}
                                </Box>
                            ))}
                        </List>
                    </AccordionDetails>
                </Accordion>
            )}
        </Grid>
    )
}
