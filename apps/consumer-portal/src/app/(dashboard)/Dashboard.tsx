import {Suspense} from 'react'
import ContentWrapper from '@/components/layout/ContentWrapper'
import LeftWrapper from '@/components/layout/LeftWrapper'
import RightWrapper from '@/components/layout/RightWrapper'
import ContentOuterWrapper from '@/components/layout/ContentOuterWrapper'
import LeftContent, {LoadingLeftContent} from './LeftContent'
import RightContent, {LoadingRightContent} from './RightContent'
import {validateSession} from '@/auth'
import AuthorizedUserDashboard from './AuthorizedUserDashboard'

export default async function Dashboard() {
    const {user} = await validateSession()

    return (
        <ContentOuterWrapper>
            <ContentWrapper>
                {user.isAuthorizedUser ? (
                    <AuthorizedUserDashboard />
                ) : (
                    <>
                        <Suspense fallback={<LoadingLeftContent />}>
                            <LeftWrapper>
                                <LeftContent />
                            </LeftWrapper>
                        </Suspense>
                        <Suspense fallback={<LoadingRightContent />}>
                            <RightWrapper>
                                <RightContent />
                            </RightWrapper>
                        </Suspense>
                    </>
                )}
            </ContentWrapper>
        </ContentOuterWrapper>
    )
}
