import {Grid, Typography} from '@mui/material'
import WelcomeBanner from '@/components/WelcomeBanner'
import {Tabs} from '@/components/Tabs'
import {validateSession} from '@/auth'
import {getTransactions, getCards} from '@/data'
import TransactionsList from './_components/TransactionsList'
import TransactionFilters from './_components/TransactionFilters'
import AuthorizedUserCards from './_components/AuthorizedUserCards'
import ManageCardsWrapper from './_manage-card/ManageCardsWrapper'

export default async function AuthorizedUserDashboard() {
    const {user} = await validateSession()

    const [transactions, cards] = await Promise.all([
        getTransactions({
            authorizedUserId: user.authorizedUserId,
        }),
        getCards({
            authorizedUserId: user.authorizedUserId,
        }),
    ])

    console.log('cards', cards)

    return (
        <Grid container flexDirection="column" sx={{maxWidth: 640, width: '100%'}} gap={2}>
            <WelcomeBanner />
            <Tabs
                tabs={[
                    {
                        node: (
                            <Grid container flexDirection="column" gap={2} sx={{pt: 2}}>
                                <TransactionFilters cards={cards.cards} />
                                <TransactionsList transactionEvents={transactions.events} />
                            </Grid>
                        ),
                        id: 'transactions_tab',
                        label: 'Recent Activity',
                    },
                    {
                        node: (
                            <ManageCardsWrapper>
                                <AuthorizedUserCards cards={cards.cards} />
                            </ManageCardsWrapper>
                        ),
                        id: 'cards_tab',
                        label: 'Cards',
                    },
                ]}
            />
        </Grid>
    )
}
