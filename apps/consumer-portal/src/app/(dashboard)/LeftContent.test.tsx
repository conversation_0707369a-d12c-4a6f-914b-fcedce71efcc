import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen, waitFor} from '@testing-library/react'
import LeftContent from './LeftContent'
import {LoadingLeftContent} from './LeftContent'
import {getBalance, getPersonDetails, getAutopayConfig, getRewardsBalance} from '@/data'
import type {Balances} from '@/services/controllers/balances'
import {APIError} from '@/services/APIError'
import type {AutopayConfiguration} from '@/services/controllers/autopay'

// Mock the external dependencies
vi.mock('@/data', () => ({
    getBalance: vi.fn(),
    getPersonDetails: vi.fn(),
    getAutopayConfig: vi.fn(),
    getRewardsBalance: vi.fn(),
}))

vi.mock('@tallied-technologies/assets', () => ({
    card: '/mocked-card-image',
}))

// Mock the child components
vi.mock('./_components/CardBalance', () => ({
    default: () => <div data-testid="card-balance">CardBalance Component</div>,
    LoadingCardBalance: () => <div data-testid="loading-card-balance">Loading Card Balance</div>,
}))

vi.mock('./_components/StatementBalance', () => ({
    default: ({
        balances,
        autoPayEnabled,
        autoPayId,
    }: {
        balances: Balances
        autoPayEnabled: boolean
        autoPayId?: string
    }) => (
        <div data-testid="statement-balance" data-autopay-enabled={autoPayEnabled} data-autopay-id={autoPayId}>
            StatementBalance Component
        </div>
    ),
    LoadingStatementBalance: () => <div data-testid="loading-statement-balance">Loading Statement Balance</div>,
}))

vi.mock('@/components/WelcomeBanner', () => ({
    default: ({children}: {children: React.ReactNode}) => (
        <div data-testid="welcome-banner">
            Welcome Banner Component
            {children}
        </div>
    ),
    LoadingWelcomeBanner: () => <div data-testid="loading-welcome-banner">Loading Welcome Banner</div>,
}))

vi.mock('./_manage-card/ManageCards', () => ({
    default: () => <div data-testid="manage-cards">Manage Cards Component</div>,
}))

vi.mock('./_manage-card/ManageCardsWrapper', () => ({
    default: ({children}: {children: React.ReactNode}) => <div data-testid="manage-cards-wrapper">{children}</div>,
}))

describe('LeftContent', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders successfully with balances', async () => {
        // Mock successful balance response
        const mockBalances: Balances = {
            previousStatementBalance: {
                amount: 4000,
                currency: 'USD',
            },
            outstandingStatementBalance: {
                amount: 4000,
                currency: 'USD',
            },
            previousMinimumPayment: {
                amount: 4000,
                currency: 'USD',
            },
            outstandingMinimumPayment: {
                amount: 4000,
                currency: 'USD',
            },
            outstandingBalance: {
                amount: -4000,
                currency: 'USD',
            },
            availableCredit: {
                amount: 4000,
                currency: 'USD',
            },
            creditLimit: {
                amount: 4000,
                currency: 'USD',
            },
        }

        const mockAutopayConfig = [
            {
                autopayConfigurationId: 'autopay-123',
                state: 'active',
                // Other props as needed for the test
            },
        ] as AutopayConfiguration[]

        vi.mocked(getBalance).mockResolvedValue(mockBalances)
        vi.mocked(getAutopayConfig).mockResolvedValue(mockAutopayConfig)
        vi.mocked(getRewardsBalance).mockResolvedValue({accrued: 100, redeemed: 10})

        // Render the async component
        const Component = await LeftContent()
        render(Component)

        // Assert components are rendered
        expect(screen.getByTestId('welcome-banner')).toBeInTheDocument()
        expect(screen.getByTestId('manage-cards-wrapper')).toBeInTheDocument()
        expect(screen.getByTestId('manage-cards')).toBeInTheDocument()
        expect(screen.getByTestId('card-balance')).toBeInTheDocument()

        // Check that StatementBalance has the correct autopay props
        const statementBalance = screen.getByTestId('statement-balance')
        expect(statementBalance).toBeInTheDocument()
        expect(statementBalance).toHaveAttribute('data-autopay-enabled', 'true')
        expect(statementBalance).toHaveAttribute('data-autopay-id', 'autopay-123')
    })

    it('renders error state when balance fetch fails', async () => {
        const mockAutopayConfig = [] as AutopayConfiguration[]

        // Mock error response
        vi.mocked(getBalance).mockRejectedValue(
            new APIError({
                message: 'Failed to fetch balances',
                status: 500,
            }),
        )
        vi.mocked(getAutopayConfig).mockResolvedValue(mockAutopayConfig)

        // Render the async component
        const Component = await LeftContent()
        render(Component)

        // Assert error message is shown
        expect(screen.getByText(/There was a problem getting your balances/)).toBeInTheDocument()
    })

    it('properly initializes ManageCards with data from API calls', async () => {
        vi.mocked(getBalance).mockResolvedValue({} as Balances)
        const mockAutopayConfig = [] as AutopayConfiguration[]

        vi.mocked(getAutopayConfig).mockResolvedValue(mockAutopayConfig)

        const Component = await LeftContent()
        render(Component)

        expect(screen.getByTestId('manage-cards')).toBeInTheDocument()
        expect(screen.getByTestId('manage-cards-wrapper')).toBeInTheDocument()

        await waitFor(() => {
            expect(vi.mocked(getAutopayConfig)).toHaveBeenCalledTimes(1)
        })
    })

    it('passes correct autopay data when no active autopay configs exist', async () => {
        vi.mocked(getBalance).mockResolvedValue({} as Balances)
        const mockAutopayConfig = [] as AutopayConfiguration[]

        vi.mocked(getAutopayConfig).mockResolvedValue(mockAutopayConfig)

        const Component = await LeftContent()
        render(Component)

        // Check that StatementBalance has the correct autopay props
        const statementBalance = screen.getByTestId('statement-balance')
        expect(statementBalance).toBeInTheDocument()
        expect(statementBalance).toHaveAttribute('data-autopay-enabled', 'false')
        // Check that the autopay ID is not present or null
        expect(statementBalance.getAttribute('data-autopay-id')).toBeNull()
    })

    it('renders loading state components', () => {
        render(<LoadingLeftContent />)

        expect(screen.getByTestId('loading-welcome-banner')).toBeInTheDocument()
        expect(screen.getByTestId('loading-card-balance')).toBeInTheDocument()
        expect(screen.getByTestId('loading-statement-balance')).toBeInTheDocument()
    })
})
