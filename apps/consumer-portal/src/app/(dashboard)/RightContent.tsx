import {Grid, Skeleton} from '@mui/material'
import {Tabs} from '@/components/Tabs'
import Statements from './_components/StatementsList'
import Transactions from './_components/TransactionsList'
import {getTransactions} from '@/data'

export default async function RightContent() {
    const transactions = await getTransactions()

    return (
        <Grid container flexDirection="column" alignItems="flex-start" alignSelf="stretch">
            <Tabs
                tabs={[
                    {
                        node: <Transactions transactionEvents={transactions.events} />,
                        id: 'transactions_tab',
                        label: 'Recent Activity',
                    },
                    {
                        node: <Statements />,
                        id: 'statements_tab',
                        label: 'Statements',
                    },
                ]}
            />
        </Grid>
    )
}

export function LoadingRightContent() {
    return (
        <Grid container flexDirection="column" alignItems="flex-start" alignSelf="stretch" sx={{mt: 2}}>
            <Skeleton variant="rectangular" width={640} height={189} />
        </Grid>
    )
}
