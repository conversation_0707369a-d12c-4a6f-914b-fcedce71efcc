import {validateSession} from '@/auth'
import {APIError} from '@/services/APIError'
import {getDispute} from '@/data'

const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

export async function GET(request: Request) {
    try {
        await validateSession()

        const disputeId = new URL(request.url).searchParams.get('disputeId')

        if (!disputeId) {
            return Response.json({error: 'MISSING_DISPUTE_ID'}, {status: 400})
        }

        if (!UUID_REGEX.test(disputeId)) {
            return Response.json({error: 'INVALID_DISPUTE_ID_FORMAT'}, {status: 400})
        }

        const dispute = await getDispute(disputeId)
        return Response.json(dispute, {status: 200})
    } catch (error) {
        console.error('Dispute route error:', error)

        if (APIError.isAPIError(error)) {
            const status = error.status
            return Response.json(
                {
                    error: error.message,
                    status: status,
                },
                {status},
            )
        }

        // Handle unknown errors
        return Response.json(
            {
                error: 'INTERNAL_SERVER_ERROR',
                status: 500,
            },
            {status: 500},
        )
    }
}
