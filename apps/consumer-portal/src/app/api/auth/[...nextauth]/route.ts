import {getZ<PERSON>delProvider} from '@/auth/provider'
import { getDomainDiscoveryFromHeaders } from '@tallied-technologies/common'
import {NextAuthResult} from 'next-auth'
import {headers} from 'next/headers'

import {NextRequest} from 'next/server'

const reqWithTrustedOrigin = (req: NextRequest): NextRequest => {
    if (process.env.AUTH_TRUST_HOST !== 'true') return req
    const proto = req.headers.get('x-forwarded-proto')
    const host = req.headers.get('x-forwarded-host')
    if (!proto || !host) {
        console.warn('Missing x-forwarded-proto or x-forwarded-host headers.')
        return req
    }
    const envOrigin = `${proto}://${host}`
    const {href, origin} = req.nextUrl
    return new NextRequest(href.replace(origin, envOrigin), req)
}

async function handleGET(req: NextRequest) {
    const discovery = await getDomainDiscoveryFromHeaders(req.headers)

    if (!discovery?.zitadelOrgDomain) {
        return new Response('Organization Domain not found', {status: 401})
    }

    const nextAuth = getZitadelProvider(discovery.zitadelOrgDomain)
    const handlers: NextAuthResult['handlers'] = nextAuth.handlers
    return handlers.GET(reqWithTrustedOrigin(req))
}

async function handlePOST(req: NextRequest) {
    const discovery = await getDomainDiscoveryFromHeaders(req.headers)

    if (!discovery?.zitadelOrgDomain) {
        return new Response('Organization Domain not found', {status: 401})
    }

    const nextAuth = getZitadelProvider(discovery.zitadelOrgDomain)
    const handlers: NextAuthResult['handlers'] = nextAuth.handlers
    return handlers.POST(reqWithTrustedOrigin(req))
}

export const GET = handleGET
export const POST = handlePOST
