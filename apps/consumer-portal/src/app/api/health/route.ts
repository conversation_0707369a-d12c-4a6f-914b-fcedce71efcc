import { loadDomainDiscovery } from "@tallied-technologies/common"

export async function GET() {
    try {
        await loadDomainDiscovery()
        return Response.json({status: 200})
    } catch (error) {
        console.error('health route error:', error)

        // Handle unknown errors
        return Response.json(
            {
                error: 'INTERNAL_SERVER_ERROR',
                status: 500,
            },
            {status: 500},
        )
    }
}