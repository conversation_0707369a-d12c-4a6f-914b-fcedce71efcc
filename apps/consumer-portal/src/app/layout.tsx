import type {Metadata} from 'next'
import {AppRouterCacheProvider} from '@mui/material-nextjs/v15-appRouter'
import AppShell from '@/components/layout/AppShell'
import UXActionsProvider from '@/components/Context/UXActions'
import QueryProvider from '@/components/Providers/QueryProvider'
import ProgramThemeProvider from '@/components/Providers/ProgramThemeProvider'

import '@/fontawesome/css/fontawesome.css'
import '@/fontawesome/css/solid.css'
import '@/fontawesome/css/regular.css'
import {getDomainDiscoveryFromHeaders} from '@tallied-technologies/common'
import {headers} from 'next/headers'
import {SessionProvider} from 'next-auth/react'
import {SessionTimeoutProvider} from '@/components/Context/SessionTimeoutProvider'
import DatadogRUMProvider from '@/components/DatadogRUMProvider'

export const metadata: Metadata = {
    title: 'Tallied Cardholder Portal - Tallied Internal Program',
    description: 'For internal use only. Cardholder Portal.',
    robots: {
        index: false,
        follow: false,
    },
    icons: {
        icon: '/tallied.png',
    },
}

export default async function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    const headersList = await headers()
    const discovery = await getDomainDiscoveryFromHeaders(headersList)

    return (
        <html lang="en">
            <head>
                {process.env.NODE_ENV === 'development' && (
                    <script src="https://unpkg.com/react-scan/dist/auto.global.js" async />
                )}
            </head>
            <body>
                <SessionProvider>
                    <DatadogRUMProvider env={process.env.DD_ENV ?? 'dev'} version={process.env.DD_VERSION ?? 'v0.0.0'}/>
                    <SessionTimeoutProvider>
                        <AppRouterCacheProvider>
                            <ProgramThemeProvider programName={discovery?.themeName ?? 'tallied'}>
                                <AppShell>
                                    <QueryProvider>
                                        <UXActionsProvider>{children}</UXActionsProvider>
                                    </QueryProvider>
                                </AppShell>
                            </ProgramThemeProvider>
                        </AppRouterCacheProvider>
                    </SessionTimeoutProvider>
                </SessionProvider>
            </body>
        </html>
    )
}
