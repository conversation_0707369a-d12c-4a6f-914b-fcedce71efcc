import {Suspense} from 'react'
import Footer from '@/components/Footer'
import Header from '@/components/Header'
import DashboardShell from '@/components/layout/DashboardShell'
import {Grid} from '@mui/material'
import {validateSession} from '@/auth'
import {getRewardsBalance} from '@/data'
import RewardsContent from './RewardsContent'

type Params = Promise<{slug: string}>
type SearchParams = Promise<URLSearchParams>

export default async function RewardsRoute(props: {params: Params; searchParams: SearchParams}) {
    await validateSession()

    const rewardsBalance = await getRewardsBalance()

    return (
        <Grid
            container
            flexDirection="column"
            flex="1 0 0"
            justifyContent="space-between"
            alignItems="center"
            alignSelf="stretch"
            flexWrap="nowrap"
        >
            <DashboardShell>
                <Header />
                <RewardsContent rewardsBalance={rewardsBalance.accrued - rewardsBalance.redeemed} />
                <Footer />
            </DashboardShell>
        </Grid>
    )
}
