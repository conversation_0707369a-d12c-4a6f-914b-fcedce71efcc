'use client'

import {<PERSON><PERSON>, Card, CardContent, Grid, Icon, IconButton, Typography} from '@mui/material'

export default function RewardsContent({rewardsBalance}: {rewardsBalance: number}) {
    return (
        <Grid container flexDirection="column" sx={{maxWidth: '640px', width: '100%', p: 2}} flex={1} gap={4}>
            <Typography variant="h4" fontSize={24} fontWeight={600} color="textPrimary" lineHeight="24px">
                Rewards
            </Typography>
            <Grid container sx={{pl: 1}} alignItems="center" alignSelf="stretch" gap={3}>
                <Avatar sx={{width: 56, height: 56, backgroundColor: 'secondary.main'}}>
                    <span className="fa-stack">
                        <i className="fa-solid fa-box fa-stack-1x" style={{fontSize: '32px'}} />
                        <i
                            className="fa-solid fa-dollar fa-stack-1x fa-inverse"
                            style={{fontSize: '13px', color: 'var(--mui-palette-secondary-main)', top: 4}}
                        />
                    </span>
                </Avatar>
                <Grid container flexDirection="column" alignItems="flex-start" gap={0.5} alignSelf="stretch">
                    <Typography variant="h6" fontSize={16} color="textSecondary" lineHeight="20px">
                        Rewards Balance
                    </Typography>
                    <Typography variant="h4" fontSize={32} fontWeight={600} color="textPrimary" lineHeight="40px">
                        {rewardsBalance.toLocaleString()}
                    </Typography>
                </Grid>
            </Grid>
            <Card>
                <CardContent>
                    <Grid container justifyContent="space-between" alignItems="center" wrap="nowrap">
                        <Grid container flexDirection="column" alignItems="flex-start" gap={1}>
                            <Typography
                                variant="h4"
                                fontSize={20}
                                fontWeight={600}
                                color="textPrimary"
                                lineHeight="143%"
                            >
                                Get Cashback
                            </Typography>
                            <Typography variant="body2" fontSize={16} color="textSecondary" lineHeight="143%">
                                Turn your rewards into a credit on your ADA statement
                            </Typography>
                        </Grid>
                        <IconButton>
                            <Icon baseClassName="fa-solid" className="fa-chevron-right" />
                        </IconButton>
                    </Grid>
                </CardContent>
            </Card>
            <Card>
                <CardContent>
                    <Grid container justifyContent="space-between" alignItems="center" wrap="nowrap">
                        <Grid container flexDirection="column" alignItems="flex-start" gap={1}>
                            <Typography
                                variant="h4"
                                fontSize={20}
                                fontWeight={600}
                                color="textPrimary"
                                lineHeight="143%"
                            >
                                Travel Rewards
                            </Typography>
                            <Typography variant="body2" fontSize={16} color="textSecondary" lineHeight="143%">
                                Show travel options with your rewards points
                            </Typography>
                        </Grid>
                        <IconButton>
                            <Icon baseClassName="fa-solid" className="fa-chevron-right" />
                        </IconButton>
                    </Grid>
                </CardContent>
            </Card>
        </Grid>
    )
}
