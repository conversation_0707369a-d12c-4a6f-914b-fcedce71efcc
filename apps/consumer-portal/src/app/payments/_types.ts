import type {AmountType, AutopayStrategyType} from '@/services/controllers/autopay'
import type {PaymentMethodVerificationProvider, MxAccountNumber, PaymentMethod} from '@/services/controllers/payments'

export type AccountState = {
    step: number
    workingProvider: PaymentMethodVerificationProvider
    mx?: {
        account?: MxAccountNumber
        nickname?: string
    }
    microdeposits?: {
        accountNumber?: string
        routingNumber?: string
        nickname?: string
        deposits?: Array<number>
    }
}

export type AccountAction =
    | {
          type: 'changeProvider'
          payload: PaymentMethodVerificationProvider
      }
    | {
          type: 'changeStep'
          payload: number
      }
    | {
          type: 'updateMxAccount'
          payload: MxAccountNumber
      }
    | {
          type: 'updateMxAccountNickname'
          payload: string
      }
    | {
          type: 'updateMicrodepositsAccount'
          payload: {
              accountNumber: string
              routingNumber: string
              nickname: string
          }
      }
    | {
          type: 'reset'
          payload: PaymentMethodVerificationProvider
      }

export interface PaymentOptionsState {
    step: number
    methodType: ('statementBalance' | 'currentBalance' | 'minimumPayment' | 'manualAmount' | '') & string
    amount: number
    paymentAccount: PaymentMethod['paymentMethodId']
    paymentDate: number
}

export interface AutopayOptionsState {
    step: number
    methodType: (AmountType | '') & string
    amount: number
    paymentAccount: PaymentMethod['paymentMethodId']
    paymentDate: number
    paymentStrategy: AutopayStrategyType
}

export type PaymentOptionsAction<T extends PaymentOptionsState | AutopayOptionsState> =
    | {type: 'changeStep'; payload: number}
    | {type: 'changeMethodType'; payload: T['methodType']}
    | {type: 'changeAmount'; payload: number}
    | {type: 'changePaymentAccount'; payload: string}
    | {type: 'changePaymentDate'; payload: number}
