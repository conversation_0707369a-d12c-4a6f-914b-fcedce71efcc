'use client'

import {
    <PERSON>rid,
    <PERSON>po<PERSON>,
    Checkbox,
    FormControlLabel,
    Box,
    Card,
    Dialog,
    DialogContent,
    DialogTitle,
    IconButton,
    Link,
    Icon,
    Snackbar,
    Alert,
    AlertTitle,
} from '@mui/material'
import type {AutopayOptionsState, PaymentOptionsAction} from '../../_types'
import {useState} from 'react'
import {getOrdinalSuffix} from './utils'
import {usePaymentAccountsData} from '@/hooks/usePaymentData'
import {formatCurrency} from '@tallied-technologies/common'

export default function PaymentStep4({
    paymentState,
    setPaymentState,
    termsAccepted,
    setTermsAccepted,
}: {
    paymentState: AutopayOptionsState
    setPaymentState: (value: PaymentOptionsAction<AutopayOptionsState>) => void
    termsAccepted: boolean
    setTermsAccepted: (accepted: boolean) => void
}) {
    const {data: paymentMethods} = usePaymentAccountsData({select: data => data.paymentMethods})
    const [termsDialogOpen, setTermsDialogOpen] = useState(false)
    const [paymentError, setPaymentError] = useState(false)
    const selectedAccount = paymentMethods?.find(acc => acc.paymentMethodId === paymentState.paymentAccount)

    // Calculate the next payment date for display in cancellation confirmation
    const getNextPaymentDate = () => {
        if (!paymentState || !paymentState.paymentDate) return undefined

        const now = new Date()
        const currentMonth = now.getMonth()
        const currentYear = now.getFullYear()

        // Create date for this month's payment
        let nextPaymentDate = new Date(currentYear, currentMonth, paymentState.paymentDate)

        // If this date has passed, use next month
        if (nextPaymentDate < now) {
            nextPaymentDate = new Date(currentYear, currentMonth + 1, paymentState.paymentDate)
        }

        // Format the date
        return nextPaymentDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        })
    }

    function closeToast() {
        setPaymentError(false)
    }

    function handleEditStep(step: number) {
        setPaymentState({
            type: 'changeStep',
            payload: step,
        })
    }

    function getPaymentTypeLabel() {
        switch (paymentState.methodType) {
            case 'lastStatementBalance':
                return 'Last Statement Balance'
            case 'minimumPaymentDue':
                return 'Minimum Payment Due'
            case 'fixed':
                return `Fixed Amount: ${formatCurrency(paymentState.amount / 100)}`
            default:
                return ''
        }
    }

    return (
        <Grid container gap={2} flexDirection="column" flex={1}>
            <Typography fontSize={24} fontWeight={600}>
                Review Auto-pay Details
            </Typography>

            <Card sx={{p: 3}}>
                <Grid container flexDirection="column" gap={3}>
                    {/* Payment Amount Section */}
                    <Grid container flexDirection="column" gap={1}>
                        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                            <Typography color="text.secondary">Payment Amount</Typography>
                            <IconButton size="small" onClick={() => handleEditStep(1)} aria-label="Edit payment amount">
                                <Icon baseClassName="fas" className="fa-solid fa-edit" fontSize="small" />
                            </IconButton>
                        </Box>
                        <Typography variant="h6">{getPaymentTypeLabel()}</Typography>
                    </Grid>

                    {/* Payment Date Section */}
                    <Grid container flexDirection="column" gap={1}>
                        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                            <Typography color="text.secondary">Payment Date</Typography>
                            <IconButton size="small" onClick={() => handleEditStep(2)} aria-label="Edit payment date">
                                <Icon baseClassName="fas" className="fa-solid fa-edit" fontSize="small" />
                            </IconButton>
                        </Box>
                        <Typography variant="h6">
                            {paymentState.paymentDate}
                            {getOrdinalSuffix(paymentState.paymentDate)} Day of Each Month
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Your first auto-payment will be processed on {getNextPaymentDate()}. If your selected
                            payment date falls on a non-banking day, we will process it one day earlier.
                        </Typography>
                    </Grid>

                    {/* Payment Method Section */}
                    <Grid container flexDirection="column" gap={1}>
                        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                            <Typography color="text.secondary">Payment Method</Typography>
                            <IconButton size="small" onClick={() => handleEditStep(3)} aria-label="Edit payment method">
                                <Icon baseClassName="fas" className="fa-solid fa-edit" fontSize="small" />
                            </IconButton>
                        </Box>
                        <Typography variant="h6" fontSize={16} fontWeight={600}>
                            {selectedAccount?.nickname}
                        </Typography>
                        <Typography variant="body2" fontSize={24} fontWeight={600}>
                            •••• {selectedAccount?.achDetails?.accountNumber.slice(-4)}
                        </Typography>
                    </Grid>
                </Grid>
            </Card>

            <FormControlLabel
                control={<Checkbox checked={termsAccepted} onChange={e => setTermsAccepted(e.target.checked)} />}
                label={
                    <Typography variant="body2" color="text.secondary">
                        By checking this box, I confirm that I have read and agree to the{' '}
                        <Link href="#" onClick={() => setTermsDialogOpen(true)}>
                            Auto-pay Terms and Conditions
                        </Link>{' '}
                        and I authorize Finwise Bank, a Utah-chartered bank, d/b/a Cottonwood Payments, to initiate
                        electronic payments from my bank account indicated above. The terms and conditions describe
                        important information, including cut-off times for making payments, how to change or cancel my
                        authorization, and the systems requirements for viewing and keeping a copy of the terms and
                        conditions. My authorization is for the amount and dates I have provided above and remains in
                        effect until cancelled.
                    </Typography>
                }
                sx={{alignItems: 'flex-start'}}
            />

            <Dialog open={termsDialogOpen} onClose={() => setTermsDialogOpen(false)} fullWidth maxWidth="md">
                <DialogTitle>
                    <IconButton onClick={() => setTermsDialogOpen(false)}>
                        <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />
                    </IconButton>
                    Auto-pay Terms and Conditions
                </DialogTitle>
                <DialogContent>
                    <Typography>
                        <b>Editing Payments</b>
                        <br />
                        You can edit or cancel a scheduled payment up to 5 p.m. ET on its scheduled date by calling the
                        telephone number on the back of your card or visiting autopay.citicards.com before 5 p.m. ET on
                        the day of your scheduled AutoPay payment.
                        <br />
                        <br />
                        <b>Cancelling AutoPay Enrollment</b>
                        <br />
                        You can cancel Auto-pay enrollment by calling the telephone number on the back of your card or
                        using the Cardholder Portal.
                        <br />
                        <br />
                        <b>Payment Amounts</b>
                        <br />
                        Your AutoPay payment amount may be reduced if you&apos;ve made additional payments or received
                        credits during the current billing cycle that result in a credit balance.
                        <br />
                        <br />
                        <b>Available Credit</b>
                        <br />
                        You will see a reduction in your current balance, and in your minimum payment due as soon as
                        your Auto-pay payment is processed. However, your available credit may not be increased by the
                        amount of your payment immediately & can take 5 business days (in some case up to 14 business
                        days) to reflect the amount of your payment.
                        <br />
                        <br />
                        <b>Returned Payments</b>
                        <br />
                        If any payment is returned unpaid by your financial institution for any reason you may also be
                        charged fees based on the terms and conditions of your Card Agreement. Viewing and Keeping These
                        Terms In order to view and retain a copy of the terms and conditions in the electronic format
                        provided, you understand that you must have: either a personal computer and a web browser with
                        at least 128-bit encryption or a web-enabled mobile device; an Internet connection; and systems
                        capability to open, view, save and print files in PDF format using Adobe Acrobat Reader or other
                        similar software.
                    </Typography>
                </DialogContent>
            </Dialog>
            <Snackbar
                open={paymentError}
                autoHideDuration={5000}
                onClose={closeToast}
                anchorOrigin={{vertical: 'top', horizontal: 'center'}}
            >
                <Alert
                    onClose={closeToast}
                    severity="error"
                    variant="filled"
                    sx={{
                        width: 350,
                    }}
                    icon={<Icon baseClassName="fas" className="fa-solid fa-exclamation-triangle" fontSize="small" />}
                >
                    <AlertTitle>Error occured.</AlertTitle>
                    <Typography>Your payment could not be posted at this time. Please try again.</Typography>
                </Alert>
            </Snackbar>
        </Grid>
    )
}
