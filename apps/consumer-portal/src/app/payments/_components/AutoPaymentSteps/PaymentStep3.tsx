'use client'

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Card, FormControlLabel, Radio, RadioGroup, Icon, IconButton} from '@mui/material'
import type {AutopayOptionsState, PaymentOptionsAction} from '../../_types'
import {usePaymentAccountsData} from '@/hooks/usePaymentData'
import AddPaymentAccountModal from '../AddPaymentAccountModal'
import {useState} from 'react'

export default function PaymentStep3({
    paymentState,
    setPaymentState,
    onBack,
}: {
    paymentState: AutopayOptionsState
    setPaymentState: (value: PaymentOptionsAction<AutopayOptionsState>) => void
    onBack?: () => void
}) {
    const {data: paymentMethods} = usePaymentAccountsData({select: data => data.paymentMethods})
    const [addPaymentMethodOpen, setAddPaymentMethodOpen] = useState(false)

    return (
        <>
            <Grid container gap={2} flexDirection="column" flex={1} sx={{minHeight: 300}}>
                <Typography fontSize={24} fontWeight={600} display="flex" alignItems="center" gap={1}>
                    {onBack && (
                        <IconButton onClick={onBack} aria-label="Back to main view" size="small" sx={{p: 0.5}}>
                            <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />
                        </IconButton>
                    )}
                    Payment Method
                </Typography>
                <Typography fontSize={14}>Where would you like to provide payment from?</Typography>
                <RadioGroup
                    name="payment-options"
                    value={paymentState.paymentAccount}
                    sx={{gap: 2}}
                    onChange={evt => setPaymentState({type: 'changePaymentAccount', payload: evt.target.value})}
                >
                    {paymentMethods?.map(paymentMethod => (
                        <Card
                            key={paymentMethod.paymentMethodId}
                            sx={{
                                p: 2,
                                backgroundColor:
                                    paymentState.paymentAccount === paymentMethod.paymentMethodId
                                        ? 'var(--mui-palette-primary-light)'
                                        : 'inherit',
                            }}
                            elevation={2}
                        >
                            <FormControlLabel
                                control={<Radio />}
                                value={paymentMethod.paymentMethodId}
                                label={
                                    <Grid container flexDirection="column">
                                        <Typography color="textSecondary">{paymentMethod.nickname}</Typography>
                                        <Typography>
                                            •••• {paymentMethod.achDetails?.accountNumber?.slice(-4) ?? 'XXXX'}
                                        </Typography>
                                    </Grid>
                                }
                                sx={{width: '100%'}}
                            />
                        </Card>
                    ))}
                </RadioGroup>
                <Button
                    variant="outlined"
                    startIcon={<Icon baseClassName="fas" className="fa-solid fa-plus" fontSize="small" />}
                    fullWidth
                    onClick={() => setAddPaymentMethodOpen(true)}
                >
                    Add a new Bank Account
                </Button>
            </Grid>
            <AddPaymentAccountModal open={addPaymentMethodOpen} onClose={() => setAddPaymentMethodOpen(false)} />
        </>
    )
}
