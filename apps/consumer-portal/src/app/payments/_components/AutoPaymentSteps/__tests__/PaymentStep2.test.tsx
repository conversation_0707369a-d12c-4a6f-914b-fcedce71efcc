import {render, screen, within} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import PaymentStep2 from '../PaymentStep2'
import {describe, it, expect, beforeEach, afterEach, beforeAll, afterAll, vi} from 'vitest'
import type {BankAccountType, PaymentMethodState} from '@/services/controllers/payments'
import type {AutopayOptionsState} from '@/app/payments/_types'

// Mock system date to be fixed at March 1, 2024
beforeAll(() => {
    const mockDate = new Date(2025, 2, 10) // Month is 0-based, so 2 = March
    vi.setSystemTime(mockDate)
})

afterAll(() => {
    vi.useRealTimers()
})

// Mock MUI useMediaQuery
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => false, // Default to mobile view
    }
})

const defaultPaymentState: AutopayOptionsState = {
    step: 2,
    methodType: 'lastStatementBalance',
    amount: 5000,
    paymentAccount: '123',
    paymentDate: 9, // Default to 9th
    paymentStrategy: 'dayInMonth',
}

const mockSetPaymentState = vi.fn()

describe('PaymentStep2', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    afterEach(() => {
        vi.clearAllMocks()
    })

    it('renders select with default value and shows all options when clicked', async () => {
        const user = userEvent.setup()

        render(<PaymentStep2 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} dueDate={21} />)

        // Find the select element
        const select = screen.getByRole('combobox')
        expect(select).toBeInTheDocument()

        // Verify the default selected value (9th)
        expect(select).toHaveTextContent('9th')

        // Click the select to open options
        await user.click(select)

        // MUI renders options in a portal, so we need to query the entire document
        const listbox = screen.getByRole('listbox')
        const options = within(listbox).getAllByRole('option')

        // Verify some specific options
        expect(options[0]).toHaveTextContent('1st')
        expect(options[8]).toHaveTextContent('9th') // Currently selected
        // With dueDate=21, expect exactly 20 options (days 1-20)
        expect(options.length).toBe(20)

        // Verify that no option exists for the due date or later
        const optionTexts = options.map(option => option.textContent)
        expect(optionTexts).not.toContain('21st')
        expect(optionTexts).not.toContain('22nd')
    })

    it('updates payment date when a new date is selected', async () => {
        const user = userEvent.setup()

        render(<PaymentStep2 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} dueDate={21} />)

        // Open the select
        const select = screen.getByRole('combobox')
        await user.click(select)

        // Select the 5th day
        const option5 = screen.getByRole('option', {name: '5th'})
        await user.click(option5)

        // Verify the callback was called with correct value
        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changePaymentDate',
            payload: 5,
        })
    })

    it('shows the correct next payment date based on selected date', () => {
        render(
            <PaymentStep2
                paymentState={{
                    ...defaultPaymentState,
                    paymentDate: 5,
                }}
                setPaymentState={mockSetPaymentState}
                dueDate={21}
            />,
        )

        // Since we mocked the date to March 1, 2024, next payment should be April 5, 2024
        const alert = screen.getByRole('alert')
        expect(within(alert).getByText(/April 5, 2025/i)).toBeInTheDocument()
    })

    it('displays back button when onBack prop is provided', () => {
        const mockOnBack = vi.fn()
        render(
            <PaymentStep2
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                dueDate={21}
                onBack={mockOnBack}
            />,
        )

        const backButton = screen.getByRole('button', {name: /back to main view/i})
        expect(backButton).toBeInTheDocument()
    })

    it('does not display back button when onBack prop is not provided', () => {
        render(<PaymentStep2 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} dueDate={21} />)

        const backButton = screen.queryByRole('button', {name: /back to main view/i})
        expect(backButton).not.toBeInTheDocument()
    })

    it('calls onBack when back button is clicked', async () => {
        const user = userEvent.setup()
        const mockOnBack = vi.fn()
        render(
            <PaymentStep2
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                dueDate={21}
                onBack={mockOnBack}
            />,
        )

        const backButton = screen.getByRole('button', {name: /back to main view/i})
        await user.click(backButton)
        expect(mockOnBack).toHaveBeenCalled()
    })
})
