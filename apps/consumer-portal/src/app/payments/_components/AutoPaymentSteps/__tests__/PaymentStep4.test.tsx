import {render, screen} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import PaymentStep4 from '../PaymentStep4'
import {describe, it, expect, beforeEach, beforeAll, afterAll, vi} from 'vitest'
import type {
    BankAccountType,
    PaymentMethodState,
    PaymentMethodType,
    PaymentMethodVerificationState,
} from '@/services/controllers/payments'
import type {AutopayOptionsState} from '@/app/payments/_types'

// Mock system date to be fixed at March 1, 2024
beforeAll(() => {
    const mockDate = new Date(2024, 2, 1) // Month is 0-based, so 2 = March
    vi.setSystemTime(mockDate)
})

afterAll(() => {
    vi.useRealTimers()
})

// Mock the usePaymentData hooks
vi.mock('@/hooks/usePaymentData', () => ({
    usePaymentAccountsData: () => ({
        data: [mockPaymentAccount],
        isLoading: false,
        error: null,
    }),
    usePaymentConfigData: () => ({
        data: {
            paymentMethodVerificationProvider: 'MX',
            microDepositsEnabled: true,
        },
        isLoading: false,
        error: null,
    }),
}))

// Mock MUI useMediaQuery
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => false, // Default to mobile view
    }
})

const mockPaymentAccount = {
    paymentMethodId: '123',
    paymentMethodState: 'active' as PaymentMethodState,
    nickname: 'Test Bank',
    achDetails: {
        accountNumber: '**********',
        bankAccountType: 'checking' as BankAccountType,
        routingNumber: '*********',
    },
    createdOn: new Date(),
    updatedOn: new Date(),
    creditAccountId: '0',
    personId: '0',
    paymentMethodType: 'ACH' as PaymentMethodType,
    paymentMethodVerificationState: 'verified' as PaymentMethodVerificationState,
}

const defaultPaymentState: AutopayOptionsState = {
    step: 4,
    methodType: 'lastStatementBalance',
    amount: 5000,
    paymentAccount: '123',
    paymentDate: 15,
    paymentStrategy: 'dayInMonth',
}

const mockSetPaymentState = vi.fn()
const mockSetTermsAccepted = vi.fn()

describe('PaymentStep4', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('displays all payment details correctly', () => {
        render(
            <PaymentStep4
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                termsAccepted={false}
                setTermsAccepted={mockSetTermsAccepted}
            />,
        )

        // Check payment type
        expect(screen.getByText('Last Statement Balance')).toBeInTheDocument()

        // Check payment date
        expect(screen.getByText(/15th Day of Each Month/)).toBeInTheDocument()
        expect(screen.getByText(/March 15, 2024/)).toBeInTheDocument() // Next payment date

        // Check bank account details
        expect(screen.getByText('Test Bank')).toBeInTheDocument()
        expect(screen.getByText('•••• 7890')).toBeInTheDocument()
    })

    it('toggles terms checkbox state when clicked', async () => {
        const user = userEvent.setup()
        render(
            <PaymentStep4
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                termsAccepted={false}
                setTermsAccepted={mockSetTermsAccepted}
            />,
        )

        // Find and click the terms checkbox
        const termsCheckbox = screen.getByRole('checkbox', {
            name: /By checking this box/i,
        })
        await user.click(termsCheckbox)

        expect(mockSetTermsAccepted).toHaveBeenCalledWith(true)
    })

    it('navigates to correct step when edit icons are clicked', async () => {
        const user = userEvent.setup()
        render(
            <PaymentStep4
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                termsAccepted={false}
                setTermsAccepted={mockSetTermsAccepted}
            />,
        )

        // Test editing payment amount (Step 1)
        const editAmountButton = screen.getByRole('button', {name: /edit payment amount/i})
        await user.click(editAmountButton)
        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changeStep',
            payload: 1,
        })

        // Test editing payment date (Step 2)
        const editDateButton = screen.getByRole('button', {name: /edit payment date/i})
        await user.click(editDateButton)
        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changeStep',
            payload: 2,
        })

        // Test editing payment method (Step 3)
        const editMethodButton = screen.getByRole('button', {name: /edit payment method/i})
        await user.click(editMethodButton)
        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changeStep',
            payload: 3,
        })
    })

    it('shows terms and conditions dialog when link is clicked', async () => {
        const user = userEvent.setup()
        render(
            <PaymentStep4
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                termsAccepted={false}
                setTermsAccepted={mockSetTermsAccepted}
            />,
        )

        const termsLink = screen.getByRole('link', {name: /auto-pay terms and conditions/i})
        await user.click(termsLink)

        expect(screen.getByRole('dialog')).toBeInTheDocument()
        expect(screen.getByText(/editing payments/i)).toBeInTheDocument()
    })
})
