import {render, screen} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import PaymentStep3 from '../PaymentStep3'
import {describe, it, expect, beforeEach, vi} from 'vitest'
import type {BankAccountType, PaymentMethodState} from '@/services/controllers/payments'
import type {AutopayOptionsState} from '@/app/payments/_types'

// Mock the AddPaymentAccountModal component
vi.mock('../AddPaymentAccountModal', () => ({
    default: ({open, onClose}) =>
        open ? (
            <div data-testid="mock-payment-modal">
                Mock Payment Modal <button onClick={onClose}>Close</button>
            </div>
        ) : null,
}))

// Mock the usePaymentData hooks
vi.mock('@/hooks/usePaymentData', () => ({
    usePaymentAccountsData: () => ({
        data: [mockPaymentAccount],
        isLoading: false,
        error: null,
    }),
    usePaymentConfigData: () => ({
        data: {
            paymentMethodVerificationProvider: 'MX',
            microDepositsEnabled: true,
        },
        isLoading: false,
        error: null,
    }),
}))

// Mock MUI useMediaQuery
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => false, // Default to mobile view
    }
})

const mockPaymentAccount = {
    paymentMethodId: '123',
    paymentMethodState: 'active' as PaymentMethodState,
    nickname: 'Test Bank',
    achDetails: {
        accountNumber: '**********',
        bankAccountType: 'checking' as BankAccountType,
        routingNumber: '*********',
    },
}

const defaultPaymentState: AutopayOptionsState = {
    step: 3,
    methodType: 'lastStatementBalance',
    amount: 5000,
    paymentAccount: '', // Start with no selected account
    paymentDate: 9,
    paymentStrategy: 'dayInMonth',
}

const mockSetPaymentState = vi.fn()

describe('PaymentStep3', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders payment accounts when available', () => {
        render(<PaymentStep3 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        // Check that the bank nickname is displayed
        expect(screen.getByText('Test Bank')).toBeInTheDocument()

        // Check that the masked account number is displayed
        expect(screen.getByText('•••• 7890')).toBeInTheDocument()
    })

    it('displays "Add a new Bank" button', () => {
        render(<PaymentStep3 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        const addButton = screen.getByRole('button', {name: /add a new bank/i})
        expect(addButton).toBeInTheDocument()
    })

    it('updates state when payment account is selected', async () => {
        const user = userEvent.setup()
        render(<PaymentStep3 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        // Find and click the radio button for the payment account
        const radio = screen.getByRole('radio')
        await user.click(radio)

        // Verify the state update was called
        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changePaymentAccount',
            payload: '123',
        })
    })

    it('displays back button when onBack prop is provided', () => {
        const mockOnBack = vi.fn()
        render(
            <PaymentStep3
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                onBack={mockOnBack}
            />,
        )

        const backButton = screen.getByRole('button', {name: /back to main view/i})
        expect(backButton).toBeInTheDocument()
    })

    it('does not display back button when onBack prop is not provided', () => {
        render(<PaymentStep3 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        const backButton = screen.queryByRole('button', {name: /back to main view/i})
        expect(backButton).not.toBeInTheDocument()
    })

    it('calls onBack when back button is clicked', async () => {
        const user = userEvent.setup()
        const mockOnBack = vi.fn()
        render(
            <PaymentStep3
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                onBack={mockOnBack}
            />,
        )

        const backButton = screen.getByRole('button', {name: /back to main view/i})
        await user.click(backButton)
        expect(mockOnBack).toHaveBeenCalled()
    })
})
