import {render, screen, within} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import PaymentStep1 from '../PaymentStep1'
import {describe, it, expect, beforeEach, vi} from 'vitest'
import type {BankAccountType, PaymentMethodState} from '@/services/controllers/payments'
import type {AutopayOptionsState} from '@/app/payments/_types'

// Mock the usePaymentAccountsData hook
vi.mock('@/hooks/usePaymentData', () => ({
    usePaymentAccountsData: () => ({
        data: [mockPaymentAccount],
        isLoading: false,
        error: null,
    }),
}))

// Mock MUI useMediaQuery
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => false, // Default to mobile view
    }
})

const mockPaymentAccount = {
    paymentMethodId: '123',
    paymentMethodState: 'active' as PaymentMethodState,
    nickname: 'Test Bank',
    achDetails: {
        accountNumber: '*********0',
        bankAccountType: 'checking' as BankAccountType,
        routingNumber: '*********',
    },
}

describe('PaymentStep1', () => {
    const mockSetPaymentState = vi.fn()
    const defaultPaymentState: AutopayOptionsState = {
        step: 1,
        methodType: 'lastStatementBalance',
        amount: 0,
        paymentAccount: '',
        paymentDate: 9,
        paymentStrategy: 'dayInMonth',
    }

    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders all payment options', () => {
        render(<PaymentStep1 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        expect(screen.getByText('Statement Balance')).toBeInTheDocument()
        expect(screen.getByText('Minimum Payment Due')).toBeInTheDocument()
        expect(screen.getByText('Fixed Amount')).toBeInTheDocument()
    })

    it('displays back button when onBack prop is provided', () => {
        const mockOnBack = vi.fn()
        render(
            <PaymentStep1
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                onBack={mockOnBack}
            />,
        )

        const backButton = screen.getByRole('button', {name: /back to main view/i})
        expect(backButton).toBeInTheDocument()
    })

    it('does not display back button when onBack prop is not provided', () => {
        render(<PaymentStep1 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        const backButton = screen.queryByRole('button', {name: /back to main view/i})
        expect(backButton).not.toBeInTheDocument()
    })

    it('calls onBack when back button is clicked', async () => {
        const user = userEvent.setup()
        const mockOnBack = vi.fn()
        render(
            <PaymentStep1
                paymentState={defaultPaymentState}
                setPaymentState={mockSetPaymentState}
                onBack={mockOnBack}
            />,
        )

        const backButton = screen.getByRole('button', {name: /back to main view/i})
        await user.click(backButton)
        expect(mockOnBack).toHaveBeenCalled()
    })

    it('allows only one payment option to be selected at a time', async () => {
        const user = userEvent.setup()
        render(<PaymentStep1 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        const statementBalanceOption = screen.getByRole('radio', {name: /statement balance/i})
        const minimumPaymentOption = screen.getByRole('radio', {name: /minimum payment/i})

        expect(statementBalanceOption).toBeChecked()

        await user.click(minimumPaymentOption)
        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changeMethodType',
            payload: 'minimumPaymentDue',
        })
    })

    it('shows tooltip content when info icon is clicked', async () => {
        const user = userEvent.setup()
        render(<PaymentStep1 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        const infoButton = screen.getByRole('button', {name: /payment options information/i})
        await user.click(infoButton)

        const tooltip = within(screen.getByRole('tooltip'))

        // Check for tooltip content
        expect(tooltip.getByText(/remaining statement balance/i)).toBeInTheDocument()
        expect(tooltip.getByText(/minimum payment/i)).toBeInTheDocument()
    })

    it('allows entering a custom amount for Fixed Amount option', async () => {
        const user = userEvent.setup()
        render(<PaymentStep1 paymentState={defaultPaymentState} setPaymentState={mockSetPaymentState} />)

        const fixedAmountOption = screen.getByRole('radio', {name: /fixed amount/i})
        await user.click(fixedAmountOption)

        const amountInput = screen.getByRole('spinbutton')
        await user.clear(amountInput)
        await user.type(amountInput, '50')

        expect(mockSetPaymentState).toHaveBeenCalledWith({
            type: 'changeAmount',
            payload: 5000, // Amount is stored in cents
        })
    })
})
