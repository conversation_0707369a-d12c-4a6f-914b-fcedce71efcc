import {render, screen} from '@/test-utils'
import PaymentStep5 from '../PaymentStep5'
import {describe, it, expect, beforeAll, afterAll, vi} from 'vitest'
import type {AutopayOptionsState} from '@/app/payments/_types'

// Mock system date to be fixed at March 1, 2024
beforeAll(() => {
    const mockDate = new Date(2024, 2, 1) // Month is 0-based, so 2 = March
    vi.setSystemTime(mockDate)
})

afterAll(() => {
    vi.useRealTimers()
})

// Mock the usePaymentData hooks
vi.mock('@/hooks/usePaymentData', () => ({
    usePaymentAccountsData: () => ({
        data: [],
        isLoading: false,
        error: null,
    }),
    usePaymentConfigData: () => ({
        data: {
            paymentMethodVerificationProvider: 'MX',
            microDepositsEnabled: true,
        },
        isLoading: false,
        error: null,
    }),
}))

const defaultPaymentState: AutopayOptionsState = {
    step: 5,
    methodType: 'lastStatementBalance',
    amount: 5000,
    paymentAccount: '123',
    paymentDate: 15,
    paymentStrategy: 'dayInMonth',
}

describe('PaymentStep5', () => {
    it('displays success message with payment date', () => {
        render(<PaymentStep5 paymentState={defaultPaymentState} />)

        // Check that success message is displayed
        expect(screen.getByText(/Your first auto-payment will processed on/i)).toBeInTheDocument()

        // Check next payment date is displayed correctly
        const nextPaymentDate = screen.getByText(/April 15, 2024/)
        expect(nextPaymentDate).toBeInTheDocument()
    })

    it('renders dashboard link button', () => {
        render(<PaymentStep5 paymentState={defaultPaymentState} />)

        const dashboardLink = screen.getByRole('link', {name: /Back to Dashboard/i})
        expect(dashboardLink).toBeInTheDocument()
        expect(dashboardLink).toHaveAttribute('href', '/')
    })
})
