'use client'

import {
    Grid,
    Typography,
    RadioGroup,
    Card,
    FormControlLabel,
    Radio,
    OutlinedInput,
    InputAdornment,
    Box,
    IconButton,
    ClickAwayListener,
    Tooltip,
    Icon,
} from '@mui/material'
import {useState} from 'react'
import type {AutopayOptionsState, PaymentOptionsAction} from '../../_types'
import {usePaymentAccountsData} from '@/hooks/usePaymentData'

export default function PaymentStep1({
    paymentState,
    setPaymentState,
    onBack,
}: {
    paymentState: AutopayOptionsState
    setPaymentState: (value: PaymentOptionsAction<AutopayOptionsState>) => void
    onBack?: () => void
}) {
    const {data: paymentMethods} = usePaymentAccountsData({select: data => data.paymentMethods})
    const [manualAmount, setManualAmount] = useState('0')
    const [tooltipOpen, setTooltipOpen] = useState(false)

    const noPaymentMethods = paymentMethods?.length === 0

    function openTooltip() {
        setTooltipOpen(true)
    }

    function closeTooltip() {
        setTooltipOpen(false)
    }

    return (
        <>
            <Grid container gap={2} flexDirection="column" flex={1}>
                <Typography fontSize={24} fontWeight={600} display="flex" alignItems="center" gap={1} component="div">
                    {onBack && (
                        <IconButton onClick={onBack} aria-label="Back to main view" size="small" sx={{p: 0.5}}>
                            <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />
                        </IconButton>
                    )}
                    Payment Amount
                    <ClickAwayListener onClickAway={closeTooltip}>
                        <Box>
                            <Tooltip
                                aria-describedby="payment-options-tooltip"
                                open={tooltipOpen}
                                onClose={closeTooltip}
                                disableFocusListener
                                disableHoverListener
                                placement="bottom"
                                slotProps={{
                                    popper: {
                                        modifiers: [
                                            {
                                                name: 'offset',
                                                options: {
                                                    offset: [0, -25],
                                                },
                                            },
                                        ],
                                        sx: {display: 'flex', alignItems: 'center'},
                                    },
                                    tooltip: {
                                        sx: {
                                            display: 'flex',
                                            padding: [0.5, 1],
                                            minWidth: 350,
                                            maxWidth: 600,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            backgroundColor: 'var(--mui-palette-grey-900)',
                                        },
                                    },
                                }}
                                title={
                                    <Box sx={{whiteSpace: 'pre-line'}}>
                                        <Typography variant="caption" component="div" fontSize={14}>
                                            <Box component="b">Remaining Statement Balance</Box>
                                            {'\n'}
                                            The previous month&apos;s statement balance in full adjusted for payments,
                                            returned payments, credits (excluding rewards redeemed for statement
                                            credits) and disputes since your last statement closing date.
                                            {'\n\n'}
                                            {/* <Box component="b">Current Balance</Box>
                                            {'\n'}
                                            The total amount currently owed at the time the payment is processed.
                                            {'\n\n'} */}
                                            <Box component="b">Minimum Payment</Box>
                                            {'\n'}
                                            The smallest amount you are required to pay by the due date to keep your
                                            account in good standing. Paying only the minimum will result in interest
                                            charges on the remaining balance.
                                            {'\n\n'}
                                            For additional information please see your Card Holder Agreement.
                                        </Typography>
                                    </Box>
                                }
                            >
                                <IconButton
                                    onClick={openTooltip}
                                    aria-label="Payment options information"
                                    color="primary"
                                >
                                    {tooltipOpen ? (
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-xmark-circle"
                                            fontSize="small"
                                        />
                                    ) : (
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-info-circle"
                                            fontSize="small"
                                        />
                                    )}
                                </IconButton>
                            </Tooltip>
                        </Box>
                    </ClickAwayListener>
                </Typography>
                <Typography fontSize={14}>How much would you like to schedule?</Typography>
                <RadioGroup
                    value={paymentState.methodType}
                    onChange={evt =>
                        setPaymentState({
                            type: 'changeMethodType',
                            payload: evt.target.value as AutopayOptionsState['methodType'],
                        })
                    }
                    name="payment-options"
                    sx={{gap: 2}}
                >
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'lastStatementBalance'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio checked={paymentState.methodType === 'lastStatementBalance'} />}
                            value={'lastStatementBalance'}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Statement Balance</Typography>
                                </Grid>
                            }
                            sx={{width: '100%'}}
                            disabled={noPaymentMethods}
                        />
                    </Card>
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'minimumPaymentDue'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio checked={paymentState.methodType === 'minimumPaymentDue'} />}
                            value={'minimumPaymentDue'}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Minimum Payment Due</Typography>
                                </Grid>
                            }
                            sx={{width: '100%'}}
                            disabled={noPaymentMethods}
                        />
                    </Card>
                    {/* <Card
                        sx={{
                            p: 2,
                            backgroundColor:
                                noPaymentMethods
                                    ? 'var(--mui-palette-action-disabled)'
                                    : paymentState.methodType === 'currentBalance'
                                      ? 'var(--mui-palette-primary-light)'
                                      : 'inherit',
                        }}
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio checked={paymentState.methodType === 'currentBalance'} />}
                            value={'currentBalance'}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Current Balance</Typography>
                                </Grid>
                            }
                            sx={{width: '100%'}}
                            disabled={noPaymentMethods}
                        />
                    </Card> */}
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'fixed'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio checked={paymentState.methodType === 'fixed'} />}
                            value={'fixed'}
                            disabled={noPaymentMethods}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Fixed Amount</Typography>
                                    <OutlinedInput
                                        startAdornment={<InputAdornment position="start">$</InputAdornment>}
                                        sx={{backgroundColor: 'var(--mui-palette-common-white)'}}
                                        size="small"
                                        value={manualAmount}
                                        aria-label="Enter payment amount"
                                        type="number"
                                        inputMode="decimal"
                                        onFocus={() =>
                                            setPaymentState({
                                                type: 'changeMethodType',
                                                payload: 'fixed',
                                            })
                                        }
                                        onChange={evt => {
                                            const value = evt.target.value
                                            // Allow empty string for clearing
                                            if (value === '') {
                                                setManualAmount('')
                                                setPaymentState({
                                                    type: 'changeAmount',
                                                    payload: 0,
                                                })
                                                return
                                            }
                                            // Only allow valid numbers greater than or equal to 0
                                            const regex = /^\d*(\.?\d*)?$/
                                            if (!regex.test(value)) return
                                            if (Number(value) < 0) return
                                            setManualAmount(value)
                                            setPaymentState({
                                                type: 'changeAmount',
                                                payload: Number(value) * 100,
                                            })
                                        }}
                                        slotProps={{
                                            input: {
                                                type: 'number',
                                                inputMode: 'decimal',
                                                step: 1,
                                                'aria-label': 'Enter payment amount',
                                            },
                                        }}
                                    />
                                </Grid>
                            }
                            sx={{width: '100%'}}
                        />
                    </Card>
                </RadioGroup>
            </Grid>
        </>
    )
}
