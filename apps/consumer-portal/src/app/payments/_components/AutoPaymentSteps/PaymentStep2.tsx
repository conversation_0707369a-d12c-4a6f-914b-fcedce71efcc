'use client'

import {Grid, Typography, Select, MenuItem, Alert, Icon, IconButton} from '@mui/material'
import type {AutopayOptionsState, PaymentOptionsAction} from '../../_types'
import {getOrdinalSuffix} from './utils'

export default function PaymentStep2({
    paymentState,
    setPaymentState,
    dueDate,
    onBack,
}: {
    paymentState: AutopayOptionsState
    setPaymentState: (value: PaymentOptionsAction<AutopayOptionsState>) => void
    dueDate: number
    onBack?: () => void
}) {
    function handleDateChange(day: number) {
        setPaymentState({
            type: 'changePaymentDate',
            payload: day,
        })
    }

    // Calculate the next payment date for display in cancellation confirmation
    const getNextPaymentDate = () => {
        if (!paymentState || !paymentState.paymentDate) return undefined

        const now = new Date()
        const currentMonth = now.getMonth()
        const currentYear = now.getFullYear()

        // Create date for this month's payment
        let nextPaymentDate = new Date(currentYear, currentMonth, paymentState.paymentDate)

        // If this date has passed, use next month
        if (nextPaymentDate < now) {
            nextPaymentDate = new Date(currentYear, currentMonth + 1, paymentState.paymentDate)
        }

        // Format the date
        return nextPaymentDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        })
    }

    return (
        <Grid container gap={2} flexDirection="column" flex={1}>
            <Typography fontSize={24} fontWeight={600} display="flex" alignItems="center" gap={1}>
                {onBack && (
                    <IconButton onClick={onBack} aria-label="Back to main view" size="small" sx={{p: 0.5}}>
                        <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />
                    </IconButton>
                )}
                Payment Date
            </Typography>

            <Grid container gap={3} flexDirection="column">
                <Grid
                    container
                    gap={1}
                    flexDirection="column"
                    padding={1}
                    sx={{backgroundColor: 'var(--mui-palette-background-default)', borderRadius: 1}}
                >
                    <Typography color="text.secondary">
                        Your due date is the {dueDate}
                        {getOrdinalSuffix(dueDate)} of each month.
                    </Typography>

                    <Typography color="text.secondary">
                        Autopay has to be set at least one day before the payment due date.
                    </Typography>

                    <Typography color="text.secondary">Payments will repeat on the same day each month.</Typography>
                </Grid>

                <Typography fontWeight={600}>Schedule my recurring payment on the</Typography>
                <Grid container gap={2} alignItems="center">
                    <Select
                        value={paymentState.paymentDate}
                        onChange={evt => handleDateChange(Number(evt.target.value))}
                        sx={{flex: '0 0 45%'}}
                        size="small"
                    >
                        {Array.from({length: dueDate - 1}, (_, i) => i + 1).map(day => (
                            <MenuItem key={day} value={day} disabled={day > dueDate}>
                                {day}
                                {getOrdinalSuffix(day)}
                            </MenuItem>
                        ))}
                    </Select>

                    <Typography fontWeight={600} sx={{flex: '0 0 45%'}}>
                        of each month
                    </Typography>
                </Grid>

                <Alert
                    severity="success"
                    sx={{
                        backgroundColor: 'var(--mui-palette-success-light)',
                        '& .MuiAlert-message': {
                            width: '100%',
                            textAlign: 'center',
                        },
                    }}
                    icon={false}
                >
                    <Typography>Your first auto-payment will process on </Typography>
                    <Typography color="success.dark" fontWeight={600}>
                        {getNextPaymentDate()}
                    </Typography>
                </Alert>
            </Grid>

            <Typography color="text.secondary" fontSize={12} sx={{flex: 1}}>
                If your selected payment date ever falls on a non-banking day, we will process it one day earlier.
            </Typography>
        </Grid>
    )
}
