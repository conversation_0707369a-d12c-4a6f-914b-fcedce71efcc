'use client'

import {Grid, Typography, Button} from '@mui/material'
import Link from 'next/link'
import type {AutopayOptionsState} from '../../_types'
import SuccessAnimation from '@/components/SuccessAnimation'

export default function PaymentStep5({paymentState}: {paymentState: AutopayOptionsState}) {
    // Calculate next payment date
    const nextPaymentDate = new Date()
    nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1)
    nextPaymentDate.setDate(paymentState.paymentDate ?? 1)

    const formattedNextPayment = nextPaymentDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    })

    return (
        <Grid container gap={2} flexDirection="column" flex={1} sx={{minHeight: 300}} alignItems="center">
            <SuccessAnimation />
            <Typography>Your first auto-payment will processed on</Typography>
            <Typography color="success.dark" fontWeight={600}>
                {formattedNextPayment}
            </Typography>
            <Grid container justifyContent="center" sx={{alignSelf: 'flex-end', width: '100%', mt: 4}}>
                <Button component={Link} href="/" variant="contained" fullWidth sx={{maxWidth: '35%'}}>
                    Back to Dashboard
                </Button>
            </Grid>
        </Grid>
    )
}
