'use client'

import {Grid, Typography, Button, Icon} from '@mui/material'
import Link from 'next/link'

interface CancellationConfirmationProps {
    nextPaymentDate?: string // Optional next payment date to display
}

export default function CancellationConfirmation({nextPaymentDate}: CancellationConfirmationProps) {
    // Format the date if provided, otherwise use a placeholder
    const formattedDate = nextPaymentDate || 'January 9th, 2025'

    return (
        <Grid container flexDirection="column" flex={1}>
            <Grid container flexDirection="column" gap={2} sx={{py: 3}}>
                <Typography variant="body1" lineHeight={1.6}>
                    Auto-pay has been cancelled.
                </Typography>

                <Typography variant="body1" lineHeight={1.6}>
                    Your next scheduled payment on {formattedDate} has also been cancelled. Please be sure to schedule a
                    payment manually.
                </Typography>
            </Grid>

            <Grid container sx={{mt: 'auto', mb: 3}}>
                <Button
                    component={Link}
                    href="/"
                    variant="contained"
                    color="primary"
                    fullWidth
                    sx={{
                        borderRadius: 28,
                        textTransform: 'uppercase',
                        py: 1.5,
                        backgroundColor: '#1565c0',
                        fontWeight: 500,
                        letterSpacing: '0.5px',
                    }}
                >
                    <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" sx={{mr: 1}} />
                    Back to my dashboard
                </Button>
            </Grid>
        </Grid>
    )
}
