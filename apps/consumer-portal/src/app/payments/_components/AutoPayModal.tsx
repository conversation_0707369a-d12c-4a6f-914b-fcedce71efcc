'use client'
import {useRouter} from 'next/navigation'
import {useReducer, useState} from 'react'

import {
    Dialog,
    DialogTitle,
    DialogContent,
    IconButton,
    useMediaQuery,
    type Theme,
    Icon,
    Grid,
    Button,
    Snackbar,
    Alert,
    AlertTitle,
    Typography,
} from '@mui/material'
import Stepper from '@/components/Stepper/Stepper'
import type {AutopayOptionsState, PaymentOptionsAction} from '../_types'
import PaymentStep1 from './AutoPaymentSteps/PaymentStep1'
import PaymentStep2 from './AutoPaymentSteps/PaymentStep2'
import PaymentStep3 from './AutoPaymentSteps/PaymentStep3'
import PaymentStep4 from './AutoPaymentSteps/PaymentStep4'
import PaymentStep5 from './AutoPaymentSteps/PaymentStep5'
import {createAutopay} from '@/actions/payments'

// Displayable steps. There are 5 screens, but only 3 are shown in the modal
const TOTAL_STEPS = 3

export default function AutoPayModal({open}: {open: boolean}) {
    const isDesktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))
    const router = useRouter()
    const [loading, setLoading] = useState(false)
    const [termsAccepted, setTermsAccepted] = useState(false)
    const [paymentError, setPaymentError] = useState(false)
    const [errorMessage, setErrorMessage] = useState<string>('')

    const [paymentState, dispatchPaymentUpdate] = useReducer(paymentStateReducer, {
        step: 1,
        methodType: 'lastStatementBalance',
        amount: 0,
        paymentAccount: '',
        paymentDate: 9, // Default to 9th of the month
        paymentStrategy: 'dayInMonth', // Hardcoded to dayInMonth for now, this will most likely come from a payment config strategy
    })

    function handleClose() {
        router.replace('/')
    }

    function handleNextStep() {
        dispatchPaymentUpdate({
            type: 'changeStep',
            payload: paymentState.step + 1,
        })
    }

    function handlePreviousStep() {
        dispatchPaymentUpdate({
            type: 'changeStep',
            payload: paymentState.step - 1,
        })
    }

    async function handleConfirmPayment() {
        setLoading(true)

        try {
            if (paymentState.methodType === '') {
                setErrorMessage('Please select a payment method')
                setPaymentError(true)
                return
            }
            const result = await createAutopay({
                amount: paymentState.amount,
                paymentMethodId: paymentState.paymentAccount,
                paymentDate: paymentState.paymentDate,
                amountType: paymentState.methodType,
                paymentStrategy: paymentState.paymentStrategy,
            })

            if (!result.success) {
                console.error(result.error)
                setErrorMessage(result.error)
                setPaymentError(true)
                return
            }

            // Success - move to confirmation step
            dispatchPaymentUpdate({
                type: 'changeStep',
                payload: 5,
            })
        } catch (error) {
            console.error(error)
            setErrorMessage(
                error instanceof Error
                    ? error.message
                    : 'Your payment could not be posted at this time. Please try again.',
            )
            setPaymentError(true)
        } finally {
            setLoading(false)
        }
    }

    // Determine if the next button should be disabled based on current step
    function isNextButtonDisabled() {
        switch (paymentState.step) {
            case 1:
                return !paymentState.methodType
            case 3:
                return !paymentState.paymentAccount
            case 4:
                return !termsAccepted
            default:
                return false
        }
    }

    return (
        <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md" fullScreen={!isDesktop}>
            {paymentState.step === 5 ? (
                <DialogTitle>Auto-Pay Confirmed!</DialogTitle>
            ) : (
                <DialogTitle>Set Up Auto-Pay</DialogTitle>
            )}
            <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={handleClose}>
                <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
            </IconButton>
            <DialogContent
                sx={{display: 'flex', flexDirection: 'column', gap: 2, flex: 1, px: isDesktop ? 15 : 2, pb: 8}}
            >
                <Stepper steps={TOTAL_STEPS} currentStep={paymentState.step} />

                {paymentState.step === 1 && (
                    <PaymentStep1 paymentState={paymentState} setPaymentState={dispatchPaymentUpdate} />
                )}
                {paymentState.step === 2 && (
                    <PaymentStep2 paymentState={paymentState} setPaymentState={dispatchPaymentUpdate} dueDate={21} />
                )}
                {paymentState.step === 3 && (
                    <PaymentStep3 paymentState={paymentState} setPaymentState={dispatchPaymentUpdate} />
                )}
                {paymentState.step === 4 && (
                    <PaymentStep4
                        paymentState={paymentState}
                        setPaymentState={dispatchPaymentUpdate}
                        termsAccepted={termsAccepted}
                        setTermsAccepted={setTermsAccepted}
                    />
                )}
                {paymentState.step === 5 && <PaymentStep5 paymentState={paymentState} />}

                {/* Navigation buttons - don't show for step 5 */}
                {paymentState.step !== 5 &&
                    (isDesktop ? (
                        <Grid container gap={2} justifyContent="flex-end" flexDirection="row" sx={{mt: 4}}>
                            {paymentState.step > 1 && (
                                <Button
                                    variant="outlined"
                                    sx={{flex: '1 0 45%'}}
                                    onClick={handlePreviousStep}
                                    startIcon={
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-chevron-left"
                                            fontSize="small"
                                        />
                                    }
                                >
                                    Previous
                                </Button>
                            )}
                            {paymentState.step < 4 ? (
                                <Button
                                    variant="contained"
                                    sx={{flex: '1 0 45%'}}
                                    onClick={handleNextStep}
                                    disabled={isNextButtonDisabled()}
                                >
                                    Continue
                                </Button>
                            ) : (
                                <Button
                                    variant="contained"
                                    sx={{flex: '1 0 45%'}}
                                    onClick={handleConfirmPayment}
                                    disabled={isNextButtonDisabled()}
                                    loading={loading}
                                >
                                    Confirm
                                </Button>
                            )}
                        </Grid>
                    ) : (
                        <Grid container gap={2} justifyContent="flex-end" flexDirection="column" sx={{mt: 4}}>
                            {paymentState.step < 4 ? (
                                <Button
                                    variant="contained"
                                    fullWidth
                                    onClick={handleNextStep}
                                    disabled={isNextButtonDisabled()}
                                >
                                    Continue
                                </Button>
                            ) : (
                                <Button
                                    variant="contained"
                                    fullWidth
                                    onClick={handleConfirmPayment}
                                    disabled={isNextButtonDisabled()}
                                    loading={loading}
                                >
                                    Confirm
                                </Button>
                            )}
                            {paymentState.step > 1 && (
                                <Button
                                    variant="outlined"
                                    fullWidth
                                    onClick={handlePreviousStep}
                                    startIcon={
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-chevron-left"
                                            fontSize="small"
                                        />
                                    }
                                >
                                    Previous
                                </Button>
                            )}
                        </Grid>
                    ))}

                {/* Show error snackbar when payment fails */}
                {paymentError && (
                    <Snackbar
                        open={paymentError}
                        autoHideDuration={5000}
                        onClose={() => setPaymentError(false)}
                        anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                    >
                        <Alert
                            onClose={() => setPaymentError(false)}
                            severity="error"
                            variant="filled"
                            sx={{width: 350}}
                            icon={
                                <Icon
                                    baseClassName="fas"
                                    className="fa-solid fa-exclamation-triangle"
                                    fontSize="small"
                                />
                            }
                        >
                            <AlertTitle>Error occurred</AlertTitle>
                            <Typography>{errorMessage}</Typography>
                        </Alert>
                    </Snackbar>
                )}
            </DialogContent>
        </Dialog>
    )
}

function paymentStateReducer(
    state: AutopayOptionsState,
    action: PaymentOptionsAction<AutopayOptionsState>,
): AutopayOptionsState {
    switch (action.type) {
        case 'changeStep': {
            return {...state, step: action.payload}
        }
        case 'changeMethodType': {
            return {...state, methodType: action.payload}
        }
        case 'changeAmount': {
            return {...state, amount: action.payload}
        }
        case 'changePaymentAccount': {
            return {...state, paymentAccount: action.payload}
        }
        case 'changePaymentDate': {
            return {...state, paymentDate: action.payload}
        }
        default:
            return state
    }
}
