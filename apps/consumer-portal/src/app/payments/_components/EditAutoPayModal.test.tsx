import React from 'react'
import {render, screen, waitFor, fireEvent} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest'
import '@testing-library/jest-dom'
import EditAutoPayModal from './EditAutoPayModal'
import type {AutopayConfiguration} from '@/services/controllers/autopay'
import type {PaymentMethod} from '@/services/controllers/payments'
import * as paymentsActions from '@/actions/payments'

// Mock the Next.js router
const mockReplace = vi.fn()
vi.mock('next/navigation', () => ({
    useRouter: () => ({
        replace: mockReplace,
    }),
}))

// Mock the payment actions
vi.mock('@/actions/payments', () => ({
    updateAutopay: vi.fn().mockResolvedValue({success: true, data: true}),
    cancelAutopay: vi.fn().mockResolvedValue({success: true, data: true}),
}))

// Define mock payment methods
const mockPaymentMethods = [
    {
        paymentMethodId: 'pm_12345678901234',
        paymentMethodState: 'active',
        paymentMethodType: 'ACH',
        nickname: 'Bank of America',
        creditAccountId: 'ca_123',
        personId: 'person_123',
        createdOn: new Date('2023-01-01'),
        updatedOn: new Date('2023-01-01'),
        achDetails: {
            accountNumber: '********1234',
            routingNumber: '********5678',
            bankAccountType: 'CHECKING',
        },
        paymentMethodVerificationState: 'verified',
    },
    {
        paymentMethodId: 'pm_98765432109876',
        paymentMethodState: 'active',
        paymentMethodType: 'ACH',
        nickname: 'Chase Bank',
        creditAccountId: 'ca_123',
        personId: 'person_123',
        createdOn: new Date('2023-01-01'),
        updatedOn: new Date('2023-01-01'),
        achDetails: {
            accountNumber: '********5678',
            routingNumber: '********1234',
            bankAccountType: 'CHECKING',
        },
        paymentMethodVerificationState: 'verified',
    },
]

// Define mock autopay config
const mockAutopayConfig = {
    autopayConfigurationId: 'apc_123456789',
    state: 'active',
    creditAccountId: 'ca_123',
    amountType: 'minimumPaymentDue',
    amount: {
        amount: 5000,
        currency: 'USD',
    },
    autopayStrategy: {
        type: 'dayInMonth',
        value: 9,
    },
    paymentMethodId: 'pm_12345678901234',
    createdOn: new Date('2023-01-01'),
    updatedOn: new Date('2023-01-01'),
}

// Mock React Query hooks
vi.mock('@/hooks/usePaymentData', () => ({
    usePaymentAccountsData: () => ({
        data: mockPaymentMethods,
        isLoading: false,
        error: null,
    }),
    usePaymentConfigData: () => ({
        data: {
            paymentMethodVerificationProvider: 'MX',
            microDepositsEnabled: true,
        },
        isLoading: false,
        error: null,
    }),
}))

// Mock the useAutopayConfigurationData hook
vi.mock('@/hooks/useAutopayConfigurationData', () => ({
    useAutopayConfiguration: () => ({
        data: [mockAutopayConfig],
        isLoading: false,
        error: null,
    }),
}))

// Mock MUI useMediaQuery
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => true, // Default to desktop view
    }
})

describe('EditAutoPayModal', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    afterEach(() => {
        vi.restoreAllMocks()
    })

    it('renders the modal when open', () => {
        render(<EditAutoPayModal open={true} />)

        expect(screen.getByText('Manage Auto-Pay')).toBeInTheDocument()
        expect(screen.getByText('Your Auto-Pay Details')).toBeInTheDocument()
        expect(screen.getByText('Payment Amount')).toBeInTheDocument()
        expect(screen.getByText('Minimum Payment')).toBeInTheDocument()
        expect(screen.getByText('Payment Date')).toBeInTheDocument()
        expect(screen.getByText('9th Day of Each Month')).toBeInTheDocument()
        expect(screen.getByText('Payment Method')).toBeInTheDocument()

        // Test for payment method display with account number
        expect(screen.getByText(/Bank of America ••••1234/)).toBeInTheDocument()

        // Check that the initial Cancel Auto-Pay button is present
        expect(screen.getByRole('button', {name: /CANCEL AUTO-PAY/i})).toBeInTheDocument()

        // Check that edit icons are present
        expect(screen.getByLabelText('Edit payment amount')).toBeInTheDocument()
        expect(screen.getByLabelText('Edit payment date')).toBeInTheDocument()
        expect(screen.getByLabelText('Edit payment method')).toBeInTheDocument()
    })

    it('does not render the modal when closed', () => {
        render(<EditAutoPayModal open={false} />)

        expect(screen.queryByText('Manage Auto-Pay')).not.toBeInTheDocument()
    })

    it('navigates to payment amount step when edit amount button is clicked', async () => {
        const user = userEvent.setup()
        render(<EditAutoPayModal open={true} />)

        const editAmountButton = screen.getByLabelText('Edit payment amount')
        await user.click(editAmountButton)

        // Should now be on the payment amount step
        expect(screen.getByText('Payment Amount')).toBeInTheDocument()
        expect(screen.getByText('How much would you like to schedule?')).toBeInTheDocument()
    })

    it('navigates to payment date step when edit date button is clicked', async () => {
        const user = userEvent.setup()
        render(<EditAutoPayModal open={true} />)

        const editDateButton = screen.getByLabelText('Edit payment date')
        await user.click(editDateButton)

        // Should now be on the payment date step
        expect(screen.getByText('Payment Date')).toBeInTheDocument()
        expect(screen.getByText('Schedule my recurring payment on the')).toBeInTheDocument()
        expect(screen.getByText('of each month')).toBeInTheDocument()
    })

    it('navigates to payment method step when edit method button is clicked', async () => {
        const user = userEvent.setup()
        render(<EditAutoPayModal open={true} />)

        const editMethodButton = screen.getByLabelText('Edit payment method')
        await user.click(editMethodButton)

        // Should now be on the payment method step
        expect(screen.getByText('Payment Method')).toBeInTheDocument()
        expect(screen.getByText('Where would you like to provide payment from?')).toBeInTheDocument()
    })

    it('returns to initial view when back button is clicked on payment amount step', async () => {
        const user = userEvent.setup()
        render(<EditAutoPayModal open={true} />)

        // First navigate to the payment amount step
        const editAmountButton = screen.getByLabelText('Edit payment amount')
        await user.click(editAmountButton)

        // Now click the back button
        const backButton = screen.getByLabelText('Back to main view')
        await user.click(backButton)

        // Should be back at the initial view
        expect(screen.getByText('Your Auto-Pay Details')).toBeInTheDocument()
    })

    // More complex tests - skipped for now
    it.skip('calls updateAutopay when payment amount is changed and update is clicked', async () => {
        const user = userEvent.setup()
        render(<EditAutoPayModal open={true} />)

        // Navigate to payment amount step
        const editAmountButton = screen.getByLabelText('Edit payment amount')
        await user.click(editAmountButton)

        // Change payment amount to "lastStatementBalance" using fireEvent instead of userEvent
        const radioButtons = screen.getAllByRole('radio')
        fireEvent.click(radioButtons[0]) // First radio should be Statement Balance

        // Click update button
        const updateButton = screen.getByRole('button', {name: /UPDATE/i})
        await user.click(updateButton)

        // Verify updateAutopay was called with correct params
        expect(paymentsActions.updateAutopay).toHaveBeenCalledWith(
            'apc_123456789',
            expect.objectContaining({
                amountType: 'lastStatementBalance',
            }),
        )

        // Should return to initial view
        await waitFor(() => {
            expect(screen.getByText('Your Auto-Pay Details')).toBeInTheDocument()
        })
    })

    it.skip('calls updateAutopay when payment date is changed and update is clicked', async () => {
        // Implementation omitted - can be filled in later if needed
    })

    it.skip('calls updateAutopay when payment method is changed and update is clicked', async () => {
        // Implementation omitted - can be filled in later if needed
    })

    it.skip('does not call updateAutopay when no changes are made', async () => {
        // Implementation omitted - can be filled in later if needed
    })

    it.skip('shows cancellation confirmation when auto-pay is cancelled', async () => {
        // Implementation omitted - can be filled in later if needed
    })

    it.skip('shows error message when cancel fails', async () => {
        // Implementation omitted - can be filled in later if needed
    })

    it.skip('shows error message when update fails', async () => {
        // Implementation omitted - can be filled in later if needed
    })
})
