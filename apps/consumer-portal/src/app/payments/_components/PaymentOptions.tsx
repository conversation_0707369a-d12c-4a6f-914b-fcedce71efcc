'use client'

import {useReducer} from 'react'
import {Grid} from '@mui/material'
import type {Balances} from '@/services/controllers/balances'
import type {PaymentOptionsState, PaymentOptionsAction} from '../_types'
import {PaymentStep0, PaymentStep1, PaymentStep2, PaymentStep3} from './PaymentSteps/Steps'
import {useBalanceData} from '@/hooks/useBalanceData'

export default function PaymentOptions() {
    const {data: balances} = useBalanceData()
    const [paymentState, dispatchPaymentUpdate] = useReducer(paymentStateReducer, {
        step: 0,
        methodType: '',
        amount: 0,
        paymentAccount: '',
        paymentDate: 9,
    })

    return (
        <Grid container flexDirection="column" gap={1} flex={1}>
            {paymentState.step === 0 && (
                <PaymentStep0 balances={balances} paymentState={paymentState} setPaymentState={dispatchPaymentUpdate} />
            )}
            {paymentState.step === 1 && (
                <PaymentStep1 paymentState={paymentState} setPaymentState={dispatchPaymentUpdate} />
            )}
            {paymentState.step === 2 && (
                <PaymentStep2 paymentState={paymentState} setPaymentState={dispatchPaymentUpdate} />
            )}
            {paymentState.step === 3 && <PaymentStep3 paymentState={paymentState} />}
        </Grid>
    )
}

function paymentStateReducer(
    state: PaymentOptionsState,
    action: PaymentOptionsAction<PaymentOptionsState>,
): PaymentOptionsState {
    switch (action.type) {
        case 'changeStep': {
            return {...state, step: action.payload}
        }
        case 'changeMethodType': {
            return {...state, methodType: action.payload}
        }
        case 'changeAmount': {
            return {...state, amount: action.payload}
        }
        case 'changePaymentAccount': {
            return {...state, paymentAccount: action.payload}
        }
        default:
            return state
    }
}
