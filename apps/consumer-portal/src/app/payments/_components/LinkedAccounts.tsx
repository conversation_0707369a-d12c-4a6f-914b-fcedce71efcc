'use client'

import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>er,
    Fab,
    Grid,
    Icon,
    IconButton,
    List,
    ListItem,
    Typography,
    useMediaQuery,
} from '@mui/material'
import type {Theme} from '@mui/material'
import type {PaymentMethod} from '@/services/controllers/payments'
import {useState, useMemo} from 'react'
import AddPaymentAccountModal from './AddPaymentAccountModal'
import VerifyPaymentAccountModal from './VerifyPaymentAccountModal'
import RemovePaymentMethodModal from './RemovePaymentMethodModal'
import {usePaymentAccountsData, usePaymentConfigData} from '@/hooks/usePaymentData'

export default function LinkedAccounts() {
    const [showLinkedAccountsDrawer, setShowLinkedAccountsDrawer] = useState(false)
    const {isLoading} = usePaymentAccountsData()

    return (
        <>
            <Grid
                container
                flexDirection="column"
                flex="0 1 74px"
                alignItems="center"
                sx={{cursor: 'pointer'}}
                onClick={() => setShowLinkedAccountsDrawer(true)}
            >
                <IconButton
                    size="large"
                    sx={{
                        backgroundColor: 'var(--mui-palette-primary-main)',
                        color: 'white',
                        height: 64,
                        width: 64,
                        mb: 0.5,
                        '&:hover': {backgroundColor: 'var(--mui-palette-primary-light)'},
                    }}
                    disabled={showLinkedAccountsDrawer}
                >
                    <Icon
                        baseClassName="fas"
                        className="fa-solid fa-credit-card"
                        fontSize="large"
                        sx={{height: 'initial', width: 'initial'}}
                    />
                </IconButton>
                <Typography
                    fontSize={12}
                    fontWeight={400}
                    textAlign="center"
                    color={showLinkedAccountsDrawer ? 'textSecondary' : 'info'}
                >
                    My Linked Accounts
                </Typography>
            </Grid>
            {!isLoading && (
                <AccountsDrawer
                    open={showLinkedAccountsDrawer}
                    handleClose={() => setShowLinkedAccountsDrawer(false)}
                />
            )}
        </>
    )
}

function AccountDetails({account}: {account: Partial<PaymentMethod>}) {
    const lastFourDigits = account.achDetails?.accountNumber?.slice(-4) ?? '****'
    return (
        <Grid
            container
            flexDirection="column"
            flex={1}
            justifyContent="center"
            sx={{
                opacity: account.paymentMethodVerificationState !== 'verified' ? 0.5 : 1,
            }}
        >
            <Typography
                fontSize={16}
                fontWeight={600}
                lineHeight="157%"
                color="textSecondary"
                aria-label="Account nickname"
            >
                {account.nickname ?? 'Payment Account'}
            </Typography>
            <Typography
                fontSize={20}
                fontWeight={600}
                lineHeight="160%"
                aria-label={`Account ending in ${lastFourDigits}`}
            >
                •••• {lastFourDigits}
            </Typography>
        </Grid>
    )
}

function AccountsDrawer({open, handleClose}: {open: boolean; handleClose: () => void}) {
    const {data: paymentMethods} = usePaymentAccountsData({select: data => data.paymentMethods})
    const {data: paymentConfig} = usePaymentConfigData()
    const sortedPaymentAccounts = paymentMethods?.sort((left, right) => {
        const leftDate = left.createdOn ?? new Date(0)
        const rightDate = right.createdOn ?? new Date(0)
        return leftDate < rightDate ? 1 : -1
    })
    const fullScreen = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    const [action, setAction] = useState<null | 'link' | 'remove' | 'verify'>(
        sortedPaymentAccounts?.length === 0 ? 'link' : null,
    )
    const [actionPaymentMethodId, setActionPaymentMethodId] = useState<PaymentMethod['paymentMethodId']>()

    const actionPaymentMethod = useMemo(() => {
        return sortedPaymentAccounts?.find(account => account.paymentMethodId === actionPaymentMethodId)
    }, [actionPaymentMethodId, sortedPaymentAccounts])

    function handleAction(action?: 'link' | 'remove' | 'verify', paymentAccountId?: PaymentMethod['paymentMethodId']) {
        setAction(action || null)
        setActionPaymentMethodId(paymentAccountId)
    }

    const extraSx = fullScreen
        ? {
              width: 375,
          }
        : {
              height: '100%',
          }

    return (
        <Drawer
            open={open}
            anchor={fullScreen ? 'right' : 'bottom'}
            sx={{flexShrink: 0, '& .MuiDrawer-paper': {...extraSx, boxSizing: 'border-box', p: 2}}}
            onClose={handleClose}
            variant="persistent"
        >
            <Grid container flex={1} flexDirection="column" gap={4}>
                <DrawerContentsWrapper handleClose={handleClose} handleAction={handleAction}>
                    <List dense disablePadding sx={{width: '100%', flex: 1, mt: 1}}>
                        {paymentMethods?.map(account => (
                            <ListItem key={`${account.paymentMethodId}`} divider disableGutters>
                                <Grid
                                    container
                                    flexDirection="row"
                                    sx={{width: '100%', px: 2}}
                                    gap={1}
                                    justifyContent="center"
                                >
                                    <AccountDetails account={account} />
                                    {account.paymentMethodVerificationState !== 'verified' ? (
                                        <Box sx={{flex: '0 0 100px', alignContent: 'center'}}>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                size="small"
                                                onClick={() => handleAction('verify', account.paymentMethodId)}
                                            >
                                                Verify now
                                            </Button>
                                        </Box>
                                    ) : null}
                                    <Box
                                        sx={{
                                            flex: '0 1 55px',
                                            color: 'var(--mui-palette-error-main)',
                                            textAlign: 'center',
                                            borderLeft: '1px solid var(--mui-palette-divider)',
                                            pl: 2,
                                        }}
                                    >
                                        <IconButton
                                            color="error"
                                            onClick={() => handleAction('remove', account.paymentMethodId)}
                                        >
                                            <Icon baseClassName="fas" className="fa-solid fa-trash" fontSize="small" />
                                        </IconButton>
                                        <Typography fontSize={12}>Remove</Typography>
                                    </Box>
                                </Grid>
                            </ListItem>
                        ))}
                    </List>
                </DrawerContentsWrapper>
                <AddPaymentAccountModal open={action === 'link'} onClose={handleAction} />
                <VerifyPaymentAccountModal
                    open={action === 'verify'}
                    onClose={handleAction}
                    paymentMethod={actionPaymentMethod}
                />
                <RemovePaymentMethodModal
                    open={action === 'remove'}
                    onClose={handleAction}
                    paymentMethodId={actionPaymentMethodId}
                    openAddAccountModal={() => handleAction('link')}
                />
            </Grid>
        </Drawer>
    )
}

function DrawerContentsWrapper({
    children,
    handleClose,
    handleAction,
}: {
    children: Readonly<React.ReactNode>
    handleClose: () => void
    handleAction: (action?: 'link' | 'remove' | 'verify', paymentMethodId?: string) => void
}) {
    const fullScreen = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    if (fullScreen) {
        return (
            <>
                <Grid container alignItems="center">
                    <IconButton onClick={handleClose}>
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
                    </IconButton>
                    <Typography variant="h6" flex={1}>
                        My Linked Accounts
                    </Typography>
                    <Fab
                        color="primary"
                        size="small"
                        aria-label="Link a new bank account"
                        onClick={() => handleAction('link')}
                    >
                        <Icon baseClassName="fas" className="fa-solid fa-plus" fontSize="small" />
                    </Fab>
                </Grid>
                {children}
            </>
        )
    } else {
        return (
            <>
                <Grid container alignItems="center">
                    <Typography variant="h6" flex={1}>
                        My Linked Accounts
                    </Typography>
                    <IconButton onClick={handleClose}>
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
                    </IconButton>
                </Grid>
                {children}
                <Grid container sx={{padding: '20px 24px'}} gap={2}>
                    <Button fullWidth variant="contained" onClick={() => handleAction('link')}>
                        Add A New Bank Account
                    </Button>
                    <Button
                        fullWidth
                        variant="text"
                        onClick={handleClose}
                        startIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />}
                    >
                        Back to Payments
                    </Button>
                </Grid>
            </>
        )
    }
}
