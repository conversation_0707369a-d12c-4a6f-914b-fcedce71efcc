'use client'

import {useState} from 'react'
import {removePaymentMethod} from '@/actions/payments'
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Box,
    Alert,
    Snackbar,
    useMediaQuery,
    type Theme,
    IconButton,
    Icon,
    Paper,
    Popover,
} from '@mui/material'
import {visuallyHidden} from '@mui/utils'
import type {PaymentMethod} from '@/services/controllers/payments'
import {usePaymentAccountsData} from '@/hooks/usePaymentData'
import {useAutopayConfiguration} from '@/hooks/useAutopayConfigurationData'
import {useQueryClient} from '@tanstack/react-query'

interface RemovePaymentMethodModalProps {
    open: boolean
    onClose: () => void
    paymentMethodId?: PaymentMethod['paymentMethodId']
    openAddAccountModal: () => void
}

export default function RemovePaymentMethodModal({
    open,
    onClose,
    paymentMethodId,
    openAddAccountModal,
}: RemovePaymentMethodModalProps) {
    const queryClient = useQueryClient()
    const {data: paymentMethods} = usePaymentAccountsData({select: data => data.paymentMethods})
    const {data: autoPayConfig} = useAutopayConfiguration()

    const isDesktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const hasAutoPay = autoPayConfig?.some(config => config.paymentMethodId === paymentMethodId)

    async function removePayment() {
        setLoading(true)
        setError(null)
        try {
            await removePaymentMethod(paymentMethodId!)
            queryClient.invalidateQueries({queryKey: ['paymentMethods']})
            onClose()
        } catch (error) {
            console.error('Failed to remove payment method:', error)
            setError('Unable to remove payment method. Please try again later.')
        } finally {
            setLoading(false)
        }
    }

    function handleCloseError() {
        setError(null)
    }

    if (paymentMethods?.length === 1) {
        return isDesktop ? (
            <>
                <Dialog
                    open={open}
                    onClose={onClose}
                    aria-labelledby="remove-payment-method-title"
                    aria-describedby="remove-payment-method-description"
                    maxWidth="sm"
                    fullWidth
                >
                    <Box id="remove-payment-method-description" style={visuallyHidden}>
                        Modal for removing a payment method
                    </Box>
                    <DialogTitle id="remove-payment-method-title">
                        <Typography fontWeight={600} fontSize={20} lineHeight="160%">
                            Unable to Remove Account
                        </Typography>
                        <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={onClose}>
                            <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
                        </IconButton>
                    </DialogTitle>
                    <DialogContent sx={{minHeight: 150}}>
                        <Typography fontSize={16} color="textSecondary">
                            You must have at least one bank account linked as a payment source. In order to remove this
                            account, you must first add another linked account.
                        </Typography>
                    </DialogContent>
                    <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                        <Button variant="text" onClick={onClose} fullWidth>
                            Ok
                        </Button>
                        <Button variant="outlined" color="success" fullWidth onClick={openAddAccountModal}>
                            Link a new bank account
                        </Button>
                    </DialogActions>
                </Dialog>
            </>
        ) : (
            <Popover
                open={open}
                onClose={onClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                slotProps={{
                    paper: {
                        sx: {
                            maxWidth: 340,
                        },
                    },
                }}
            >
                <Paper>
                    <DialogTitle>
                        <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                            Unable to Remove Account
                        </Typography>
                    </DialogTitle>
                    <DialogContent>
                        <Typography fontSize={16} lineHeight="150%">
                            You must have at least one bank account linked as a payment source. In order to remove this
                            account, you must first add another linked account.
                        </Typography>
                    </DialogContent>
                    <DialogActions sx={{justifyContent: 'space-between', gap: 1}}>
                        <Button variant="text" color="primary" onClick={onClose}>
                            Ok
                        </Button>
                        <Button variant="outlined" color="success" onClick={openAddAccountModal}>
                            Link a new bank account
                        </Button>
                    </DialogActions>
                </Paper>
            </Popover>
        )
    }

    return isDesktop ? (
        <>
            <Dialog
                open={open}
                onClose={onClose}
                aria-labelledby="remove-payment-method-title"
                aria-describedby="remove-payment-method-description"
                maxWidth="sm"
                fullWidth
            >
                <Box id="remove-payment-method-description" style={visuallyHidden}>
                    Modal for removing a payment method
                </Box>
                <DialogTitle id="remove-payment-method-title">
                    <Typography fontWeight={600} fontSize={20} lineHeight="160%">
                        Remove Linked Account
                    </Typography>
                    <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={onClose}>
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{minHeight: 150}}>
                    {hasAutoPay && (
                        <>
                            <Typography fontSize={16} color="textSecondary">
                                This linked account is being used for Autopay and{' '}
                                <b>removing it will immediately deactivate Autopay.</b>
                            </Typography>
                            <br />
                        </>
                    )}
                    <Typography fontSize={16} color="textSecondary">
                        Removing this linked account will immediately prevent you from using it for future payments.
                    </Typography>
                </DialogContent>
                <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                    <Button variant="outlined" color="error" loading={loading} onClick={removePayment} fullWidth>
                        Remove Account
                    </Button>
                    <Button variant="text" onClick={onClose}>
                        Keep Account
                    </Button>
                </DialogActions>
                <Snackbar
                    open={!!error}
                    autoHideDuration={6000}
                    onClose={handleCloseError}
                    anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                >
                    <Alert onClose={handleCloseError} severity="error" variant="filled" sx={{width: '100%'}}>
                        {error}
                    </Alert>
                </Snackbar>
            </Dialog>
        </>
    ) : (
        <Popover
            open={open}
            onClose={onClose}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'center',
            }}
            slotProps={{
                paper: {
                    sx: {
                        maxWidth: 340,
                    },
                },
            }}
        >
            <Paper>
                <DialogTitle>
                    <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                        Remove Linked Account
                    </Typography>
                </DialogTitle>
                <DialogContent>
                    <Typography fontSize={16} lineHeight="150%">
                        Removing this linked account will immediately prevent you from using it for future payments.
                    </Typography>
                </DialogContent>
                <DialogActions sx={{justifyContent: 'space-between', gap: 1}}>
                    <Button variant="text" color="primary" onClick={onClose}>
                        Keep Account
                    </Button>
                    <Button loading={loading} variant="outlined" color="error" onClick={removePayment}>
                        Remove Account
                    </Button>
                </DialogActions>
            </Paper>
        </Popover>
    )
}
