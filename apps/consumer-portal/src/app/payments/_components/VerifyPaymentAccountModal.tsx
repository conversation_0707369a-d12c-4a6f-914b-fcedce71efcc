'use client'

import {useState} from 'react'
import {
    Dialog,
    Box,
    DialogTitle,
    Grid,
    Typography,
    IconButton,
    DialogContent,
    FormControl,
    TextField,
    DialogActions,
    Button,
    Snackbar,
    Alert,
    AlertTitle,
    Icon,
} from '@mui/material'
import {visuallyHidden} from '@mui/utils'
import type {PaymentMethod} from '@tallied-technologies/services/Payments'
import {updatePaymentMethodVerificationForMicroDeposits} from '@/actions/payments'

export default function VerifyPaymentAccountModal({
    open,
    onClose,
    paymentMethod,
}: {
    open: boolean
    onClose: () => void
    paymentMethod?: Partial<PaymentMethod>
}) {
    const [deposits, setDeposits] = useState<{deposit1: string; deposit2: string}>({deposit1: '', deposit2: ''})
    const [showToast, setShowToast] = useState(false)

    async function handleConfirm() {
        if (paymentMethod && isValidDeposit(deposits.deposit1) && isValidDeposit(deposits.deposit2)) {
            try {
                const response = await updatePaymentMethodVerificationForMicroDeposits(
                    paymentMethod.paymentMethodId!,
                    deposits.deposit1,
                    deposits.deposit2,
                )
                console.log(response)

                setShowToast(true)
                onClose()
            } catch (er) {
                console.error(er)
            }
        }
    }

    return (
        <>
            <Dialog
                open={open}
                onClose={() => onClose()}
                fullWidth
                maxWidth="lg"
                aria-labelledby="verify-payment-account-title"
                aria-describedby="verify-payment-account-description"
            >
                <Box id="verify-payment-account-description" style={visuallyHidden}>
                    Modal for verifying your bank account using microdeposits
                </Box>
                <DialogTitle id="verify-payment-account-title">
                    <Grid container gap={2} flexDirection="column">
                        <Typography fontWeight={600} fontSize={20} lineHeight="160%">
                            Verify My Bank Account
                        </Typography>
                        <IconButton sx={{position: 'absolute', top: 10, right: 10}} onClick={() => onClose()}>
                            <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
                        </IconButton>
                    </Grid>
                </DialogTitle>
                <DialogContent>
                    <Grid container gap={2}>
                        <Typography fontSize={16} color="textSecondary">
                            Two deposits in the range of $0.01-$0.99 should have arrived in your account ending in{' '}
                            {paymentMethod?.achDetails?.accountNumber.slice(-4)}. Please enter the amounts of each.
                        </Typography>
                        <FormControl fullWidth>
                            <TextField
                                label="Deposit 1"
                                variant="outlined"
                                type="number"
                                slotProps={{
                                    input: {startAdornment: '$', type: 'number'},
                                }}
                                value={deposits.deposit1}
                                onChange={evt => {
                                    if (!/^\d*\.?\d{0,2}$/.test(evt.target.value)) return false
                                    setDeposits({...deposits, deposit1: evt.target.value})
                                }}
                            />
                        </FormControl>
                        <FormControl fullWidth>
                            <TextField
                                label="Deposit 2"
                                variant="outlined"
                                type="number"
                                slotProps={{
                                    input: {startAdornment: '$', type: 'number'},
                                }}
                                value={deposits.deposit2}
                                onChange={evt => {
                                    if (!/^\d*\.?\d{0,2}$/.test(evt.target.value)) return false
                                    setDeposits({...deposits, deposit2: evt.target.value})
                                }}
                            />
                        </FormControl>
                    </Grid>
                </DialogContent>
                <DialogActions sx={{p: [2.5, 3]}}>
                    <Button variant="contained" color="primary" onClick={handleConfirm} fullWidth>
                        Confirm Deposits
                    </Button>
                </DialogActions>
            </Dialog>
            <Snackbar
                open={!!showToast}
                onClose={() => setShowToast(false)}
                autoHideDuration={2000}
                anchorOrigin={{vertical: 'top', horizontal: 'center'}}
            >
                <Alert
                    severity="success"
                    variant="filled"
                    sx={{width: 350, backgroundColor: 'var(--mui-palette-primary-main)'}}
                    onClose={() => setShowToast(false)}
                >
                    <AlertTitle>Bank Account Verified</AlertTitle>
                </Alert>
            </Snackbar>
        </>
    )
}

function isValidDeposit(value: string) {
    return Number(value) >= 0.01 && Number(value) <= 0.99
}
