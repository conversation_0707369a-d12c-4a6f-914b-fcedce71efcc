import {Grid, Typography} from '@mui/material'
import LinkedAccounts from './LinkedAccounts'

export default async function PaymentsHeaderAndActions() {
    return (
        <Grid container flexDirection="row" gap={1} alignItems="center" sx={{mt: 1}}>
            <Grid container flexDirection="column" flex={1}>
                <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                    Payments
                </Typography>
                <Typography fontSize={14} lineHeight="24px" color="textSecondary">
                    Next Payment Due
                </Typography>
            </Grid>
            <LinkedAccounts />
        </Grid>
    )
}
