'use client'

import {
    <PERSON>alog<PERSON>ontent,
    Grid,
    Button,
    Typography,
    TextField,
    DialogActions,
    FormControl,
    FormHelperText,
    Divider,
    Checkbox,
    FormControlLabel,
    Link
} from '@mui/material'
import {useState} from 'react'

export function MicroDepositStep0({onClose, onContinue}: {onClose: () => void; onContinue?: () => void}) {
    return (
        <>
            <DialogContent>
                <Grid container gap={1}>
                    <Typography fontWeight={600} fontSize={16}>
                        Follow this simple process:
                    </Typography>
                    <ol>
                        <li>
                            <Typography fontSize={14}>Enter your account numbers</Typography>
                        </li>
                        <br />
                        <li>
                            <Typography fontSize={14}>Receive two deposits (May take up to 3 business days)</Typography>
                        </li>
                        <br />
                        <li>
                            <Typography fontSize={14}>Return to verify amounts</Typography>
                        </li>
                    </ol>
                    <Divider flexItem sx={{flex: 1}} />
                    <Typography variant="caption" fontSize={12} color="textSecondary">
                        By selecting “CONTINUE” you authorize “Company Name” to credit your account for the purpose of
                        account verification.
                    </Typography>
                </Grid>
            </DialogContent>
            <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={onContinue}
                    fullWidth
                    aria-label="Continue to account information"
                >
                    Continue
                </Button>
                <Button variant="text" onClick={onClose} aria-label="Cancel and close modal">
                    Cancel
                </Button>
            </DialogActions>
        </>
    )
}

export function MicroDepositStep1({
    onConnectMicrodeposits,
    onClose,
}: {
    onConnectMicrodeposits: (payload: {accountNumber: string; routingNumber: string; nickname: string}) => void
    onClose?: () => void
}) {
    const [state, setState] = useState({ accountNumber: '', routingNumber: '', nickname: '' })
    const [isChecked, setIsChecked] = useState(false)

    function handleCheckboxChange(event: React.ChangeEvent<HTMLInputElement>) {
        setIsChecked(event.target.checked)
    }

    function makeRequest() {
        onConnectMicrodeposits({
            accountNumber: state.accountNumber,
            routingNumber: state.routingNumber,
            nickname: state.nickname,
        })
    }

    return (
        <>
            <DialogContent>
                <Grid container gap={2}>
                    <Typography fontSize={16} fontWeight={700} lineHeight="150%" color="textSecondary">
                        Enter your account information
                    </Typography>
                    <Grid container gap={2}>
                        <FormControl fullWidth>
                            <TextField
                                label="Routing Number"
                                variant="outlined"
                                slotProps={{
                                    htmlInput: {pattern: '[0-9]{9}', inputMode: 'numeric', maxLength: 9, minLength: 9},
                                }}
                                required
                                value={state.routingNumber}
                                onChange={evt => setState(current => ({...current, routingNumber: evt.target.value}))}
                            />
                            <FormHelperText>
                                What is routing number? The routing number is the nine-digit number printed in the
                                bottom left corner of each check. Your specific account number (usually 10 to 12 digits)
                                is the second set of numbers printed on the bottom of your checks.
                            </FormHelperText>
                        </FormControl>
                        <FormControl fullWidth>
                            <TextField
                                label="Account Number"
                                variant="outlined"
                                slotProps={{
                                    htmlInput: {
                                        pattern: '[0-9]{4,20}',
                                        inputMode: 'numeric',
                                        maxLength: 20,
                                        minLength: 4,
                                    },
                                }}
                                required
                                value={state.accountNumber}
                                onChange={evt => setState(current => ({...current, accountNumber: evt.target.value}))}
                            />
                            <FormHelperText>
                                What is account number? The account number is located in the bottom center of your
                                personal check, just to the right of your routing number. The account number is the
                                unique identifier for your bank account.
                            </FormHelperText>
                        </FormControl>
                        <FormControl fullWidth>
                            <TextField
                                label="Account Nickname"
                                variant="outlined"
                                value={state.nickname}
                                onChange={evt => setState(current => ({...current, nickname: evt.target.value}))}
                            />
                        </FormControl>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={isChecked}
                                    onChange={handleCheckboxChange}
                                    name="agreement"
                                    color="primary"
                                    slotProps={{
                                        input: {
                                            'aria-label': 'Agree to privacy policy and terms of service',
                                        },
                                    }}
                                />
                            }
                            label={
                                <span>
                                    By clicking this box, you agree to our payment service provider Dwolla&apos;s{' '}
                                    <Link
                                        href="https://www.dwolla.com/legal/dwolla-account-terms-of-service"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        Terms of Service
                                    </Link>{' '}
                                    and{' '}
                                    <Link
                                        href="https://www.dwolla.com/legal/privacy"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        Privacy Policy
                                    </Link>{' '}
                                    and Cottonwood Payments&apos;{' '}
                                    <Link
                                        href="https://********.fs1.hubspotusercontent-na1.net/hubfs/********/FinWise%20Bank%20dba%20Cottonwood%20Payments%20Privacy%20Notice%20_%2001-2025.pdf"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        Privacy Policy
                                    </Link>
                                    .
                                </span>
                            }
                            sx={{alignItems: 'flex-start', mt: 1}}
                        />
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                <Button
                    variant="contained"
                    fullWidth
                    onClick={makeRequest}
                    aria-label="Submit and continue on to next step"
                    disabled={!isChecked}
                >
                    Submit
                </Button>
                <Button variant="text" fullWidth onClick={onClose} aria-label="Cancel and close modal">
                    Cancel
                </Button>
            </DialogActions>
        </>
    )
}

export function MicroDepositStep2({
    onComplete,
    createPayment,
}: {
    onComplete: () => void
    createPayment: () => Promise<void>
}) {
    const [loading, setLoading] = useState(false)

    return (
        <>
            <DialogContent>
                <Grid container gap={2}>
                    <Typography fontSize={16} fontWeight={700} lineHeight="150%" color="textSecondary">
                        We have received your information
                    </Typography>
                    <Typography fontSize={14}>
                        Please return in one to three business days. You&apos;ll verify that two deposits in the range
                        of $0.01 to $0.99 appeared in your account
                    </Typography>
                </Grid>
            </DialogContent>
            <DialogActions sx={{p: [2.5, 3]}}>
                <Button
                    loading={loading}
                    variant="contained"
                    color="primary"
                    onClick={async () => {
                        setLoading(true)
                        await createPayment()
                        onComplete()
                        setLoading(false)
                    }}
                    fullWidth
                    aria-label="Return to payments"
                >
                    Thank you
                </Button>
            </DialogActions>
        </>
    )
}
