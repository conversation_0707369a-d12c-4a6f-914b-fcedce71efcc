'use client'

import {prepareV<PERSON><PERSON><PERSON><PERSON><PERSON>} from '@/actions/payments'
import {
    DialogContent,
    Grid,
    Box,
    CircularProgress,
    Button,
    Typography,
    TextField,
    DialogActions,
    Divider,
    Checkbox,
    FormControlLabel,
    Link,
} from '@mui/material'

import {useState, useEffect} from 'react'
// @ts-ignore-next-line // Done on purpose as the mx widget does not export type defs
import {ConnectWidget} from '@mxenabled/web-widget-sdk'
import type {ConnectMemberConnectedPayload} from '@mxenabled/widget-post-message-definitions'
import type {AccountAction, AccountState} from '../../_types'

export function MxStep0({onClose, onContinue}: {onClose: () => void; onContinue?: () => void}) {
    return (
        <>
            <DialogContent>
                <Grid container gap={1}>
                    <Typography fontWeight={600} fontSize={16}>
                        “Company Name” uses MX to connect your account.
                    </Typography>
                    <ul>
                        <li>
                            <Typography fontSize={14}>
                                “Company Name” lets you <b>securely</b> connect your financial accounts in seconds.
                            </Typography>
                        </li>
                        <br />
                        <li>
                            <Typography fontSize={14}>
                                “Company Name” <b>does not sell personal information</b>, and will only use it with your
                                permission.
                            </Typography>
                        </li>
                    </ul>
                    <Divider flexItem sx={{flex: 1}} />
                    <Typography variant="caption" fontSize={12} color="textSecondary">
                        By selecting “CONTINUE” you agree to the{' '}
                        <Link href="https://www.mx.com/privacy-policy" target="_blank" rel="noopener noreferrer">
                            MX End User Privacy Policy.
                        </Link>
                    </Typography>
                </Grid>
            </DialogContent>
            <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                <Button variant="contained" color="primary" onClick={onContinue} fullWidth>
                    Continue
                </Button>
                <Button variant="text" onClick={onClose}>
                    Cancel
                </Button>
            </DialogActions>
        </>
    )
}

export function MxStep1({
    handleConnect,
    switchProvider,
}: {
    handleConnect: (payload: ConnectMemberConnectedPayload) => Promise<void>
    switchProvider?: () => void
}) {
    const [widgetUrl, setWidgetUrl] = useState<string>()
    const [isLoading, setIsLoading] = useState(false)

    async function handleVerify() {
        try {
            setIsLoading(true)
            const verificationResponse = await prepareVerificationVendor('mx')
            if (!verificationResponse.success) {
                console.log('show toast error')
                return
            }
            setWidgetUrl(verificationResponse.data.widgetUrl)
        } catch (er) {
            console.error('handleVerify error', er)
        } finally {
            setIsLoading(false)
        }
    }

    // Fetch the widget URL when the component mounts without adding extra dependencies.
    useEffect(() => {
        handleVerify()
    }, [])

    useEffect(() => {
        if (!widgetUrl) return

        const connectWidget = new ConnectWidget({
            container: '#mx-widget',
            url: widgetUrl,
            onMemberConnected: handleConnect,
        })

        return () => {
            connectWidget.unmount()
        }
    }, [widgetUrl, handleConnect])

    return (
        <DialogContent>
            <Grid container gap={2} flexDirection="column" alignItems="center">
                <Box id="mx-widget">
                    {isLoading ? (
                        <Box display="flex" justifyContent="center" alignItems="center" p={4}>
                            <CircularProgress size={24} />
                        </Box>
                    ) : null}
                </Box>
                <Typography variant="subtitle2" color="textSecondary">
                    Having issues connecting your account?
                </Typography>
                <Button variant="contained" fullWidth onClick={switchProvider}>
                    Link my bank another way
                </Button>
            </Grid>
        </DialogContent>
    )
}

export function MxStep2({
    accountState,
    updateAccountState,
    createPayment,
}: {
    accountState: AccountState
    updateAccountState: (action: AccountAction) => void
    createPayment: () => void
}) {
    const [loading, setLoading] = useState(false)
    const [isChecked, setIsChecked] = useState(false)

    function handleCheckboxChange(event: React.ChangeEvent<HTMLInputElement>) {
        setIsChecked(event.target.checked)
    }

    return (
        <>
            <DialogContent>
                <Grid container gap={2} flexDirection="column" sx={{pt: 1}}>
                    {accountState.workingProvider === 'dwolla' && (
                        <Typography variant="subtitle2" color="textSecondary">
                            Enter your account number
                        </Typography>
                    )}
                    <TextField
                        label="Account Number"
                        disabled
                        value={`••••••${accountState.mx?.account?.accountNumber?.slice(-4) ?? ''}`}
                        fullWidth
                    />
                    {accountState.workingProvider === 'dwolla' && (
                        <Typography variant="subtitle2" color="textSecondary">
                            Enter your routing number
                        </Typography>
                    )}
                    <TextField
                        label="Routing Number"
                        disabled
                        value={`••••••${accountState.mx?.account?.routingNumber?.slice(-4) ?? ''}`}
                        fullWidth
                    />
                    <Typography variant="subtitle2" color="textPrimary" sx={{mt: 2}}>
                        Enter your Account&apos;s Nickname
                    </Typography>
                    <TextField
                        label="Nickname"
                        value={accountState.mx?.nickname ?? ''}
                        onChange={evt =>
                            updateAccountState({type: 'updateMxAccountNickname', payload: evt.target.value})
                        }
                        fullWidth
                    />
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={isChecked}
                                onChange={handleCheckboxChange}
                                name="agreement"
                                color="primary"
                                slotProps={{
                                    input: {
                                        'aria-label': 'Agree to privacy policy and terms of service',
                                    },
                                }}
                            />
                        }
                        label={
                            <span>
                                By clicking this box, you agree to our payment service provider Dwolla&apos;s{' '}
                                <Link
                                    href="https://www.dwolla.com/legal/dwolla-account-terms-of-service"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    Terms of Service
                                </Link>{' '}
                                and{' '}
                                <Link
                                    href="https://www.dwolla.com/legal/privacy"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    Privacy Policy
                                </Link>{' '}
                                and Cottonwood Payments&apos;{' '}
                                <Link
                                    href="https://********.fs1.hubspotusercontent-na1.net/hubfs/********/FinWise%20Bank%20dba%20Cottonwood%20Payments%20Privacy%20Notice%20_%2001-2025.pdf"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    Privacy Policy
                                </Link>
                                .
                            </span>
                        }
                        sx={{alignItems: 'flex-start', mt: 1}}
                    />
                </Grid>
            </DialogContent>
            <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                <Button
                    loading={loading}
                    variant="contained"
                    color="primary"
                    onClick={async () => {
                        setLoading(true)
                        await createPayment()
                        updateAccountState({type: 'changeStep', payload: 3})
                        setLoading(false)
                    }}
                    fullWidth
                    disabled={!isChecked || loading}
                >
                    Continue
                </Button>
                <Button variant="text" onClick={() => updateAccountState({type: 'changeStep', payload: 1})} fullWidth>
                    Back
                </Button>
            </DialogActions>
        </>
    )
}

export function MxStep3({accountState, onComplete}: {accountState: AccountState; onComplete?: () => void}) {
    return (
        <>
            <DialogContent>
                <Typography variant="subtitle2" color="textSecondary">
                    Success! Your bank account has been linked successfully.
                </Typography>
                <Typography variant="body2" color="textSecondary" mt={2}>
                    Account ending in: {accountState.mx?.account?.accountNumber?.slice(-4)}
                </Typography>
            </DialogContent>
            <DialogActions sx={{flexDirection: 'column', gap: 2, p: [2.5, 3]}}>
                <Button variant="contained" color="primary" onClick={onComplete} fullWidth>
                    Return to Payments
                </Button>
            </DialogActions>
        </>
    )
}
