'use client'

import {useReducer, useEffect} from 'react'
import {Dialog, DialogTitle, DialogContent, Box, Typography, Grid, IconButton, Icon} from '@mui/material'
import {visuallyHidden} from '@mui/utils'
import {PaymentMethodVerificationProvider, type AchDetails} from '@/services/controllers/payments'
import {createPaymentMethod, getProcessorToken, verifyMxAccount} from '@/actions/payments'
import type {ConnectMemberConnectedPayload} from '@mxenabled/widget-post-message-definitions'
import {AccountState, AccountAction} from '../_types'
import {MxStep0, MxStep1, MxStep2, MxStep3} from './MxSteps/Steps'
import {MicroDepositStep0, MicroDepositStep1, MicroDepositStep2} from './MicroDepositSteps/Steps'
import {usePaymentConfigData} from '@/hooks/usePaymentData'
import {useQueryClient} from '@tanstack/react-query'

interface AddPaymentAccountModalProps {
    open: boolean
    onClose: () => void
}

export default function AddPaymentAccountModal({open, onClose}: AddPaymentAccountModalProps) {
    const queryClient = useQueryClient()
    const {data: paymentConfig} = usePaymentConfigData()

    const paymentMethodVerificationProvider =
        paymentConfig?.defaultPaymentMethodVerificationProvider ?? PaymentMethodVerificationProvider.Dwolla

    const [setupState, dispatchAccountUpdate] = useReducer(accountStateReducer, {
        step: 0,
        workingProvider: paymentMethodVerificationProvider,
    })

    useEffect(() => {
        dispatchAccountUpdate({type: 'changeProvider', payload: paymentMethodVerificationProvider})
    }, [paymentMethodVerificationProvider])

    // Reset the state machine when the modal closes
    function handleClose() {
        dispatchAccountUpdate({type: 'reset', payload: paymentMethodVerificationProvider})
        onClose()
    }

    async function handleConnect(payload: ConnectMemberConnectedPayload) {
        try {
            const accountDetails = await verifyMxAccount(payload.user_guid, payload.member_guid)
            if (!accountDetails.success || accountDetails.data.length === 0) {
                console.log('show toast error')
                return
            }

            dispatchAccountUpdate({type: 'updateMxAccount', payload: accountDetails.data[0]})
        } catch (er) {
            console.error('handleConnect error', er)
        }
    }

    async function addNewPaymentMethod() {
        const provider = setupState.workingProvider

        if (provider === 'mx' && setupState.mx !== undefined) {
            const token = await getProcessorToken(
                setupState.mx?.account?.accountGuid!,
                setupState.mx?.account?.memberGuid!,
                setupState.mx?.account?.userGuid!,
            )

            if (!token.success) {
                throw new Error('Unable to retrieve MX token')
            }

            await createPaymentMethod(
                'mx',
                setupState.mx?.nickname ?? 'Bank Account',
                setupState.mx?.account?.accountNumber!,
                setupState.mx?.account?.routingNumber!,
                'CHECKING',
                token.data,
            )
            queryClient.invalidateQueries({queryKey: ['paymentMethods']})
        } else if (provider === 'dwolla' && setupState.microdeposits !== undefined) {
            await createPaymentMethod(
                'dwolla',
                setupState.microdeposits?.nickname ?? 'Bank Account',
                setupState.microdeposits?.accountNumber!,
                setupState.microdeposits?.routingNumber!,
            )
            queryClient.invalidateQueries({queryKey: ['paymentMethods']})
        } else {
            throw new Error('Unknown Payment Provider, addPayment')
        }
    }

    return (
        <Dialog
            open={open}
            onClose={() => handleClose()}
            aria-labelledby="add-payment-account-title"
            aria-describedby="add-payment-account-description"
            maxWidth="sm"
            fullWidth
        >
            <Box id="add-payment-account-description" style={visuallyHidden}>
                Modal for linking your bank account using either instant verification or microdeposits
            </Box>
            <DialogTitle id="add-payment-account-title">
                <Grid container gap={2} flexDirection="column">
                    <Typography fontWeight={600} fontSize={20} lineHeight="160%">
                        Link My Bank Account
                    </Typography>
                    <IconButton sx={{position: 'absolute', top: 10, right: 10}} onClick={() => handleClose()}>
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
                    </IconButton>
                </Grid>
            </DialogTitle>
            {setupState.workingProvider === PaymentMethodVerificationProvider.Mx ? (
                setupState.step === 0 ? (
                    <MxStep0
                        onClose={() => handleClose()}
                        onContinue={() => dispatchAccountUpdate({type: 'changeStep', payload: 1})}
                    />
                ) : setupState.step === 1 ? (
                    <MxStep1
                        handleConnect={handleConnect}
                        switchProvider={() =>
                            dispatchAccountUpdate({
                                type: 'changeProvider',
                                payload: PaymentMethodVerificationProvider.Dwolla,
                            })
                        }
                    />
                ) : setupState.step === 2 ? (
                    <MxStep2
                        accountState={setupState}
                        createPayment={addNewPaymentMethod}
                        updateAccountState={dispatchAccountUpdate}
                    />
                ) : setupState.step === 3 ? (
                    <MxStep3 accountState={setupState} onComplete={handleClose} />
                ) : null
            ) : setupState.workingProvider === PaymentMethodVerificationProvider.Dwolla ? (
                setupState.step === 0 ? (
                    <MicroDepositStep0
                        onContinue={() => dispatchAccountUpdate({type: 'changeStep', payload: 1})}
                        onClose={() => handleClose()}
                    />
                ) : setupState.step === 1 ? (
                    <MicroDepositStep1
                        onConnectMicrodeposits={payload =>
                            dispatchAccountUpdate({type: 'updateMicrodepositsAccount', payload})
                        }
                        onClose={handleClose}
                    />
                ) : setupState.step === 2 ? (
                    <MicroDepositStep2 createPayment={addNewPaymentMethod} onComplete={handleClose} />
                ) : null
            ) : (
                <DialogContent>
                    <Typography>Unknown provider type. Please contact support.</Typography>
                </DialogContent>
            )}
        </Dialog>
    )
}

function validateProvider(provider: string): asserts provider is PaymentMethodVerificationProvider {
    if (!['dwolla', 'mx'].includes(provider)) {
        throw new Error(`Invalid provider ${provider}`)
    }
}

function validateStep(step: number): asserts step is AccountState['step'] {
    if (step < 0 || step > 3) {
        throw new Error(`Invalid step ${step}`)
    }
}

function accountStateReducer(state: AccountState, action: AccountAction): AccountState {
    switch (action.type) {
        case 'changeProvider':
            // We are going to reset everything back to the start with the new provider. Clear out any previous state
            validateProvider(action.payload)
            return {...state, workingProvider: action.payload, step: 0, mx: undefined, microdeposits: undefined}
        case 'changeStep':
            validateStep(action.payload)
            return {...state, step: action.payload}
        case 'updateMxAccount':
            return {...state, mx: {...state.mx, account: action.payload}, step: 2}
        case 'updateMxAccountNickname':
            return {...state, mx: {...state.mx, nickname: action.payload}}
        case 'updateMicrodepositsAccount':
            return {
                ...state,
                microdeposits: {
                    ...state.microdeposits,
                    accountNumber: action.payload.accountNumber,
                    routingNumber: action.payload.routingNumber,
                    nickname: action.payload.nickname,
                },
                step: 2,
            }
        case 'reset':
            return {
                ...state,
                step: 0,
                workingProvider: action.payload,
                mx: undefined,
                microdeposits: undefined,
            }
        default:
            return state
    }
}
