import {
    Box,
    Button,
    Card,
    FormControlLabel,
    Grid,
    IconButton,
    InputAdornment,
    OutlinedInput,
    Radio,
    RadioGroup,
    Typography,
    Snackbar,
    Alert,
    AlertTitle,
    Link,
    Icon,
} from '@mui/material'
import {PaymentOptionsState, type PaymentOptionsAction} from '../../_types'
import type {Balances, Money} from '@/services/controllers/balances'
import {useState} from 'react'
import {formatCurrency, flipNumberSign} from '@tallied-technologies/common'
import AddPaymentAccountModal from '../AddPaymentAccountModal'
import {makePayment} from '@/actions/payments'
import {useRouter} from 'next/navigation'
import SuccessAnimation from '@/components/SuccessAnimation'
import {usePaymentAccountsData} from '@/hooks/usePaymentData'
import {useQueryClient} from '@tanstack/react-query'

export function PaymentStep0({
    paymentState,
    setPaymentState,
    balances,
}: {
    paymentState: PaymentOptionsState
    setPaymentState: (value: PaymentOptionsAction<PaymentOptionsState>) => void
    balances: Balances | undefined
}) {
    const {data: paymentMethods} = usePaymentAccountsData()
    const [manualAmount, setManualAmount] = useState('0')
    const [addPaymentMethodOpen, setAddPaymentMethodOpen] = useState(false)

    const noPaymentMethods = paymentMethods?.paymentMethods?.length === 0

    function handleNextAction() {
        setPaymentState({
            type: 'changeStep',
            payload: 1,
        })
    }

    return (
        <>
            <Grid container gap={2} flexDirection="column" flex={1}>
                {noPaymentMethods && (
                    <Typography fontSize={16} color="textSecondary">
                        You currently do not have any linked bank accounts. Please{' '}
                        <Link onClick={() => setAddPaymentMethodOpen(true)}>link an account</Link> to make a payment.
                    </Typography>
                )}
                <Typography fontSize={24} fontWeight={600}>
                    Make a Payment
                </Typography>
                <RadioGroup
                    value={paymentState.methodType}
                    onChange={evt =>
                        setPaymentState({
                            type: 'changeMethodType',
                            payload: evt.target.value as PaymentOptionsState['methodType'],
                        })
                    }
                    name="payment-options"
                    sx={{gap: 2}}
                >
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'statementBalance'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        onClick={() =>
                            setPaymentState({
                                type: 'changeAmount',
                                payload: getAmount(balances?.outstandingStatementBalance),
                            })
                        }
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio />}
                            value={'statementBalance'}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Last Statement Balance</Typography>
                                    <Typography>
                                        {getNormalizedBalance(balances?.outstandingStatementBalance)}
                                    </Typography>
                                </Grid>
                            }
                            sx={{width: '100%'}}
                            disabled={noPaymentMethods}
                        />
                    </Card>
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'currentBalance'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        onClick={() =>
                            setPaymentState({
                                type: 'changeAmount',
                                payload: getAmount(balances?.outstandingBalance),
                            })
                        }
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio />}
                            value={'currentBalance'}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Outstanding Balance</Typography>
                                    <Typography>{getNormalizedBalance(balances?.outstandingBalance)}</Typography>
                                </Grid>
                            }
                            sx={{width: '100%'}}
                            disabled={noPaymentMethods}
                        />
                    </Card>
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'minimumPayment'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        onClick={() =>
                            setPaymentState({
                                type: 'changeAmount',
                                payload: getAmount(balances?.outstandingMinimumPayment),
                            })
                        }
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio />}
                            value={'minimumPayment'}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Minimum Payment</Typography>
                                    <Typography>{getNormalizedBalance(balances?.outstandingMinimumPayment)}</Typography>
                                </Grid>
                            }
                            sx={{width: '100%'}}
                            disabled={noPaymentMethods}
                        />
                    </Card>
                    <Card
                        sx={{
                            p: 2,
                            backgroundColor: noPaymentMethods
                                ? 'var(--mui-palette-action-disabled)'
                                : paymentState.methodType === 'manualAmount'
                                  ? 'var(--mui-palette-primary-light)'
                                  : 'inherit',
                        }}
                        elevation={2}
                    >
                        <FormControlLabel
                            control={<Radio />}
                            value={'manualAmount'}
                            disabled={noPaymentMethods}
                            label={
                                <Grid container flexDirection="column">
                                    <Typography color="textSecondary">Another Amount</Typography>
                                    <OutlinedInput
                                        startAdornment={<InputAdornment position="start">$</InputAdornment>}
                                        sx={{backgroundColor: 'var(--mui-palette-common-white)'}}
                                        size="small"
                                        value={manualAmount}
                                        aria-label="Enter payment amount"
                                        type="number"
                                        inputMode="decimal"
                                        onFocus={() =>
                                            setPaymentState({
                                                type: 'changeMethodType',
                                                payload: 'manualAmount',
                                            })
                                        }
                                        onChange={evt => {
                                            const value = evt.target.value
                                            // Allow empty string for clearing
                                            if (value === '') {
                                                setManualAmount('')
                                                setPaymentState({
                                                    type: 'changeAmount',
                                                    payload: 0,
                                                })
                                                return
                                            }
                                            // Only allow valid numbers greater than or equal to 0
                                            const regex = /^\d*(\.?\d*)?$/
                                            if (!regex.test(value)) return
                                            if (Number(value) < 0) return
                                            setManualAmount(value)
                                            setPaymentState({
                                                type: 'changeAmount',
                                                payload: Number(value) * 100,
                                            })
                                        }}
                                        slotProps={{
                                            input: {
                                                type: 'number',
                                                inputMode: 'decimal',
                                                step: 1,
                                                'aria-label': 'Enter payment amount',
                                            },
                                        }}
                                    />
                                </Grid>
                            }
                            sx={{width: '100%'}}
                        />
                    </Card>
                </RadioGroup>
            </Grid>
            <Box sx={{alignSelf: 'flex-end', width: '100%'}}>
                <Button
                    variant="contained"
                    fullWidth
                    disabled={
                        !paymentState.methodType ||
                        (paymentState.methodType === 'manualAmount' &&
                            (manualAmount === '' || isNaN(Number(manualAmount)) || Number(manualAmount) <= 0))
                    }
                    onClick={handleNextAction}
                >
                    Next Step
                </Button>
            </Box>
            <AddPaymentAccountModal open={addPaymentMethodOpen} onClose={() => setAddPaymentMethodOpen(false)} />
        </>
    )
}

export function PaymentStep1({
    paymentState,
    setPaymentState,
}: {
    paymentState: PaymentOptionsState
    setPaymentState: (value: PaymentOptionsAction<PaymentOptionsState>) => void
}) {
    const {data: paymentMethods} = usePaymentAccountsData()
    const [addPaymentMethodOpen, setAddPaymentMethodOpen] = useState(false)

    function handleNextAction() {
        setPaymentState({
            type: 'changeStep',
            payload: 2,
        })
    }

    return (
        <>
            <Grid container gap={2} flexDirection="column" flex={1}>
                <Grid container flexDirection="row" alignItems="center" gap={1}>
                    <IconButton
                        onClick={() => setPaymentState({type: 'changeStep', payload: 0})}
                        size="large"
                        sx={{p: 0}}
                    >
                        <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="large" />
                    </IconButton>
                    <Typography fontWeight={600} fontSize={24} lineHeight="30px">
                        Select a Payment Method
                    </Typography>
                </Grid>
                <RadioGroup
                    name="payment-options"
                    value={paymentState.paymentAccount}
                    sx={{gap: 2}}
                    onChange={evt => setPaymentState({type: 'changePaymentAccount', payload: evt.target.value})}
                >
                    {paymentMethods?.paymentMethods?.map(paymentMethod => (
                        <Card
                            key={paymentMethod.paymentMethodId}
                            sx={{
                                p: 2,
                                backgroundColor:
                                    paymentState.paymentAccount === paymentMethod.paymentMethodId
                                        ? 'var(--mui-palette-primary-light)'
                                        : 'inherit',
                            }}
                            elevation={2}
                        >
                            <FormControlLabel
                                control={<Radio />}
                                value={paymentMethod.paymentMethodId}
                                label={
                                    <Grid container flexDirection="column">
                                        <Typography color="textSecondary">{paymentMethod.nickname}</Typography>
                                        <Typography>
                                            •••• {paymentMethod.achDetails?.accountNumber.slice(-4)}
                                        </Typography>
                                    </Grid>
                                }
                                sx={{width: '100%'}}
                            />
                        </Card>
                    ))}
                </RadioGroup>
                <Button
                    variant="outlined"
                    startIcon={<Icon baseClassName="fas" className="fa-solid fa-plus" fontSize="small" />}
                    fullWidth
                    onClick={() => setAddPaymentMethodOpen(true)}
                >
                    Add a new Bank Account
                </Button>
            </Grid>
            <Grid container flexDirection="column" gap={3} sx={{alignSelf: 'flex-end', width: '100%'}}>
                <Button
                    disabled={paymentState.paymentAccount === ''}
                    onClick={handleNextAction}
                    variant="contained"
                    fullWidth
                    sx={{alignSelf: 'flex-end'}}
                >
                    Schedule My Payment
                </Button>
                <Button
                    variant="text"
                    fullWidth
                    onClick={() => setPaymentState({type: 'changeStep', payload: 0})}
                    startIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />}
                >
                    Previous
                </Button>
            </Grid>
            <AddPaymentAccountModal open={addPaymentMethodOpen} onClose={() => setAddPaymentMethodOpen(false)} />
        </>
    )
}

export function PaymentStep2({
    paymentState,
    setPaymentState,
}: {
    paymentState: PaymentOptionsState
    setPaymentState: (value: PaymentOptionsAction<PaymentOptionsState>) => void
}) {
    const {data: paymentMethods} = usePaymentAccountsData()
    const [loading, setLoading] = useState(false)
    const [paymentError, setPaymentError] = useState(false)

    async function handleMakePayment() {
        setLoading(true)
        const paymentMethodId = paymentState.paymentAccount
        const amount = paymentState.amount

        try {
            await makePayment(paymentMethodId, amount)
            setPaymentState({type: 'changeStep', payload: 3})
        } catch (error) {
            console.error(error)
            setPaymentError(true)
        } finally {
            setLoading(false)
        }
    }

    function closeToast() {
        setPaymentError(false)
    }

    const selectedPaymentMethod = paymentMethods?.paymentMethods?.find(
        account => account.paymentMethodId === paymentState.paymentAccount,
    )

    return (
        <>
            <Grid container gap={2} flexDirection="column" flex={1}>
                <Grid container flexDirection="row" alignItems="center" gap={1}>
                    <IconButton
                        onClick={() => setPaymentState({type: 'changeStep', payload: 1})}
                        size="large"
                        sx={{p: 0}}
                    >
                        <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="large" />
                    </IconButton>
                    <Typography fontWeight={600} fontSize={24} lineHeight="30px">
                        Review Payment Details
                    </Typography>
                </Grid>
                <Card sx={{p: 2}} elevation={2}>
                    <Grid container gap={2} flexDirection="column">
                        <Grid container flexDirection="row">
                            <Grid flex={1}>
                                <Typography fontSize={16} lineHeight="157%" color="textSecondary">
                                    Payment Amount
                                </Typography>
                                <Typography fontSize={24} fontWeight={600} lineHeight="175%">
                                    {formatCurrency(paymentState.amount / 100)}
                                </Typography>
                            </Grid>
                            <IconButton
                                sx={{alignSelf: 'center'}}
                                onClick={() => setPaymentState({type: 'changeStep', payload: 0})}
                            >
                                <Icon baseClassName="fas" className="fa-solid fa-edit" fontSize="small" />
                            </IconButton>
                        </Grid>
                        <Grid container flexDirection="row">
                            <Grid flex={1}>
                                <Typography fontSize={16} lineHeight="157%" color="textSecondary">
                                    Payment Method
                                </Typography>
                                <Typography fontSize={16} fontWeight={600} lineHeight="157%">
                                    {selectedPaymentMethod?.nickname}
                                </Typography>
                                <Typography fontSize={24} fontWeight={600} lineHeight="175%">
                                    •••• {selectedPaymentMethod?.achDetails?.accountNumber.slice(-4)}
                                </Typography>
                            </Grid>
                            <IconButton
                                sx={{alignSelf: 'center'}}
                                onClick={() => setPaymentState({type: 'changeStep', payload: 1})}
                            >
                                <Icon baseClassName="fas" className="fa-solid fa-edit" fontSize="small" />
                            </IconButton>
                        </Grid>
                    </Grid>
                </Card>
                <Typography variant="body2" fontSize={12} fontWeight={400}>
                    By clicking &quot;Submit Payment&quot;, I authorize Tallied to initiate an electronic payment from
                    my bank account indicated above and authorize my bank to honor the withdrawal.
                    <br />
                    <br />
                    Once submitted, payments cannot be cancelled by Tallied.
                    <br />
                    <br />
                    Payments will immediately reduce your balance by the payment amount, but will not impact your
                    available credit until we receive confirmation from your bank that the payment has been successfully
                    completed which may take up to 4 business days.
                </Typography>
                <Typography variant="body2" fontSize={12} fontWeight={400}>
                    Dwolla{' '}
                    <Link href="https://www.dwolla.com/legal/privacy" target="_blank" rel="noopener noreferrer">
                        Privacy Policy
                    </Link>{' '}
                    &{' '}
                    <Link
                        href="https://www.dwolla.com/legal/dwolla-account-terms-of-service"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        Terms of Service
                    </Link>
                </Typography>
            </Grid>
            <Grid container flexDirection="column" gap={3} sx={{alignSelf: 'flex-end', width: '100%'}}>
                <Button variant="contained" fullWidth loading={loading} onClick={handleMakePayment}>
                    Confirm Payment
                </Button>
                <Button
                    variant="text"
                    fullWidth
                    onClick={() => setPaymentState({type: 'changeStep', payload: 1})}
                    startIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />}
                >
                    Previous
                </Button>
            </Grid>
            <Snackbar
                open={paymentError}
                autoHideDuration={5000}
                onClose={closeToast}
                anchorOrigin={{vertical: 'top', horizontal: 'center'}}
            >
                <Alert
                    onClose={closeToast}
                    severity="error"
                    variant="filled"
                    sx={{
                        width: 350,
                    }}
                    icon={<Icon baseClassName="fas" className="fa-solid fa-exclamation-triangle" fontSize="small" />}
                >
                    <AlertTitle>Error occured.</AlertTitle>
                    <Typography>Your payment could not be posted at this time. Please try again.</Typography>
                </Alert>
            </Snackbar>
        </>
    )
}

export function PaymentStep3({paymentState}: {paymentState: PaymentOptionsState}) {
    const queryClient = useQueryClient()
    const router = useRouter()
    const {data: paymentMethods} = usePaymentAccountsData()

    const selectedPaymentMethod = paymentMethods?.paymentMethods?.find(
        account => account.paymentMethodId === paymentState.paymentAccount,
    )

    function handleBackToDashboard() {
        queryClient.invalidateQueries({queryKey: ['balance']})
        router.push('/')
    }

    return (
        <>
            <Grid container gap={2} flexDirection="column" flex={1}>
                <Typography fontWeight={600} fontSize={24} lineHeight="30px">
                    Payment Submitted!
                </Typography>
                <Grid container justifyContent="center" gap={3}>
                    <SuccessAnimation />
                    <Typography fontSize={16} fontWeight={400} textAlign="center">
                        Thank you for submitting a payment. Your &quot;{selectedPaymentMethod?.nickname}&quot; will
                        typically be debited within 4 business days.
                    </Typography>
                </Grid>
            </Grid>
            <Box sx={{alignSelf: 'flex-end', width: '100%'}}>
                <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="large" />}
                    onClick={handleBackToDashboard}
                >
                    Back to my Dashboard
                </Button>
            </Box>
        </>
    )
}

function getNormalizedBalance(money?: Money) {
    if (money && money.amount) {
        return formatCurrency(-money.amount / 100)
    }
    return formatCurrency(0)
}

function getAmount(money?: Money) {
    if (money && money.amount) {
        return flipNumberSign(Math.round(money.amount))
    }
    return 0
}
