'use client'
import {useRouter} from 'next/navigation'
import {useState, useReducer, useRef} from 'react'

import {
    Dialog,
    DialogTitle,
    DialogContent,
    IconButton,
    useMediaQuery,
    type Theme,
    Icon,
    Typography,
    Box,
    Button,
    Popover,
    Paper,
    DialogActions,
    Snackbar,
    Alert,
    AlertTitle,
    Grid,
} from '@mui/material'
import type {AutopayConfiguration} from '@/services/controllers/autopay'
import type {PaymentMethod} from '@/services/controllers/payments'
import {formatCurrency} from '@tallied-technologies/common'
import type {AutopayOptionsState, PaymentOptionsAction} from '../_types'
import PaymentStep1 from './AutoPaymentSteps/PaymentStep1'
import PaymentStep2 from './AutoPaymentSteps/PaymentStep2'
import PaymentStep3 from './AutoPaymentSteps/PaymentStep3'
import CancellationConfirmation from './AutoPaymentSteps/CancellationConfirmation'
import {updateAutopay, cancelAutopay} from '@/actions/payments'
import {useAutopayConfiguration} from '@/hooks/useAutopayConfigurationData'
import {usePaymentAccountsData} from '@/hooks/usePaymentData'

interface EditAutoPayModalProps {
    open: boolean
    autoPayConfig: AutopayConfiguration[]
}

export default function EditAutoPayModalLoader({open}: {open: boolean}) {
    const {data: autoPayConfig = [], isLoading} = useAutopayConfiguration()

    if (isLoading) {
        return null
    }

    return <EditAutoPayModal open={open} autoPayConfig={autoPayConfig} />
}

export function EditAutoPayModal({open, autoPayConfig}: EditAutoPayModalProps) {
    const isDesktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))
    const router = useRouter()
    const [hasError, setHasError] = useState<null | string>(null)
    const [callingCancel, setCallingCancel] = useState(false)
    const [updating, setUpdating] = useState(false)
    const [popoverAnchor, setPopoverAnchor] = useState<HTMLButtonElement | null>(null)
    const confirmPopover = Boolean(popoverAnchor)
    const [showCancellationConfirmation, setShowCancellationConfirmation] = useState(false)

    const activeAutoPayConfig = autoPayConfig?.find(config => config.state === 'active')

    // Initialize reducer state based on autoPayConfig
    const initialState: AutopayOptionsState = {
        step: 1, // Start at edit overview screen
        methodType: activeAutoPayConfig?.amountType || 'lastStatementBalance',
        amount: activeAutoPayConfig?.amount?.amount || 0,
        paymentAccount: activeAutoPayConfig?.paymentMethodId || '',
        paymentDate: activeAutoPayConfig?.autopayStrategy?.value || 1,
        paymentStrategy: activeAutoPayConfig?.autopayStrategy?.type || 'dayInMonth',
    }

    // Store a reference to the initial state for diffing
    const initialStateRef = useRef<AutopayOptionsState>(initialState)

    const [paymentState, dispatchPaymentUpdate] = useReducer(paymentStateReducer, initialState)

    function handleClose() {
        router.replace('/')
    }

    function handleOpenPopover(evt: React.MouseEvent<HTMLButtonElement>) {
        setPopoverAnchor(evt.currentTarget)
    }

    function closePopover() {
        setPopoverAnchor(null)
    }

    function handleNavigateToStep(step: number) {
        dispatchPaymentUpdate({
            type: 'changeStep',
            payload: step,
        })
    }

    async function handleCancelAutoPay() {
        if (!activeAutoPayConfig?.autopayConfigurationId) {
            setHasError('Auto-pay configuration ID not found.')
            return
        }

        setCallingCancel(true)
        try {
            // Use the real API function instead of a mock
            const result = await cancelAutopay(activeAutoPayConfig.autopayConfigurationId)

            if (!result.success) {
                setHasError(result.error || 'There was an issue canceling Auto-Pay. Please try again')
                return
            }

            closePopover()
            // Show the cancellation confirmation
            setShowCancellationConfirmation(true)
        } catch (error) {
            setHasError(
                error instanceof Error ? error.message : 'There was an issue canceling Auto-Pay. Please try again',
            )
        } finally {
            setCallingCancel(false)
        }
    }

    async function handleUpdateAutoPay() {
        if (!activeAutoPayConfig?.autopayConfigurationId) {
            setHasError('Auto-pay configuration ID not found.')
            return
        }

        // Compare initial state with current state to determine what has changed
        const initialStateSnapshot = initialStateRef.current
        const changes: Record<string, any> = {}

        // Check for amount type changes
        if (paymentState.methodType !== initialStateSnapshot.methodType) {
            changes.amountType = paymentState.methodType
        }

        // Check for amount changes
        if (paymentState.amount !== initialStateSnapshot.amount) {
            changes.amount = {
                amount: paymentState.amount,
                currency: 'USD',
            }
        }

        // Check for payment method changes
        if (paymentState.paymentAccount !== initialStateSnapshot.paymentAccount) {
            changes.paymentMethodId = paymentState.paymentAccount
        }

        // Check for payment date/strategy changes
        if (
            paymentState.paymentDate !== initialStateSnapshot.paymentDate ||
            paymentState.paymentStrategy !== initialStateSnapshot.paymentStrategy
        ) {
            changes.autopayStrategy = {
                type: paymentState.paymentStrategy,
                value: paymentState.paymentDate,
            }
        }

        // If no changes detected, just navigate back to step 1 without making an API call
        if (Object.keys(changes).length === 0) {
            console.log('No changes detected, skipping update API call')
            dispatchPaymentUpdate({
                type: 'changeStep',
                payload: 1,
            })
            return
        }

        console.log('Updating autopay with changes:', changes)

        setUpdating(true)
        try {
            // Use the real API function instead of a mock
            const result = await updateAutopay(activeAutoPayConfig.autopayConfigurationId, changes)

            if (!result.success) {
                setHasError(result.error || 'There was an issue updating Auto-Pay. Please try again')
                return
            }

            // Return to step 1 on success
            dispatchPaymentUpdate({
                type: 'changeStep',
                payload: 1,
            })

            // Update initialStateRef to reflect the new baseline state
            initialStateRef.current = {...paymentState}

            // Optionally, you could also refresh the page to fetch the latest data
            // router.refresh()
        } catch (error) {
            setHasError(
                error instanceof Error ? error.message : 'There was an issue updating Auto-Pay. Please try again',
            )
        } finally {
            setUpdating(false)
        }
    }

    // Calculate the next payment date for display in cancellation confirmation
    const getNextPaymentDate = () => {
        if (!paymentState || !paymentState.paymentDate) return undefined

        const now = new Date()
        const currentMonth = now.getMonth()
        const currentYear = now.getFullYear()

        // Create date for this month's payment
        let nextPaymentDate = new Date(currentYear, currentMonth, paymentState.paymentDate)

        // If this date has passed, use next month
        if (nextPaymentDate < now) {
            nextPaymentDate = new Date(currentYear, currentMonth + 1, paymentState.paymentDate)
        }

        // Format the date
        return nextPaymentDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        })
    }

    return (
        <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md" fullScreen={!isDesktop}>
            <DialogTitle>{showCancellationConfirmation ? 'Auto-pay Cancellation' : 'Manage Auto-Pay'}</DialogTitle>
            <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={handleClose}>
                <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="small" />
            </IconButton>
            <DialogContent sx={{display: 'flex', flexDirection: 'column', gap: 2, flex: 1, px: isDesktop ? 15 : 2}}>
                {showCancellationConfirmation ? (
                    <CancellationConfirmation nextPaymentDate={getNextPaymentDate()} />
                ) : paymentState.step === 1 ? (
                    <InitalAutoPayDisplay paymentState={paymentState} onNavigate={handleNavigateToStep} />
                ) : paymentState.step === 2 ? (
                    <PaymentStep1
                        paymentState={paymentState}
                        setPaymentState={dispatchPaymentUpdate}
                        onBack={() => handleNavigateToStep(1)}
                    />
                ) : paymentState.step === 3 ? (
                    <PaymentStep2
                        paymentState={paymentState}
                        setPaymentState={dispatchPaymentUpdate}
                        dueDate={21} // This could be dynamic based on account info
                        onBack={() => handleNavigateToStep(1)}
                    />
                ) : paymentState.step === 4 ? (
                    <PaymentStep3
                        paymentState={paymentState}
                        setPaymentState={dispatchPaymentUpdate}
                        onBack={() => handleNavigateToStep(1)}
                    />
                ) : null}

                <DialogActions>
                    {showCancellationConfirmation ? null : paymentState.step === 1 ? ( // No actions for cancellation confirmation - the button is inside the component
                        <Button
                            variant="outlined"
                            color="error"
                            fullWidth
                            sx={{mt: 2}}
                            onClick={handleOpenPopover}
                            loading={callingCancel}
                        >
                            CANCEL AUTO-PAY
                        </Button>
                    ) : (
                        <Button
                            variant="contained"
                            color="primary"
                            fullWidth
                            sx={{mt: 2}}
                            onClick={handleUpdateAutoPay}
                            loading={updating}
                        >
                            UPDATE
                        </Button>
                    )}

                    <Popover
                        open={confirmPopover}
                        anchorEl={popoverAnchor}
                        onClose={closePopover}
                        anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'center',
                        }}
                        transformOrigin={{
                            vertical: 'center',
                            horizontal: 'center',
                        }}
                        slotProps={{
                            paper: {
                                sx: {
                                    maxWidth: 340,
                                },
                            },
                        }}
                    >
                        <Paper>
                            <DialogTitle>
                                <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                                    Cancel Auto-Pay?
                                </Typography>
                            </DialogTitle>
                            <DialogContent>
                                <Typography fontSize={16} lineHeight="150%">
                                    Cancelling Auto-Pay will immediately stop any future automatic payments from being
                                    processed. You will need to make manual payments to avoid late fees.
                                </Typography>
                            </DialogContent>
                            <DialogActions sx={{justifyContent: 'space-between', gap: 1}}>
                                <Button variant="text" color="primary" onClick={closePopover}>
                                    Keep Auto-Pay
                                </Button>
                                <Button
                                    loading={callingCancel}
                                    variant="outlined"
                                    color="error"
                                    onClick={handleCancelAutoPay}
                                >
                                    Cancel Auto-Pay
                                </Button>
                            </DialogActions>
                        </Paper>
                    </Popover>
                </DialogActions>
            </DialogContent>

            {hasError && (
                <Snackbar
                    open={!!hasError}
                    autoHideDuration={5000}
                    onClose={() => setHasError(null)}
                    anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                >
                    <Alert
                        onClose={() => setHasError(null)}
                        severity="error"
                        variant="filled"
                        sx={{width: 350, backgroundColor: 'var(--mui-palette-error-main)'}}
                    >
                        <AlertTitle>Error</AlertTitle>
                        <Typography>{hasError}</Typography>
                    </Alert>
                </Snackbar>
            )}
        </Dialog>
    )
}

// Reducer function to handle state updates
function paymentStateReducer(
    state: AutopayOptionsState,
    action: PaymentOptionsAction<AutopayOptionsState>,
): AutopayOptionsState {
    switch (action.type) {
        case 'changeStep': {
            return {...state, step: action.payload}
        }
        case 'changeMethodType': {
            return {...state, methodType: action.payload}
        }
        case 'changeAmount': {
            return {...state, amount: action.payload}
        }
        case 'changePaymentAccount': {
            return {...state, paymentAccount: action.payload}
        }
        case 'changePaymentDate': {
            return {...state, paymentDate: action.payload}
        }
        default:
            return state
    }
}

function InitalAutoPayDisplay({
    paymentState,
    onNavigate,
}: {
    paymentState: AutopayOptionsState
    onNavigate: (step: number) => void
}) {
    const {data: paymentMethods} = usePaymentAccountsData({select: data => data.paymentMethods})

    function handleEditAmount() {
        // Set step to edit amount screen
        onNavigate(2)
    }

    function handleEditDate() {
        // Set step to edit payment date screen
        onNavigate(3)
    }

    function handleEditPaymentMethod() {
        // Set step to edit payment method screen
        onNavigate(4)
    }

    // Get payment method details if available
    const selectedPaymentMethod = paymentMethods?.find(method => method.paymentMethodId === paymentState.paymentAccount)

    // Format payment method display
    const paymentMethodDisplay = selectedPaymentMethod
        ? selectedPaymentMethod.nickname +
          (selectedPaymentMethod.achDetails?.accountNumber
              ? ` ••••${selectedPaymentMethod.achDetails.accountNumber.replace(/[^0-9]/g, '').slice(-4) || '1234'}`
              : '')
        : paymentState.paymentAccount
          ? `${paymentState.paymentAccount.substring(0, 14)}...`
          : 'Not specified'

    // Get payment amount display
    const getPaymentAmountDisplay = () => {
        switch (paymentState.methodType) {
            case 'lastStatementBalance':
                return 'Last Statement Balance'
            case 'minimumPaymentDue':
                return 'Minimum Payment'
            case 'fixed':
                return paymentState.amount ? formatCurrency(paymentState.amount / 100) : 'Fixed Amount'
            default:
                return 'Not specified'
        }
    }

    // Helper function to get day suffix (st, nd, rd, th)
    function getDaySuffix(day: number): string {
        if (day >= 11 && day <= 13) {
            return 'th'
        }

        switch (day % 10) {
            case 1:
                return 'st'
            case 2:
                return 'nd'
            case 3:
                return 'rd'
            default:
                return 'th'
        }
    }

    // Get payment date display
    const getPaymentDateDisplay = () => {
        const dayValue = paymentState.paymentDate
        return `${dayValue}${getDaySuffix(dayValue)} Day of Each Month`
    }

    // Calculate next payment date
    const getNextPaymentDate = () => {
        const now = new Date()
        const currentMonth = now.getMonth()
        const currentYear = now.getFullYear()

        // Create date for this month's payment
        let nextPaymentDate = new Date(currentYear, currentMonth, paymentState.paymentDate)

        // If this date has passed, use next month
        if (nextPaymentDate < now) {
            nextPaymentDate = new Date(currentYear, currentMonth + 1, paymentState.paymentDate)
        }

        // Format the date
        return nextPaymentDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        })
    }

    return (
        <Grid container flexDirection="column" gap={2} flex={1}>
            <Typography variant="h6" fontWeight={600} fontSize={20}>
                Your Auto-Pay Details
            </Typography>

            {/* Payment Amount Section */}
            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <Box>
                    <Typography fontSize={14} color="textSecondary">
                        Payment Amount
                    </Typography>
                    <Typography fontSize={16} fontWeight={600}>
                        {getPaymentAmountDisplay()}
                    </Typography>
                </Box>
                <IconButton onClick={handleEditAmount} aria-label="Edit payment amount" size="small">
                    <Icon baseClassName="fas" className="fa-solid fa-pen" fontSize="small" />
                </IconButton>
            </Box>

            {/* Payment Date Section */}
            <Box>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                    <Box>
                        <Typography fontSize={14} color="textSecondary">
                            Payment Date
                        </Typography>
                        <Typography fontSize={16} fontWeight={600}>
                            {getPaymentDateDisplay()}
                        </Typography>
                    </Box>
                    <IconButton onClick={handleEditDate} aria-label="Edit payment date" size="small">
                        <Icon baseClassName="fas" className="fa-solid fa-pen" fontSize="small" />
                    </IconButton>
                </Box>
                <Typography fontSize={12} color="textSecondary">
                    Your next auto-payment will be processed on {getNextPaymentDate() || 'the scheduled date'}. If your
                    selected payment date falls on a non-banking day, we will process it one day earlier.
                </Typography>
            </Box>

            {/* Payment Method Section */}
            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <Box>
                    <Typography fontSize={14} color="textSecondary">
                        Payment Method
                    </Typography>
                    <Typography fontSize={16} fontWeight={600}>
                        {paymentMethodDisplay}
                    </Typography>
                </Box>
                <IconButton onClick={handleEditPaymentMethod} aria-label="Edit payment method" size="small">
                    <Icon baseClassName="fas" className="fa-solid fa-pen" fontSize="small" />
                </IconButton>
            </Box>
        </Grid>
    )
}
