import {Grid, Skeleton} from '@mui/material'
import PaymentOptions from './_components/PaymentOptions'
import PaymentsHeaderAndActions from './_components/PaymentsHeaderAndActions'
import AutoPayModal from './_components/AutoPayModal'
import EditAutoPayModal from './_components/EditAutoPayModal'

type Params = Promise<{slug: string}>
type SearchParams = Promise<URLSearchParams>

export default async function PaymentsContent(props: {params: Params; searchParams: SearchParams}) {
    const searchParams = await props.searchParams
    const searchParamsObject = new URLSearchParams(searchParams)
    const autopay = searchParamsObject.get('q') === 'autopay'
    const editAutopay = searchParamsObject.get('q') === 'edit-autopay'

    return (
        <Grid container flexDirection="column" sx={{maxWidth: '640px', width: '100%', p: 2}} flex={1}>
            <PaymentsHeaderAndActions />
            <PaymentOptions />
            <AutoPayModal open={autopay} />
            <EditAutoPayModal open={editAutopay} />
        </Grid>
    )
}

export function LoadingPaymentsContent() {
    return (
        <Grid container flexDirection="column" sx={{maxWidth: '640px', width: '100%', p: 2}} flex={1}>
            <Skeleton variant="rectangular" width={620} height={600} />
        </Grid>
    )
}
