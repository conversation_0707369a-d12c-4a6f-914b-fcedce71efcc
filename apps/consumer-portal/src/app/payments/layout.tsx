import {DataCache} from '@/components/Context/DataCache'
import {getPaymentAccounts, getAutopayConfig, getPayments} from '@/data'

export default async function PaymentsLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    const [paymentMethods, payments] = await Promise.all([getPaymentAccounts({state: 'active'}), getPayments()])

    return (
        <DataCache paymentMethods={paymentMethods} payments={payments}>
            {children}
        </DataCache>
    )
}
