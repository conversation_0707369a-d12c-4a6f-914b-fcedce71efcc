import {Suspense} from 'react'
import Footer from '@/components/Footer'
import Header from '@/components/Header'
import DashboardShell from '@/components/layout/DashboardShell'
import {Grid} from '@mui/material'
import PaymentsContent, {LoadingPaymentsContent} from './PaymentsContent'
import {validateSession} from '@/auth'

type Params = Promise<{slug: string}>
type SearchParams = Promise<URLSearchParams>

export default async function PaymentsRoute(props: {params: Params; searchParams: SearchParams}) {
    await validateSession()

    return (
        <Grid
            container
            flexDirection="column"
            flex="1 0 0"
            justifyContent="space-between"
            alignItems="center"
            alignSelf="stretch"
            flexWrap="nowrap"
        >
            <DashboardShell>
                <Header />
                <Suspense fallback={<LoadingPaymentsContent />}>
                    <PaymentsContent {...props} />
                </Suspense>
                <Footer />
            </DashboardShell>
        </Grid>
    )
}
