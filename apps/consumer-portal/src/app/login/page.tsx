'use client'

import {
    <PERSON>,
    <PERSON><PERSON>,
    Container,
    Link,
    Grid,
    Ty<PERSON><PERSON>,
    Card,
    Snackbar,
    Alert,
    AlertTitle,
    Icon,
    useTheme,
} from '@mui/material'
import {login} from './actions'
import Image from 'next/image'
import {useSearchParams} from 'next/navigation'

export default function LoginPage() {
    const searchParams = useSearchParams()
    const theme = useTheme()

    const message = searchParams.get('c') as LogoutMessageEnums | null
    const returnUrl = searchParams.get('returnUrl')

    return (
        <>
            <Container
                maxWidth="xs"
                fixed
                sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    textAlign: 'center',
                    [theme.breakpoints.down('lg')]: {
                        top: '40%',
                    },
                }}
            >
                <Card
                    sx={{
                        p: '64px 32px 16px 32px',
                        width: `${188 + 64}px`,
                        mx: 'auto',
                        [theme.breakpoints.up('lg')]: {p: '88px 64px 16px 64px', width: `${188 + 128}px`},
                    }}
                >
                    <Grid
                        container
                        flexDirection="column"
                        sx={{maxWidth: 200, mx: 'auto', justifyContent: 'space-between', height: '100%', width: 183}}
                    >
                        <Grid sx={{position: 'relative', mb: 8}}>
                            <Box>
                                <Image
                                    src="/assets/tallied-logo.svg"
                                    width={120}
                                    height={30}
                                    alt="Tallied Technologies Logo"
                                />
                            </Box>
                        </Grid>
                        <Grid container component="form" action={login} sx={{mb: 8}}>
                            {/* Hidden input to pass returnUrl if it exists */}
                            {returnUrl && <input type="hidden" name="returnUrl" value={returnUrl} />}

                            <Button type="submit" color="primary" variant="contained" fullWidth sx={{mb: 1}}>
                                Login with Email
                            </Button>
                            <Grid container width="100%" justifyContent="space-between">
                                <Link
                                    href="https://www.tallied.io/legals/terms-of-service"
                                    variant="caption"
                                    fontSize={11}
                                >
                                    Terms of Service
                                </Link>
                                <Link
                                    href="https://www.tallied.io/legals/privacy-policy"
                                    variant="caption"
                                    fontSize={11}
                                >
                                    Privacy Policy
                                </Link>
                            </Grid>
                        </Grid>
                        <Grid sx={{mx: 'auto', width: '100%'}}>
                            <Box display="flex" alignItems="center" justifyContent="center">
                                <Typography variant="caption" sx={{mr: 0.5}}>
                                    Powered by
                                </Typography>
                                <Image
                                    src="/assets/zitadel-logo-dark.svg"
                                    alt="Powered by ZITADEL"
                                    width={97}
                                    height={27}
                                />
                            </Box>
                        </Grid>
                    </Grid>
                </Card>
            </Container>
            {message ? (
                <Snackbar open anchorOrigin={{vertical: 'top', horizontal: 'center'}}>
                    <Alert
                        icon={<Icon baseClassName="fas" className="fa-exclamation-triangle" />}
                        variant="filled"
                        severity="error"
                        sx={{backgroundColor: 'var(--mui-palette-SnackbarContent-bg)', maxWidth: 295, width: 295}}
                    >
                        {getSnackbarMessage(message)}
                    </Alert>
                </Snackbar>
            ) : null}
        </>
    )
}

function getSnackbarMessage(message: LogoutMessageEnums) {
    if (message === 'SessionExpired') {
        return (
            <>
                <AlertTitle>Session Expired</AlertTitle>
                <Typography fontSize={14} fontWeight={500} lineHeight="143%">
                    Please login again
                </Typography>
            </>
        )
    } else if (message === 'RefreshAccessTokenError') {
        return (
            <>
                <AlertTitle>Session Expired</AlertTitle>
                <Typography fontSize={14} fontWeight={500} lineHeight="143%">
                    Please login again
                </Typography>
            </>
        )
    } else if (message === 'logout') {
        return (
            <>
                <AlertTitle>Session Ended</AlertTitle>
                <Typography fontSize={14} fontWeight={500} lineHeight="143%">
                    Please login again
                </Typography>
            </>
        )
    }
}

const LogoutMessageEnums = {
    SessionExpired: 'SessionExpired',
    logout: 'logout',
    RefreshAccessTokenError: 'RefreshAccessTokenError',
} as const
type LogoutMessageEnums = (typeof LogoutMessageEnums)[keyof typeof LogoutMessageEnums]
