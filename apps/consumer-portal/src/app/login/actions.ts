'use server'

import {signIn} from '@/auth'
import {redirect} from 'next/navigation'

export async function login(formData: FormData) {
    // Get the returnUrl from the form data if it exists
    const returnUrl = formData.get('returnUrl') as string | null

    // If a returnUrl is provided, use it as the redirect destination after login
    const redirectTo = returnUrl ? decodeURIComponent(returnUrl) : '/'

    await signIn('zitadel', {redirectTo})
}
