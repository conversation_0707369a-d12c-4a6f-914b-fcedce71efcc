'use server'

import {decorateApi} from '@/services'
import {validateSession} from '@/auth'
import {randomUUID} from 'crypto'
import PaymentsApi from '@/services/controllers/payments'
import AutopayApi, {
    type AutopayConfiguration,
    type UpdateAutopayConfigurationRequest,
    type AmountType,
} from '@/services/controllers/autopay'
import type {
    PaymentMethodVerificationProvider,
    PrepareVerificationVendor200Response,
    PaymentMethod,
    MxAccountNumber,
    GetPaymentMethodVerification200Response,
    PaymentsConfiguration,
} from '@/services/controllers/payments'
import {revalidatePath} from 'next/cache'
import {createSuccessResult, createErrorResult, handleActionError, type Result} from './types'
import {APIError} from '@/services/APIError'

export async function getPaymentConfig(): Promise<Result<PaymentsConfiguration>> {
    try {
        await validateSession()
        const paymentConfigResponse = await decorateApi(PaymentsApi.getDefaultPaymentsConfiguration)
        return createSuccessResult(paymentConfigResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function prepareVerificationVendor(
    provider: PaymentMethodVerificationProvider,
): Promise<Result<PrepareVerificationVendor200Response>> {
    try {
        const {user} = await validateSession()
        // Validate that user has a credit account
        if (!user.creditAccountId) {
            return createErrorResult('User does not have an associated credit account')
        }
        // Validate that user has a person
        if (!user.personId) {
            return createErrorResult('User does not have an associated person')
        }

        const prepareVerificationVendorResponse = await decorateApi(PaymentsApi.prepareVerificationVendor, {
            idempotencyKey: randomUUID(),
            prepareVerificationVendorRequest: {
                paymentMethodVerificationProvider: provider,
                creditAccountId: user.creditAccountId,
                personId: user.personId,
            },
        })

        return createSuccessResult(prepareVerificationVendorResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function verifyMxAccount(userGuid: string, memberGuid: string): Promise<Result<MxAccountNumber[]>> {
    try {
        await validateSession()
        const verifiedAccounts = await decorateApi(PaymentsApi.getMxVerifiedAccounts, {
            userGuid,
            memberGuid,
        })
        return createSuccessResult(verifiedAccounts)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function getProcessorToken(
    accountGuid: string,
    memberGuid: string,
    userGuid: string,
): Promise<Result<string>> {
    try {
        await validateSession()
        const processorTokenResponse = await decorateApi(PaymentsApi.getMxProcessorToken, {
            getMxProcessorTokenRequest: {
                accountGuid,
                memberGuid,
                userGuid,
            },
        })
        return createSuccessResult(processorTokenResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function updatePaymentMethodVerificationForMicroDeposits(
    paymentMethodId: string,
    deposit1: string,
    deposit2: string,
): Promise<Result<GetPaymentMethodVerification200Response>> {
    try {
        await validateSession()

        const depositAmount1 = Number(deposit1)
        const depositAmount2 = Number(deposit2)

        if (
            Number.isNaN(depositAmount1) ||
            Number.isNaN(depositAmount2) ||
            depositAmount1 <= 0 ||
            depositAmount2 <= 0
        ) {
            return createErrorResult('Deposit amounts must be positive numbers')
        }

        const paymentConfigResponse = await decorateApi(PaymentsApi.updatePaymentMethodVerification, {
            paymentMethodId,
            updatePaymentMethodVerificationRequest: {
                paymentMethodVerificationMethod: 'microdeposits',
                paymentMethodVerificationState: 'verified',
                microdeposits: {
                    verificationAmount1: {
                        currency: 'USD',
                        amount: Math.round(depositAmount1 * 100),
                    },
                    verificationAmount2: {
                        currency: 'USD',
                        amount: Math.round(depositAmount2 * 100),
                    },
                },
            },
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/payments')
        return createSuccessResult(paymentConfigResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function updatePaymentMethodVerificationForMx(
    paymentMethodId: string,
): Promise<Result<GetPaymentMethodVerification200Response>> {
    try {
        await validateSession()
        const paymentConfigResponse = await decorateApi(PaymentsApi.updatePaymentMethodVerification, {
            paymentMethodId,
            updatePaymentMethodVerificationRequest: {
                paymentMethodVerificationMethod: 'mxToken',
                paymentMethodVerificationState: 'verified',
            },
            idempotencyKey: randomUUID(),
        })
        return createSuccessResult(paymentConfigResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function createPaymentMethod(
    provider: PaymentMethodVerificationProvider,
    nickname: string,
    accountNumber: string,
    routingNumber: string,
    bankAccountType: 'CHECKING' | 'SAVINGS' = 'CHECKING',
    token?: string,
): Promise<Result<PaymentMethod>> {
    try {
        const {user} = await validateSession()
        // Validate that user has a credit account
        if (!user.creditAccountId) {
            return createErrorResult('User does not have an associated credit account')
        }
        // Validate that user has a person
        if (!user.personId) {
            return createErrorResult('User does not have an associated person')
        }

        if (!/^\d{9}$/.test(routingNumber)) {
            return createErrorResult('Routing number must be 9 digits')
        }

        if (!/^\d{4,20}$/.test(accountNumber)) {
            return createErrorResult('Account number must be between 4 and 20 digits')
        }

        const verificationMethod = provider === 'mx' ? 'mxToken' : 'microdeposits'

        const paymentMethodResponse = await decorateApi(PaymentsApi.createPaymentMethod, {
            createPaymentMethodRequest: {
                creditAccountId: user.creditAccountId,
                personId: user.personId,
                paymentMethodType: 'ACH',
                paymentMethodVerificationProvider: provider,
                nickname,
                achDetails: {
                    bankAccountType,
                    accountNumber,
                    routingNumber,
                },
                token,
            },
            idempotencyKey: randomUUID(),
        })

        if (paymentMethodResponse) {
            await decorateApi(PaymentsApi.updatePaymentMethodVerification, {
                paymentMethodId: paymentMethodResponse.paymentMethodId,
                updatePaymentMethodVerificationRequest: {
                    paymentMethodVerificationMethod: verificationMethod,
                    paymentMethodVerificationState: provider === 'mx' ? 'verified' : 'inProgress',
                },
                idempotencyKey: randomUUID(),
            })
        }

        revalidatePath('/payments')
        return createSuccessResult(paymentMethodResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function removePaymentMethod(paymentMethodId: string): Promise<Result<void>> {
    try {
        await validateSession()

        await decorateApi(PaymentsApi.updatePaymentMethod, {
            paymentMethodId,
            updatePaymentMethodRequest: {
                paymentMethodState: 'inactive',
            },
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/payments')
        return createSuccessResult(undefined)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function makePayment(paymentMethodId: string, amount: number): Promise<Result<boolean>> {
    try {
        const {user} = await validateSession()
        
        // Validate that user has a credit account
        if (!user.creditAccountId) {
            return createErrorResult('User does not have an associated credit account')
        }
        // Validate that user has a person
        if (!user.personId) {
            return createErrorResult('User does not have an associated person')
        }
        
        if (amount <= 0) {
            return createErrorResult('Payment amount must be greater than 0')
        }

        const paymentCreationResponse = await decorateApi(PaymentsApi.createPayment, {
            createPaymentRequest: {
                paymentMethodId,
                creditAccountId: user.creditAccountId,
                initiatorPersonId: user.personId,
                strategy: 'holdForCompletion',
                description: 'Payment for ' + new Date().toLocaleDateString(),
                amount: {
                    amount,
                    currency: 'USD',
                },
            },
            idempotencyKey: randomUUID(),
        })

        if (paymentCreationResponse) {
            await decorateApi(PaymentsApi.updatePayment, {
                paymentId: paymentCreationResponse.paymentId,
                updatePaymentRequest: {
                    state: 'initiated',
                },
                idempotencyKey: randomUUID(),
            })

            return createSuccessResult(true)
        }

        return createSuccessResult(false)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

interface CreateAutopayRequest {
    amount: number
    paymentMethodId: string
    paymentDate: number
    amountType: AmountType
    paymentStrategy: 'daysBeforeDueDate' | 'daysAfterStatementClosed' | 'dayInMonth'
}

export async function createAutopay(autopayRequest: CreateAutopayRequest): Promise<Result<AutopayConfiguration>> {
    try {
        const {user} = await validateSession()
        
        // Validate that user has a credit account
        if (!user.creditAccountId) {
            return createErrorResult('User does not have an associated credit account')
        }
        
        const autopayResponse = await decorateApi(AutopayApi.createAutopayConfiguration, {
            createAutopayConfigurationRequest: {
                amount: {
                    amount: autopayRequest.amount,
                    currency: 'USD',
                },
                amountType: autopayRequest.amountType,
                autopayStrategy: {
                    type: autopayRequest.paymentStrategy,
                    value: autopayRequest.paymentDate,
                },
                paymentMethodId: autopayRequest.paymentMethodId,
                creditAccountId: user.creditAccountId,
            },
            idempotencyKey: randomUUID(),
        })

        if (autopayResponse) {
            return createSuccessResult(autopayResponse)
        }

        return createErrorResult('Failed to create autopay')
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function updateAutopay(
    autopayId: string,
    autopayRequest: UpdateAutopayConfigurationRequest,
): Promise<Result<AutopayConfiguration>> {
    try {
        await validateSession()

        const autopayResponse = await decorateApi(AutopayApi.updateAutopayConfiguration, {
            updateAutopayConfigurationRequest: autopayRequest,
            autopayConfigurationId: autopayId,
            idempotencyKey: randomUUID(),
        })

        if (autopayResponse) {
            return createSuccessResult(autopayResponse)
        }

        return createErrorResult('Failed to update autopay')
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function cancelAutopay(autopayId: string): Promise<Result<boolean>> {
    try {
        await validateSession()

        const autopayResponse = await decorateApi(AutopayApi.updateAutopayConfiguration, {
            updateAutopayConfigurationRequest: {
                state: 'inactive',
            },
            autopayConfigurationId: autopayId,
            idempotencyKey: randomUUID(),
        })

        if (autopayResponse) {
            return createSuccessResult(true)
        }

        return createSuccessResult(false)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}
