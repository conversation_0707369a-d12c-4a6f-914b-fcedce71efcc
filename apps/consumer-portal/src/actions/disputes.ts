'use server'

import {decorate<PERSON>pi} from '@/services'
import {validateSession} from '@/auth'
import {randomUUID} from 'crypto'
import {revalidatePath} from 'next/cache'
import Disputes<PERSON><PERSON>, {
    type CreateDisputeRequest,
    type CreateDispute201Response,
    type GetDisputes200ResponseDisputesInner,
} from '@/services/controllers/disputes'
import {createSuccessResult, createErrorResult, handleActionError, type Result} from './types'
import {APIError} from '@/services/APIError'
import {getDispute as getDisputeData} from '@/data'

export async function createDispute(
    createDisputeRequest: CreateDisputeRequest,
): Promise<Result<CreateDispute201Response>> {
    try {
        await validateSession()

        const createDisputeResponse = await decorateApi(DisputesApi.createDispute, {
            createDisputeRequest,
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/')
        return createSuccessResult(createDisputeResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(error.message)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function getDispute(disputeId: string): Promise<Result<GetDisputes200ResponseDisputesInner>> {
    try {
        await validateSession()
        const dispute = await getDisputeData(disputeId)
        return createSuccessResult(dispute)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(error.message)
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        throw error
    }
}
