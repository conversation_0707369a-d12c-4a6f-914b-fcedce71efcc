import {ResponseError} from '@/services/APIError'

export type Result<T> =
    | {
          success: true
          data: T
      }
    | {
          success: false
          error: string
          status?: number
      }

export function createSuccessResult<T>(data: T): Result<T> {
    return {
        success: true,
        data,
    }
}

export function createErrorResult(error: string | Error | ResponseError): Result<never> {
    if (error instanceof ResponseError) {
        return {
            success: false,
            error: error.message ?? 'Response Error',
            status: error.response.status,
        }
    }

    if (error instanceof Error) {
        return {
            success: false,
            error: error.message,
        }
    }

    if (typeof error === 'string') {
        return {
            success: false,
            error,
        }
    }

    return {
        success: false,
        error: 'An unknown error occurred',
        status: undefined,
    }
}

export function handleActionError(error: unknown): Result<never> {
    console.error('Action error:', error)

    if (error instanceof ResponseError) {
        return createErrorResult(error)
    }

    if (error instanceof Error) {
        return createErrorResult(error)
    }

    if (typeof error === 'object' && error !== null) {
        return createErrorResult(error as ResponseError)
    }

    return createErrorResult('An unknown error occurred')
}
