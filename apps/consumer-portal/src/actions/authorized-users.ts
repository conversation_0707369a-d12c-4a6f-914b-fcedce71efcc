'use server'

import {decorateApi} from '@/services'
import {validateSession} from '@/auth'
import {randomUUID} from 'crypto'
import AuthorizedUsersApi, {type AuthorizedUser} from '@/services/controllers/authorizedUsers'
import {createSuccessResult, createErrorResult, handleActionError, type Result} from './types'
import {APIError} from '@/services/APIError'

/**
 * Creates a new authorized user for the credit account
 */
export async function createAuthorizedUser(userData: {
    firstName: string
    lastName: string
    email: string
    dob: string
    ssn: string
    phoneNumber?: string
}): Promise<Result<AuthorizedUser>> {
    try {
        const {user} = await validateSession()

        // Validate that user has a credit account
        if (!user.creditAccountId) {
            return createErrorResult('User does not have an associated credit account')
        }

        // Validate required fields
        if (!userData.firstName || !userData.lastName || !userData.email || !userData.dob || !userData.ssn) {
            return createErrorResult('Missing required fields for authorized user')
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(userData.email)) {
            return createErrorResult('Invalid email format')
        }

        // Validate SSN (9 digits)
        if (!/^\d{9}$/.test(userData.ssn.replace(/-/g, ''))) {
            return createErrorResult('SSN must be 9 digits')
        }

        // Validate phone number format if provided
        if (userData.phoneNumber) {
            const phoneRegex = /^\+[1-9]\d{1,14}$/
            if (!phoneRegex.test(userData.phoneNumber)) {
                return createErrorResult('Phone number must be in E.164 format (e.g., +***********)')
            }
        }

        // Parse and validate date of birth
        const dobMatch = userData.dob.match(/^(\d{2})\/(\d{2})\/(\d{4})$/)
        if (!dobMatch) {
            return createErrorResult('Date of birth must be in MM/DD/YYYY format')
        }

        const [_, month, day, year] = dobMatch.map(Number)
        const dobDate = new Date(year, month - 1, day)
        const today = new Date()

        // Validate date is real and within allowed range
        if (
            dobDate.getMonth() !== month - 1 ||
            dobDate.getDate() !== day ||
            dobDate.getFullYear() !== year ||
            dobDate > today ||
            dobDate.getFullYear() < 1900
        ) {
            return createErrorResult('Invalid date of birth')
        }

        // Format SSN with dashes (XXX-XX-XXXX)
        const formattedSSN = userData.ssn.replace(/-/g, '').replace(/^(\d{3})(\d{2})(\d{4})$/, '$1-$2-$3')

        // Create request object - using type assertion to handle string types
        const createAuthorizedUserRequest = {
            firstName: userData.firstName,
            lastName: userData.lastName,
            dateOfBirth: dobDate,
            email: {
                address: userData.email,
            },
            creditAccountId: user.creditAccountId,
            nationalId: formattedSSN, // Using formatted SSN with dashes
            ...(userData.phoneNumber ? {phoneNumber: {number: userData.phoneNumber}} : {}),
        }


        // Make API call
        const authorizedUserResponse = await decorateApi(
            AuthorizedUsersApi.createAuthorizedUser,
            {
                createAuthorizedUserRequest,
                idempotencyKey: randomUUID(),
            },
        )

        return createSuccessResult(authorizedUserResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail || 'Failed to create authorized user')
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

/**
 * Updates basic information for an existing authorized user
 */
export async function updateAuthorizedUser(
    authorizedUserId: string,
    updates: Partial<{
        firstName: string
        lastName: string
        email: string
        phoneNumber: string
    }>,
): Promise<Result<AuthorizedUser>> {
    try {
        await validateSession()

        // Validate that we have something to update
        if (Object.keys(updates).length === 0) {
            return createErrorResult('No fields provided for update')
        }

        // Validate email format if provided
        if (updates.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updates.email)) {
            return createErrorResult('Invalid email format')
        }

        // Validate phone number format if provided
        if (updates.phoneNumber) {
            const phoneRegex = /^\+[1-9]\d{1,14}$/
            if (!phoneRegex.test(updates.phoneNumber)) {
                return createErrorResult('Phone number must be in E.164 format (e.g., +***********)')
            }
        }

        // Make API call using getAuthorizedUser to find existing user first
        const existingUser = await decorateApi(AuthorizedUsersApi.getAuthorizedUser, {
            authorizedUserId,
        })

        if (!existingUser) {
            return createErrorResult('Authorized user not found')
        }

        // Then create a new user with updated fields to replace the existing one
        // This is a common pattern when specific update endpoints aren't available
        const updatedUser = await decorateApi(AuthorizedUsersApi.modifyAuthorizedUser, {
            modifyAuthorizedUserRequest: {
                firstName: updates.firstName,
                lastName: updates.lastName,
                ...(updates.email && {email: {address: updates.email}}),
                ...(updates.phoneNumber && {phoneNumber: {number: updates.phoneNumber}}),
            },
            authorizedUserId,
            idempotencyKey: randomUUID(),
        })

        return createSuccessResult(updatedUser)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail || 'Failed to update authorized user')
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

/**
 * Removes an authorized user by setting status to inactive
 * Note: This implementation depends on the available API methods.
 * Since we don't have a direct setInactive method, we're approximating
 * with the available API.
 */
export async function removeAuthorizedUser(authorizedUserId: string): Promise<Result<void>> {
    try {
        await validateSession()

        // Get the existing user
        const existingUser = await decorateApi(AuthorizedUsersApi.getAuthorizedUser, {
            authorizedUserId,
        })

        if (!existingUser) {
            return createErrorResult('Authorized user not found')
        }

        // Set the user to inactive by calling the create method with updated state
        await decorateApi(AuthorizedUsersApi.removeAuthorizedUser, {
            authorizedUserId,
            idempotencyKey: randomUUID(),
        })

        return createSuccessResult(undefined)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            const errorResponse = await error.response.json()
            return createErrorResult(errorResponse.detail || 'Failed to remove authorized user')
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}
