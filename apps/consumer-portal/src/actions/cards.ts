'use server'

import {decorateApi} from '@/services'
import CardsApi from '@/services/controllers/cards'
import type {
    Card,
    CreateCreditCardRequest,
    State,
    ShippingMethod,
    SpendControl,
    Address,
    Type,
    SpendControlsCadenceIntervalEnum,
    UpdateCreditCardRequest,
} from '@/services/controllers/cards'
import {validateSession} from '@/auth'
import {randomUUID} from 'crypto'
import {revalidatePath} from 'next/cache'
import {createSuccessResult, createErrorResult, handleActionError, type Result} from './types'
import {APIError} from '@/services/APIError'

// Type for successful replace card response
export type ReplaceCardSuccess = {
    cardId: string
    state: State
    newCardId: string
    newCardState: State
}

export async function replaceCard(
    card: Pick<
        Card,
        'cardId' | 'type' | 'lastFour' | 'state' | 'authorizedUserId' | 'spendControls' | 'authorizationControls'
    >,
    shipping: Card['shippedTo'],
): Promise<Result<ReplaceCardSuccess>> {
    try {
        await validateSession()

        const cancelResponse = await cancelCard({cardId: card.cardId, lastFour: card.lastFour, state: card.state})
        if (!cancelResponse.success) {
            return cancelResponse
        }

        // Create options based on card type
        let cardOptions: CardRequestOptions

        if (card.type === 'physical') {
            // Check if this is an authorized user card
            if (card.authorizedUserId) {
                // Create options for authorized user physical card
                cardOptions = {
                    type: 'physical' as const,
                    shipping: shipping as Address,
                    authorizedUserId: card.authorizedUserId,
                    spendControls: card.spendControls
                        ? Array.from(card.spendControls).map(control => ({
                              amount: control.limit.amount,
                              currency: control.limit.currency,
                              cadence: control.cadence.interval,
                          }))
                        : undefined,
                    // Add authorizationControls if they exist
                    ...(card.authorizationControls && {authorizationControls: card.authorizationControls}),
                }
            } else {
                // Create options for primary user physical card
                cardOptions = {
                    type: 'physical' as const,
                    shipping: shipping as Address,
                    // Add authorizationControls if they exist even for primary user
                    ...(card.authorizationControls && {authorizationControls: card.authorizationControls}),
                }
            }
        } else {
            // Handle virtual cards (though we're restricting virtual card replacement in the UI)
            if (card.authorizedUserId) {
                cardOptions = {
                    type: 'virtual' as const,
                    authorizedUserId: card.authorizedUserId,
                    spendControls: card.spendControls
                        ? Array.from(card.spendControls).map(control => ({
                              amount: control.limit.amount,
                              currency: control.limit.currency,
                              cadence: control.cadence.interval,
                          }))
                        : undefined,
                    // Add authorizationControls if they exist
                    ...(card.authorizationControls && {authorizationControls: card.authorizationControls}),
                }
            } else {
                cardOptions = {
                    type: 'virtual' as const,
                    // Add authorizationControls if they exist even for primary user
                    ...(card.authorizationControls && {authorizationControls: card.authorizationControls}),
                }
            }
        }

        const createCardResponse = await requestCard(cardOptions)
        if (!createCardResponse.success) {
            return createCardResponse
        }

        return createSuccessResult({
            cardId: cancelResponse.data.cardId,
            state: cancelResponse.data.state,
            newCardId: createCardResponse.data.cardId,
            newCardState: createCardResponse.data.state,
        })
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function cancelCard(card: Pick<Card, 'cardId' | 'lastFour' | 'state'>): Promise<Result<Card>> {
    try {
        await validateSession()

        if (card.state === 'locked') {
            const unlockResult = await unlockCard(card)
            if (!unlockResult.success) {
                return unlockResult
            }
        }

        const cancelCardResponse = await decorateApi(CardsApi.cancelCreditCard, {
            cardId: card.cardId,
            idempotencyKey: randomUUID(),
            changeCardStateRequest: {
                lastFour: card.lastFour,
            },
        })

        revalidatePath('/')
        return createSuccessResult(cancelCardResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

// Define common card request properties without authorizedUserId or spendControls
export interface BaseCardRequest {
    type: Type
}

// Base interface for primary user cards (without authorizedUserId)
interface PrimaryUserCardRequest extends BaseCardRequest {
    authorizedUserId?: never
    spendControls?: never
    authorizationControls?: any // Allow authorizationControls for primary users
}

// Base interface for authorized user cards (with authorizedUserId)
interface AuthorizedUserCardRequest extends BaseCardRequest {
    authorizedUserId: string
    spendControls?: Array<{
        amount: number
        currency?: string // Optional, will default to USD
        cadence: (typeof SpendControlsCadenceIntervalEnum)[keyof typeof SpendControlsCadenceIntervalEnum]
    }>
    authorizationControls?: any // Allow authorizationControls for authorized users
}

// Physical card types
interface PhysicalPrimaryUserCardRequest extends PrimaryUserCardRequest {
    type: 'physical'
    shipping: Address
    shippingMethod?: ShippingMethod
}

interface PhysicalAuthorizedUserCardRequest extends AuthorizedUserCardRequest {
    type: 'physical'
    shipping: Address
    shippingMethod?: ShippingMethod
}

// Virtual card types
interface VirtualPrimaryUserCardRequest extends PrimaryUserCardRequest {
    type: 'virtual'
    shipping?: never
    shippingMethod?: never
}

interface VirtualAuthorizedUserCardRequest extends AuthorizedUserCardRequest {
    type: 'virtual'
    shipping?: never
    shippingMethod?: never
}

// Union type for all card request options
export type CardRequestOptions =
    | PhysicalPrimaryUserCardRequest
    | PhysicalAuthorizedUserCardRequest
    | VirtualPrimaryUserCardRequest
    | VirtualAuthorizedUserCardRequest

export async function requestCard(options: CardRequestOptions): Promise<Result<Card>> {
    const {user} = await validateSession()
    try {
        // Validate required fields based on card type - runtime check
        if (options.type === 'physical' && !options.shipping) {
            return createErrorResult('Shipping address is required for physical cards')
        }
        
        // Validate that user has a credit account
        if (!user.creditAccountId) {
            return createErrorResult('User does not have an associated credit account')
        }

        // Prepare the base card request
        const createCardRequest: CreateCreditCardRequest = {
            type: options.type,
            creditAccountId: user.creditAccountId,
        }

        // Add authorized user ID if provided
        if ('authorizedUserId' in options && options.authorizedUserId) {
            createCardRequest.authorizedUserId = options.authorizedUserId

            // For authorized users, spend controls are required
            // Map spend controls to the required format with defaults
            if (options.spendControls) {
                const formattedSpendControls = options.spendControls.map(control => ({
                    limit: {
                        amount: control.amount,
                        currency: control.currency || 'USD',
                    },
                    cadence: {
                        interval: control.cadence || 'monthly', // Default to monthly if not specified
                    },
                }))

                // Create a set with the properly typed spend controls
                createCardRequest.spendControls = new Set(formattedSpendControls as any as SpendControl[])
            }
        }

        // Add authorization controls if provided
        if ('authorizationControls' in options && options.authorizationControls) {
            createCardRequest.authorizationControls = options.authorizationControls
        }

        // Add shipping information for physical cards
        if (options.type === 'physical' && options.shipping) {
            createCardRequest.shipTo = options.shipping
            createCardRequest.shippingMethod = options.shippingMethod || 'STANDARD'
        }

        const createCardResponse = await decorateApi(CardsApi.createCreditCard, {
            createCreditCardRequest: createCardRequest,
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/')
        return createSuccessResult(createCardResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        throw error
    }
}

export async function startBasisTheorySession(cardId: string, nonce: string): Promise<Result<void>> {
    try {
        if (!nonce || typeof nonce !== 'string' || nonce.length < 16) {
            return createErrorResult('Invalid nonce provided')
        }

        await validateSession()

        await decorateApi(CardsApi.authorizeSession, {
            idempotencyKey: randomUUID(),
            cardId,
            authorizeSessionRequest: {
                nonce,
            },
        })

        return createSuccessResult(undefined)
    } catch (error) {
        console.error('Failed to authorize Basis Theory session:', error)
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function activateCard(card: Pick<Card, 'cardId' | 'lastFour'>): Promise<Result<Card>> {
    try {
        await validateSession()

        const activateCardResponse = await decorateApi(CardsApi.activateCreditCard, {
            cardId: card.cardId,
            changeCardStateRequest: {
                lastFour: card.lastFour,
            },
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/')
        return createSuccessResult(activateCardResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function lockCard(card: Pick<Card, 'cardId' | 'lastFour' | 'state'>): Promise<Result<Card>> {
    try {
        await validateSession()

        if (card.state === 'locked') {
            return createSuccessResult(card as Card)
        }

        const lockCardResponse = await decorateApi(CardsApi.lockCreditCard, {
            cardId: card.cardId,
            changeCardStateRequest: {
                lastFour: card.lastFour,
            },
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/')
        return createSuccessResult(lockCardResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function unlockCard(card: Pick<Card, 'cardId' | 'lastFour' | 'state'>): Promise<Result<Card>> {
    try {
        await validateSession()

        if (card.state !== 'locked') {
            return createSuccessResult(card as Card)
        }

        const unlockCardResponse = await decorateApi(CardsApi.unlockCreditCard, {
            cardId: card.cardId,
            changeCardStateRequest: {
                lastFour: card.lastFour,
            },
            idempotencyKey: randomUUID(),
        })

        revalidatePath('/')
        return createSuccessResult(unlockCardResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}

export async function updateCardSpendControls(
    card: Pick<Card, 'cardId'>,
    spendControls?: {
        amount: number | null
        currency?: string
        cadence?: 'daily' | 'weekly' | 'monthly'
    }[],
): Promise<Result<Card>> {
    try {
        await validateSession()

        const updateCardRequest: UpdateCreditCardRequest = {}

        if (spendControls) {
            // Format spend controls to match the API requirements
            const formattedSpendControls = spendControls.map(control => ({
                limit: {
                    amount: control.amount,
                    currency: control.currency || 'USD',
                },
                cadence: {
                    interval: control.cadence || 'monthly',
                },
            }))

            // Create a set with the properly typed spend controls
            updateCardRequest.spendControls = new Set(formattedSpendControls as any as SpendControl[])
        }

        const updateCardResponse = await decorateApi(CardsApi.modifyCreditCard, {
            cardId: card.cardId,
            updateCreditCardRequest: updateCardRequest,
            idempotencyKey: randomUUID(),
        })

        return createSuccessResult(updateCardResponse)
    } catch (error) {
        if (APIError.isAPIError(error)) {
            return createErrorResult(await error.getErrorMessage())
        }
        if (error instanceof Error) {
            return createErrorResult(error.message)
        }
        return handleActionError(error)
    }
}
