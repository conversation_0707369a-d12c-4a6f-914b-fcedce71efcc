import NextAuth from 'next-auth'
import type {Profile} from 'next-auth'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from 'next-auth/providers/zitadel'
import {headers} from 'next/headers'
import {getDomainDiscoveryFromHeaders} from '@tallied-technologies/common'

const ZITADEL_USER_METADATA_KEY = 'urn:zitadel:iam:user:metadata'

function getScope(orgDomain?: string) {
    const basicScope = `openid profile email offline_access urn:zitadel:iam:org:projects:roles ${ZITADEL_USER_METADATA_KEY}`

    return `${basicScope} urn:zitadel:iam:org:project:id:${process.env.ZITADEL_PROJECT_ID}:aud urn:zitadel:iam:org:domain:primary:${orgDomain}`
}

// TODO: determine performance impact of and mitigations for what appears to be constructing a new provider each time
export function getZitadelProvider(domain: string) {
    return NextAuth({
        secret: process.env.NEXTAUTH_SECRET,
        providers: [
            ZitadelProvider({
                issuer: process.env.ZITADEL_ISSUER,
                clientId: process.env.ZITADEL_CLIENT_ID,
                client: {
                    token_endpoint_auth_method: 'none',
                },
                authorization: {
                    params: {
                        scope: getScope(domain),
                    },
                },
                // Build the User object from the OIDC profile returned by Zitadel.
                async profile(profile) {
                    const projectRoles = profile['urn:zitadel:iam:org:project:roles']

                    return {
                        id: profile.sub,
                        name: profile.name,
                        firstName: profile.given_name,
                        lastName: profile.family_name,
                        email: profile.email,
                        loginName: profile.username,
                        image: profile.picture,
                        roles: projectRoles ? Object.keys(projectRoles) : [],
                        creditAccountId: profile[ZITADEL_USER_METADATA_KEY]?.creditAccountId
                            ? Buffer.from(profile[ZITADEL_USER_METADATA_KEY].creditAccountId, 'base64').toString(
                                  'utf-8',
                              )
                            : undefined,
                        personId: profile[ZITADEL_USER_METADATA_KEY]?.personId
                            ? Buffer.from(profile[ZITADEL_USER_METADATA_KEY].personId, 'base64').toString('utf-8')
                            : undefined,
                        authorizedUserId: profile[ZITADEL_USER_METADATA_KEY]?.authorizedUserId
                            ? Buffer.from(profile[ZITADEL_USER_METADATA_KEY].authorizedUserId, 'base64').toString(
                                  'utf-8',
                              )
                            : undefined,
                    }
                },
            }),
        ],
        session: {
            strategy: 'jwt',
        },
        callbacks: {
            // JWT callback runs server-side only, and what is returned here is not exposed to the client,
            // unless forwarded explicitly via the session callback.
            async jwt({token, user, account}) {
                if (user) {
                    token.user ??= user
                }

                if (account) {
                    token.accessToken = account?.access_token
                    token.idToken = account?.id_token
                    token.refreshToken = account?.refresh_token
                    token.expiresAt = (account?.expires_at ?? 0) * 1000
                }

                // TODO: fix refresh tokens - or do we? compliance wise, if a user times out or logs out, they should need to re-authenticate
                token.error = undefined

                return token
            },
            // Session callback controls what reaches the client and only includes token when using jwt strategy.
            async session({session, token}) {
                if (token.user) {
                    session.user = token.user
                }

                if (token.error) {
                    session.error = token.error as string
                }

                return session
            },
        },
        debug: false,
        trustHost: true,
    })
}
