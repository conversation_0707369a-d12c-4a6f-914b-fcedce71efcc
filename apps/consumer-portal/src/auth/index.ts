'use server'
import type {Session} from 'next-auth'
import {cache} from 'react'
import {redirect} from 'next/navigation'
import {GetServerSidePropsContext, NextApiRequest, NextApiResponse} from 'next'
import {cookies, headers} from 'next/headers'
import {getZitadelProvider} from './provider'
import {getDomainDiscoveryFromHeaders} from '@tallied-technologies/common'
import {getToken} from 'next-auth/jwt'

export type AuthFunction = ((...args: []) => Promise<Session | null>) &
    ((...args: [GetServerSidePropsContext]) => Promise<Session | null>) &
    ((...args: [NextApiRequest, NextApiResponse]) => Promise<Session | null>)

export const getAuth = async (): Promise<AuthFunction> => {
    const hdrs = await headers()
    const discovery = await getDomainDiscoveryFromHeaders(hdrs)

    if (!discovery?.zitadelOrgDomain) {
        throw new Error('Organization Domain not found')
    }

    const nextAuth = getZitadelProvider(discovery.zitadelOrgDomain)
    return nextAuth.auth as AuthFunction
}

export async function auth() {
    const auth = await getAuth()
    return auth()
}

export async function signIn(provider: string, options?: Record<string, unknown>) {
    const hdrs = await headers()
  const discovery = await getDomainDiscoveryFromHeaders(hdrs)

    if (!discovery?.zitadelOrgDomain) {
        throw new Error('Organization Domain not found')
    }

    const nextAuth = getZitadelProvider(discovery.zitadelOrgDomain)
    return nextAuth.signIn(provider, options)
}

// Wrap in React Cache. This is used in the same request cycle, so if multiple pages/actions call validateSession
// The auth token will have a cached response. Note it's in the request cycle not application lifecycle.
// This means a server action that calls to validateSession return once and then be cached for later calls
export const validateSession = cache(async () => {
    const session = await auth()
    const req = {
        headers: Object.fromEntries(await headers()),
        cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value])),
    }
    const token = await getToken({
        req,
        secret: process.env.NEXTAUTH_SECRET,
        secureCookie: process.env.NODE_ENV === 'production',
        salt: process.env.NODE_ENV === 'production' ? '__Secure-authjs.session-token' : 'authjs.session-token',
    })

    if (!session?.user) {
        redirect('/login')
    }

    if (session?.error) {
        redirect(`/login?c=${encodeURIComponent(session.error)}`)
    }

    return {
        user: {...session.user, isAuthorizedUser: session.user.authorizedUserId !== undefined},
        accessToken: token?.accessToken,
    }
})
