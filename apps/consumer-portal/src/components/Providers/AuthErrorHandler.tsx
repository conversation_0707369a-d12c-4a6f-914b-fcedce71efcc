'use client'

import {useEffect, PropsWithChildren} from 'react'
import {useQueryClient} from '@tanstack/react-query'
import {handleUnauthorizedError, UnauthorizedError} from '@/services/apiWrapper'

/**
 * AuthErrorHandler intercepts UnauthorizedError errors thrown by the apiWrapper
 * and handles them by clearing React Query cache and logging out the user.
 */
export default function AuthErrorHandler({children}: PropsWithChildren) {
    const queryClient = useQueryClient()

    useEffect(() => {
        // Global error handler for unauthorized errors
        const handleError = async (event: ErrorEvent) => {
            const error = event.error

            // Check if the error is an UnauthorizedError
            if (error instanceof UnauthorizedError) {
                // Clear all React Query cache
                queryClient.clear()

                // Handle the unauthorized error (logout, redirect)
                await handleUnauthorizedError()

                // Prevent default error handling
                event.preventDefault()
            }
        }

        // Add global error handler
        window.addEventListener('error', handleError)

        // Cleanup
        return () => {
            window.removeEventListener('error', handleError)
        }
    }, [queryClient])

    return <>{children}</>
}
