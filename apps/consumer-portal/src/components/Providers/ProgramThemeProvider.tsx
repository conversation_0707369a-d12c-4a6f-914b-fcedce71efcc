'use client'

import React, {createContext, useContext} from 'react'
import {CssB<PERSON><PERSON>, ThemeProvider as MUIThemeProvider} from '@mui/material'
import {getTheme} from '@/theme'

interface ProgramThemeContextValue {
    programName: string
}

const ProgramThemeContext = createContext<ProgramThemeContextValue | null>(null)

export function useProgramTheme() {
    const context = useContext(ProgramThemeContext)
    if (!context) {
        throw new Error('useProgramTheme must be used within ProgramThemeProvider')
    }
    return context
}

interface ProgramThemeProviderProps {
    programName: string
    children: React.ReactNode
}

export default function ProgramThemeProvider({programName, children}: ProgramThemeProviderProps) {
    const theme = getTheme(programName)

    return (
        <ProgramThemeContext.Provider value={{programName}}>
            <MUIThemeProvider theme={theme}>
                <CssBaseline />
                {children}
            </MUIThemeProvider>
        </ProgramThemeContext.Provider>
    )
}
