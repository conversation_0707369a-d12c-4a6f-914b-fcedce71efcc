'use client'

import {QueryClient, QueryClientProvider} from '@tanstack/react-query'
import {PropsWithChildren, useState} from 'react'
import AuthErrorHandler from './AuthErrorHandler'

export default function QueryProvider({children}: PropsWithChildren) {
    const [queryClient] = useState(
        () =>
            new QueryClient({
                defaultOptions: {
                    queries: {
                        staleTime: 1000 * 60 * 5, // 5 minutes
                        refetchOnWindowFocus: false,
                    },
                },
            }),
    )

    return (
        <QueryClientProvider client={queryClient}>
            <AuthErrorHandler>{children}</AuthErrorHandler>
        </QueryClientProvider>
    )
}
