'use client'

import {Box, useMediaQuery, Theme} from '@mui/material'
import Image from 'next/image'
import {useProgramTheme} from '@/components/Providers/ProgramThemeProvider'

export default function BannerAd() {
    const {programName} = useProgramTheme()
    const desktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    if (programName === 'tallied') return null

    return (
        <Box sx={{position: 'relative', top: -2}}>
            {desktop ? (
                <Image src="/assets/ADA-banner-ad-desktop.png" alt="Ad Banner" width={389} height={48} />
            ) : (
                <Image src="/assets/ADA-banner-ad-mobile.png" alt="Ad Banner" width={375} height={46.88} />
            )}
        </Box>
    )
}
