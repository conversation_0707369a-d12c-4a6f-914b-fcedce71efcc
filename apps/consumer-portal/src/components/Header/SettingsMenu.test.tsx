import {render, screen, within} from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import SettingsMenu from './SettingsMenu'
import {useUXActions} from '../Context/UXActions'
import {signOut} from 'next-auth/react'
import {describe, it, expect, beforeEach, vi} from 'vitest'
import type {DisclosureTypeEnum, Disclosure} from '@tallied-technologies/services/Disclosures'

// Mock the next-auth signOut function
vi.mock('next-auth/react', () => ({
    signOut: vi.fn(),
}))

// Mock the UXActions context
vi.mock('../Context/UXActions', () => ({
    useUXActions: vi.fn(),
}))

// Mock MUI useMediaQuery
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => false, // Default to mobile view
    }
})

const mockDocuments = [
    {
        href: '/programs/sandbox/creditAccount/123456/cards/54321/protected',
        title: 'Card Art',
    },
] as Array<{href: string; title: string}>

describe('SettingsMenu', () => {
    const mockSetRightDrawerState = vi.fn()

    beforeEach(() => {
        vi.clearAllMocks()
        ;(useUXActions as ReturnType<typeof vi.fn>).mockReturnValue({
            setRightDrawerState: mockSetRightDrawerState,
        })
    })

    it('renders settings button', () => {
        render(<SettingsMenu documents={mockDocuments} />)
        expect(screen.getByLabelText('Settings')).toBeInTheDocument()
    })

    it('opens menu when settings button is clicked', async () => {
        render(<SettingsMenu documents={mockDocuments} />)

        await userEvent.click(screen.getByLabelText('Settings'))

        const menuElement = within(screen.getByLabelText('settings menu'))
        expect(menuElement.getByText('My Account')).toBeVisible()
        expect(menuElement.getByText('Documents')).toBeVisible()
        expect(menuElement.getByText('Logout')).toBeVisible()
    })

    it('calls signOut with correct parameters with Logout is clicked', async () => {
        render(<SettingsMenu documents={mockDocuments} />)

        await userEvent.click(screen.getByLabelText('Settings'))

        await userEvent.click(screen.getByText('Logout'))

        expect(signOut).toHaveBeenCalledOnce()
    })

    describe('My Account flow', () => {
        it('opens my account drawer and closes menu when My Account is clicked', async () => {
            render(<SettingsMenu documents={mockDocuments} />)

            // Open menu
            await userEvent.click(screen.getByLabelText('Settings'))

            // Click Profile
            const menuElement = within(screen.getByLabelText('settings menu'))
            await userEvent.click(menuElement.getByText('My Account'))

            // Menu should be closed
            expect(screen.queryByLabelText('settings menu')).toBeNull()

            // Drawer should be opened
            const drawerProfile = within(screen.getByLabelText('My Account settings'))
            expect(drawerProfile.getByText('My Account')).toBeInTheDocument()
            expect(mockSetRightDrawerState).toHaveBeenCalledWith(true)
        })

        it('closes my account drawer when close button is clicked', async () => {
            render(<SettingsMenu documents={mockDocuments} />)

            // Open menu and click Profile
            await userEvent.click(screen.getByLabelText('Settings'))

            const menuElement = within(screen.getByLabelText('settings menu'))
            await userEvent.click(menuElement.getByText('My Account'))

            // Click close button
            const drawerProfile = within(screen.getByLabelText('My Account settings'))
            const closeButton = drawerProfile.getByLabelText('Close')
            await userEvent.click(closeButton)

            expect(mockSetRightDrawerState).toHaveBeenCalledWith(false)
        })
    })

    describe('Documents flow', () => {
        it('displays documents when available', async () => {
            render(<SettingsMenu documents={mockDocuments} />)

            // Open menu and documents drawer
            await userEvent.click(screen.getByLabelText('Settings'))
            const menuElement = within(screen.getByLabelText('settings menu'))
            await userEvent.click(menuElement.getByText('Documents'))

            // Verify document content
            const drawerDocuments = within(screen.getByLabelText('Documents settings'))
            expect(drawerDocuments.getByText('Card Art')).toBeInTheDocument()
        })

        it('opens documents drawer and closes menu when Documents is clicked', async () => {
            render(<SettingsMenu documents={mockDocuments} />)

            // Open menu
            await userEvent.click(screen.getByLabelText('Settings'))

            // Click Documents
            const menuElement = within(screen.getByLabelText('settings menu'))
            await userEvent.click(menuElement.getByText('Documents'))

            // Menu should be closed
            expect(screen.queryByLabelText('settings menu')).toBeNull()

            // Drawer should be opened
            const drawerDocuments = within(screen.getByLabelText('Documents settings'))
            expect(drawerDocuments.getByText('Documents')).toBeInTheDocument()
            expect(mockSetRightDrawerState).toHaveBeenCalledWith(true)
        })

        it('closes documents drawer when close button is clicked', async () => {
            render(<SettingsMenu documents={mockDocuments} />)

            // Open menu and click Documents
            await userEvent.click(screen.getByLabelText('Settings'))

            const menuElement = within(screen.getByLabelText('settings menu'))
            await userEvent.click(menuElement.getByText('Documents'))

            // Click close button
            const drawerDocuments = within(screen.getByLabelText('Documents settings'))
            const closeButton = drawerDocuments.getByLabelText('Close')
            await userEvent.click(closeButton)

            expect(mockSetRightDrawerState).toHaveBeenCalledWith(false)
        })
    })

    describe('Logout flow', () => {
        it('calls signOut with correct parameters when Logout is clicked', async () => {
            render(<SettingsMenu documents={mockDocuments} />)

            // Open menu
            await userEvent.click(screen.getByLabelText('Settings'))

            // Click Logout
            await userEvent.click(screen.getByText('Logout'))

            expect(signOut).toHaveBeenCalledWith({
                redirect: true,
                callbackUrl: '/login',
            })
        })
    })
})
