import {Toolbar, Box, AppBar, Grid} from '@mui/material'
import Navigation from './Navigation'
import {Suspense} from 'react'
import SettingsMenuServer, {LoadingSettingsMenuServer} from './SettingsMenuServer'
import BannerAd from './BannerAd'
import { validateSession } from '@/auth'

export default async function Header() {
    const {user} = await validateSession()
    return (
        <Grid container justifyContent="center" sx={{width: '100%'}}>
            <AppBar
                position="static"
                sx={{backgroundColor: 'var(--mui-palette-brand-main)', color: 'var(--mui-palette-brand-contrastText)'}}
            >
                <Toolbar
                    component="nav"
                    sx={{
                        display: 'flex',
                        pt: 2,
                        pb: 2,
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <Navigation showNavigationItems={!user?.isAuthorizedUser} />
                    <Box sx={{display: 'flex', justifyContent: 'flex-end', gap: 1, flex: 1}}>
                        {/* <IconButton sx={{color: 'white'}} aria-label="Notifications">
                                <Badge
                                    badgeContent={2}
                                    max={9}
                                    anchorOrigin={{vertical: 'bottom', horizontal: 'right'}}
                                    color="secondary"
                                    sx={{'& .MuiBadge-badge': {border: theme => `1px solid ${theme.palette.primary.main}`}}}
                                >
                                    <Notifications />
                                </Badge>
                            </IconButton> */}
                        <Suspense fallback={<LoadingSettingsMenuServer />}>
                            <SettingsMenuServer />
                        </Suspense>
                    </Box>
                </Toolbar>
            </AppBar>
            <BannerAd />
        </Grid>
    )
}
