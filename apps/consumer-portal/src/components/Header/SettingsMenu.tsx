'use client'

import {
    <PERSON>er,
    Grid,
    IconButton,
    Menu,
    MenuItem,
    ListItemIcon,
    Typography,
    ListItemText,
    Divider,
    useMediaQuery,
    type Theme,
    ListItem,
    ListItemButton,
    List,
    Icon,
} from '@mui/material'
import {useState, type MouseEvent} from 'react'
import {useUXActions} from '../Context/UXActions'
import {signOut} from 'next-auth/react'
import {datadogRum} from '@datadog/browser-rum'

interface SettingsMenuProps {
    documents: {href: string; title: string}[]
}

export default function SettingsMenu({documents}: SettingsMenuProps) {
    const {setRightDrawerState} = useUXActions()
    const [settingsAnchorElement, setSettingsAnchorElement] = useState<HTMLElement | null>(null)
    const settingsMenuOpen = Boolean(settingsAnchorElement)

    const [myAccountOpen, setMyAccountOpen] = useState(false)
    const [documentsOpen, setDocumentsOpen] = useState(false)

    function setSettingsMenuAnchorElement(event: MouseEvent<HTMLElement>) {
        setSettingsAnchorElement(event.currentTarget)
    }

    function handleCloseSettingsMenu() {
        setSettingsAnchorElement(null)
    }

    function openMyAccount() {
        setMyAccountOpen(true)
        handleCloseSettingsMenu()
        setRightDrawerState(true)
    }

    function openDocuments() {
        setDocumentsOpen(true)
        handleCloseSettingsMenu()
        setRightDrawerState(true)
    }

    function closeDrawer() {
        setDocumentsOpen(false)
        setMyAccountOpen(false)
        setRightDrawerState(false)
    }

    //const hasDocuments = !('error' in documents) && documents.disclosures && documents.disclosures.length > 0
    const hasDocuments = true

    return (
        <Grid container sx={{width: 'max-content'}}>
            <IconButton
                sx={{color: 'inherit'}}
                onClick={setSettingsMenuAnchorElement}
                aria-controls={settingsMenuOpen ? 'settings-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={settingsMenuOpen ? 'true' : undefined}
                aria-label="Settings"
            >
                <Icon baseClassName="fas" className="fa-solid fa-user" fontSize="inherit" />
            </IconButton>
            <Menu
                anchorEl={settingsAnchorElement}
                id="settings-menu"
                open={settingsMenuOpen}
                onClose={handleCloseSettingsMenu}
                transformOrigin={{horizontal: 'right', vertical: 'top'}}
                anchorOrigin={{horizontal: 'right', vertical: 'bottom'}}
                slotProps={{
                    paper: {
                        'aria-label': 'settings menu',
                        sx: {
                            backgroundColor: 'var(--mui-palette-brand-main)',
                            color: 'var(--mui-palette-brand-contrastText)',
                        },
                    },
                }}
            >
                <MenuItem onClick={openMyAccount}>
                    <ListItemText>My Account</ListItemText>
                </MenuItem>
                <MenuItem onClick={openDocuments}>
                    <ListItemText>Documents</ListItemText>
                </MenuItem>
                <Divider />
                <MenuItem 
                    data-dd-action-name="Logout"
                    onClick={() => {
                        datadogRum.stopSession()
                        datadogRum.clearUser()
                        signOut({
                            redirect: true,
                            callbackUrl: '/login',
                        })
                    }}
                >
                    <ListItemText>Logout</ListItemText>
                    <ListItemIcon sx={{justifyContent: 'flex-end', color: 'inherit'}}>
                        <Icon baseClassName="fas" className="fa-arrow-right-from-bracket" fontSize="inherit" />
                    </ListItemIcon>
                </MenuItem>
            </Menu>
            <SettingsDrawer open={myAccountOpen} onClose={closeDrawer} title="My Account">
                Hello
            </SettingsDrawer>
            <SettingsDrawer open={documentsOpen} onClose={closeDrawer} title="Documents">
                {hasDocuments ? (
                    <List>
                        {documents?.map((document, indx) => (
                            <ListItem key={`${indx}+${document.href}`}>
                                <ListItemButton
                                    component="a"
                                    href={document.href}
                                    dense
                                    disableGutters
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <ListItemIcon sx={{minWidth: 24, mr: 2}}>
                                        <Icon
                                            baseClassName="fas"
                                            className="fa-solid fa-arrow-up-right-from-square"
                                            fontSize="inherit"
                                        />
                                    </ListItemIcon>
                                    <ListItemText
                                        primaryTypographyProps={{fontSize: 16, lineHeight: '24px', fontWeight: 600}}
                                    >
                                        {document.title}
                                    </ListItemText>
                                </ListItemButton>
                            </ListItem>
                        ))}
                    </List>
                ) : (
                    <Typography color="text.secondary" sx={{mt: 2}}>
                        No documents available
                    </Typography>
                )}
            </SettingsDrawer>
        </Grid>
    )
}

type DrawerContentProps = {
    open: boolean
    onClose: () => void
    title: string
    children?: React.ReactNode
}

function SettingsDrawer({open, onClose, title, children}: DrawerContentProps) {
    const desktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    return (
        <Drawer
            open={open}
            onClose={onClose}
            anchor={desktop ? 'right' : 'bottom'}
            variant="persistent"
            sx={{
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: desktop ? 375 : '100%',
                    height: '100%',
                    boxSizing: 'border-box',
                    p: 2,
                },
            }}
            role="dialog"
            aria-modal="true"
            aria-label={`${title} settings`}
        >
            <Grid container flexDirection="column">
                <Grid container alignItems="center">
                    <IconButton onClick={onClose} aria-label="Close">
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="inherit" />
                    </IconButton>
                    <Typography variant="h6" flex={1}>
                        {title}
                    </Typography>
                </Grid>
                {children}
            </Grid>
        </Drawer>
    )
}
