'use client'

import {useRef, useState} from 'react'
import {
    Button,
    IconButton,
    Box,
    ButtonProps,
    List,
    ListItem,
    ListItemButton,
    ListItemText,
    ListItemIcon,
    Popover,
    useMediaQuery,
    Grid,
    Icon,
} from '@mui/material'
import type {SvgIcon, Theme} from '@mui/material'
import {styled} from '@mui/material/styles'
import {TalliedLogo} from '@tallied-technologies/assets'
import Image from 'next/image'
import Link from 'next/link'
import {usePathname} from 'next/navigation'
import {useProgramTheme} from '@/components/Providers/ProgramThemeProvider'

interface NavigationButtonProps extends ButtonProps {
    navActive: boolean
    colorScheme?: 'main' | 'inverted'
}

const NavigationButton = styled(But<PERSON>, {
    name: 'MuiNavigationButton',
    slot: 'Root',
    shouldForwardProp: prop => !['navActive', 'colorScheme'].includes(prop as string),
})<NavigationButtonProps>(({theme, ...props}) => {
    const colorScheme = props.colorScheme || 'main'
    const schemes = {
        main: {
            active: {
                color: theme.palette.primary.main,
                backgroundColor: theme.palette.common.white,
            },
            default: {
                color: 'inherit',
                backgroundColor: 'inherit',
            },
        },
        inverted: {
            active: {
                color: theme.palette.common.white,
                backgroundColor: theme.palette.primary.main,
            },
            default: {
                color: theme.palette.primary.main,
                backgroundColor: 'transparent',
            },
        },
    }

    const scheme = schemes[colorScheme]
    const activeStyles = scheme.active
    const defaultStyles = scheme.default

    return {
        ...defaultStyles,
        padding: theme.spacing(1, 2),
        textTransform: 'initial',
        ...(props.navActive && activeStyles),
        '&:hover': activeStyles,
        '&:focus': activeStyles,
        '&:active': activeStyles,
    }
})

type NavigationItem = {
    name: string
    icon?: string
    path: string
}

const navigationList: Array<NavigationItem> = [
    {
        path: '/',
        name: 'Home',
        icon: 'fa-home',
    },
    {
        path: '/payments',
        name: 'Payments',
    },
    {
        path: '/rewards',
        name: 'Rewards',
    },
]

export default function Navigation({showNavigationItems = true}: {showNavigationItems?: boolean}) {
    const {programName} = useProgramTheme()
    const desktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))
    const path = usePathname()
    const [navigationAnchorEl, setNavigationAnchorEl] = useState<HTMLElement | null>(null)
    const navigationPopperElement = useRef<HTMLDivElement | null>(null)
    const navigationMenuOpen = Boolean(navigationAnchorEl)

    const popoverId = navigationMenuOpen ? 'navigation-popper' : undefined

    function handleNavigationClick() {
        setNavigationAnchorEl(navigationPopperElement.current as HTMLElement)
    }

    function handleNavigationClose() {
        setNavigationAnchorEl(null)
    }

    if (!showNavigationItems) {
        return (
            <Grid container alignItems="center" ref={navigationPopperElement} flex="1">
                <Link href="/" style={{height: '33px'}}>
                    {getBrandAsset(programName)}
                </Link>
            </Grid>
        )
    }

    return (
        <>
            <Grid container alignItems="center" ref={navigationPopperElement} flex="1" flexWrap="nowrap">
                <IconButton
                    color="inherit"
                    edge="start"
                    sx={{mr: 2, display: desktop ? 'none' : 'inline-flex'}}
                    onClick={handleNavigationClick}
                    aria-describedby={popoverId}
                    aria-label="Navigation Menu"
                >
                    {navigationMenuOpen ? (
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="inherit" />
                    ) : (
                        <Icon baseClassName="fas" className="fa-solid fa-bars" fontSize="inherit" />
                    )}
                </IconButton>
                <Link href="/" style={{height: '33px'}}>
                    {getBrandAsset(programName)}
                </Link>
            </Grid>
            <Box
                sx={{
                    display: desktop ? 'flex' : 'none',
                    gap: 6,
                    '& a': {color: 'inherit', textDecoration: 'none'},
                    flex: 1,
                    justifyContent: 'space-between',
                }}
            >
                {navigationList.map(navigationItem => (
                    <Link key={navigationItem.path} href={navigationItem.path}>
                        <NavigationButton
                            startIcon={
                                navigationItem.icon ? (
                                    <Icon
                                        baseClassName="fas"
                                        className={`fa-solid ${navigationItem.icon}`}
                                        fontSize="inherit"
                                        sx={{fontSize: '14px !important'}}
                                    />
                                ) : null
                            }
                            navActive={path === navigationItem.path}
                            colorScheme={programName === 'ada' ? 'inverted' : 'main'}
                            sx={{fontSize: '20px'}}
                            size="small"
                        >
                            {navigationItem.name}
                        </NavigationButton>
                    </Link>
                ))}
            </Box>
            <Popover
                id={popoverId}
                open={navigationMenuOpen}
                anchorEl={navigationAnchorEl}
                onClose={handleNavigationClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                sx={{display: desktop ? 'none' : 'block'}}
                slotProps={{
                    paper: {
                        sx: {
                            width: '100%',
                            maxWidth: '100%',
                            backgroundColor: theme => theme.palette.primary.main,
                            borderTopLeftRadius: 0,
                            borderTopRightRadius: 0,
                            color: theme => theme.palette.common.white,
                        },
                    },
                }}
                marginThreshold={null}
            >
                <Box onClick={handleNavigationClose}>
                    <List sx={{px: 4}}>
                        {navigationList.map(navigationItem => (
                            <ListItem key={navigationItem.path} disablePadding disableGutters alignItems="flex-start">
                                <ListItemButton component={Link} href={navigationItem.path} dense disableGutters>
                                    <ListItemIcon sx={{minWidth: 24, mr: '13px'}}>
                                        {navigationItem.icon ? (
                                            <Icon
                                                baseClassName="fas"
                                                className={`fa-solid ${navigationItem.icon}`}
                                                fontSize="inherit"
                                                sx={{fontSize: '14px !important', color: 'white'}}
                                            />
                                        ) : null}
                                    </ListItemIcon>
                                    <ListItemText
                                        sx={{flex: '0 0 auto'}}
                                        primaryTypographyProps={{fontSize: 20, fontWeight: 600}}
                                    >
                                        {navigationItem.name}
                                    </ListItemText>
                                </ListItemButton>
                            </ListItem>
                        ))}
                    </List>
                </Box>
            </Popover>
        </>
    )
}

function getBrandAsset(programName: string) {
    if (programName === 'ada') {
        return <Image src="/assets/ADA.png" priority alt="ADA - Cardholder Portal Action" width={194} height={33} />
    }
    return <Image src={TalliedLogo} priority alt="Tallied Logo - Portal" width={142} />
}
