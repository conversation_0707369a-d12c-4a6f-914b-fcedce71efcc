import {getDisclosures} from '@/data'
import SettingsMenu from './SettingsMenu'
import {Grid, Icon, IconButton} from '@mui/material'
import {APIError} from '@/services/APIError'

interface Document {
    title: string
    href: string
}

export default async function SettingsMenuServer() {
    try {
        const disclosuresResponse = await getDisclosures()

        const documents: Document[] =
            disclosuresResponse.disclosures?.map(disclosure => ({
                title: disclosure.title ?? 'Untitled Document',
                href: disclosure.links?.href ?? '#',
            })) ?? []

        if (documents.length === 0) {
            throw new Error('No documents found')
        }

        return <SettingsMenu documents={documents} />
    } catch (error) {
        // Fallback to static documents if API fails
        const PUBLIC_DOCS_DOMAIN = process.env.PUBLIC_DOCS_DOMAIN ?? 'https://documents.tallied.io'
        const documents: Document[] = [
            {
                title: 'Credit Card Agreement',
                href: `${PUBLIC_DOCS_DOMAIN}/credit_card_agreement.pdf`,
            },
            {
                title: 'TCPA Consent',
                href: `${PUBLIC_DOCS_DOMAIN}/tcpa_consent.pdf`,
            },
            {
                title: 'Electronic Communications Disclosure and Agreement',
                href: `${PUBLIC_DOCS_DOMAIN}/electronic_communications_disclosure_and_agreement_tallied.pdf`,
            },
        ]
        return <SettingsMenu documents={documents} />
    }
}

export function LoadingSettingsMenuServer() {
    return (
        <Grid container>
            <IconButton sx={{color: 'inherit', opacity: 0.6}} aria-label="Settings">
                <Icon baseClassName="fas" className="fa-solid fa-user" fontSize="inherit" />
            </IconButton>
        </Grid>
    )
}
