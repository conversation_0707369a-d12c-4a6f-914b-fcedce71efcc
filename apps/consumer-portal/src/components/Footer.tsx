'use client'

import {Grid, Typography, Link} from '@mui/material'
import {TalliedLogo} from '@tallied-technologies/assets'
import Image from 'next/image'
import {useProgramTheme} from '@/components/Providers/ProgramThemeProvider'

export default function Footer() {
    const {programName} = useProgramTheme()
    return (
        <Grid
            component="footer"
            flexDirection="row"
            alignSelf="stretch"
            sx={theme => ({
                color: 'var(--mui-palette-brand-contrastText)',
                background: 'var(--mui-palette-brand-main)',
                py: 3,
                px: 11,
                [theme.breakpoints.down('lg')]: {
                    p: 3,
                },
            })}
        >
            <Grid
                container
                justifyContent="space-between"
                alignItems="flex-start"
                alignSelf="stretch"
                sx={theme => ({
                    [theme.breakpoints.down('lg')]: {
                        flexDirection: 'row-reverse',
                    },
                })}
            >
                {getBrandAsset(programName)}

                <Grid
                    role="navigation"
                    aria-label="Footer Navigation"
                    container
                    alignContent="flex-start"
                    flex={1}
                    flexDirection="row"
                    justifyContent="flex-end"
                    alignItems="flex-start"
                    gap={15}
                    alignSelf="stretch"
                    pt={1}
                    sx={theme => ({
                        [theme.breakpoints.down('lg')]: {
                            flexDirection: 'column',
                            gap: 2,
                        },
                    })}
                >
                    <Typography>Contact</Typography>
                    <Typography>Legal</Typography>
                    <Typography component={Link} href="https://www.tallied.io/legals/privacy-policy" color="inherit">
                        Privacy
                    </Typography>
                    <Typography>Help Center</Typography>
                </Grid>
            </Grid>
        </Grid>
    )
}

function getBrandAsset(programName: string) {
    if (programName === 'ada') {
        return <Image src="/assets/ADA.png" priority alt="ADA - Cardholder Portal Action" width={194} height={33} />
    }
    return <Image src={TalliedLogo} priority alt="Tallied Logo - Portal" width={142} />
}
