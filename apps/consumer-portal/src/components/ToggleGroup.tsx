import React, {useState, useEffect, useRef} from 'react'
import {Box, Button, Typography} from '@mui/material'

// Props for individual toggle items
export interface ToggleItemProps {
    value: string
    label: React.ReactNode
    disabled?: boolean
}

// Props for the toggle group container
export interface ToggleGroupProps {
    value: string
    onChange: (value: string) => void
    options: ToggleItemProps[]
    color?: 'primary' | 'secondary'
    size?: 'small' | 'medium'
    fullWidth?: boolean
    disabled?: boolean
}

export const ToggleGroup: React.FC<ToggleGroupProps> = ({
    value,
    onChange,
    options,
    color = 'secondary',
    size = 'medium',
    fullWidth = false,
    disabled = false,
}) => {
    // Refs for measuring button widths
    const buttonsRef = useRef<(HTMLButtonElement | null)[]>([])
    const containerRef = useRef<HTMLDivElement | null>(null)

    // State for indicator position and dimensions
    const [indicator, setIndicator] = useState({
        left: 0,
        width: 0,
    })

    // Calculate indicator position and size based on the selected button
    useEffect(() => {
        const updateIndicator = () => {
            // Find the selected button's index
            const selectedIndex = options.findIndex(option => option.value === value)
            if (selectedIndex === -1 || !buttonsRef.current[selectedIndex]) return

            const button = buttonsRef.current[selectedIndex]
            const container = containerRef.current

            if (button && container) {
                // Get positions relative to the container
                const buttonRect = button.getBoundingClientRect()
                const containerRect = container.getBoundingClientRect()

                setIndicator({
                    left: buttonRect.left - containerRect.left,
                    width: buttonRect.width,
                })
            }
        }

        // Immediate update
        updateIndicator()

        // Listen for resize events to recalculate
        window.addEventListener('resize', updateIndicator)
        return () => {
            window.removeEventListener('resize', updateIndicator)
        }
    }, [value, options])

    // Handle button clicks
    const handleClick = (newValue: string) => {
        if (!disabled) {
            onChange(newValue)
        }
    }

    return (
        <Box
            ref={containerRef}
            sx={{
                display: 'flex',
                borderRadius: 2,
                bgcolor: `${color}.light`,
                position: 'relative',
                width: fullWidth ? '100%' : 'auto',
            }}
        >
            {options.map((option, index) => (
                <Button
                    key={option.value}
                    ref={el => {
                        buttonsRef.current[index] = el
                    }}
                    onClick={() => handleClick(option.value)}
                    disabled={disabled || option.disabled}
                    sx={{
                        flex: fullWidth ? 1 : 'auto',
                        color: value === option.value ? 'common.white' : 'text.secondary',
                        position: 'relative',
                        zIndex: 2,
                        minWidth: 0,
                        fontSize: 12,
                        lineHeight: '24px',
                        textTransform: 'none',
                        transition: 'color 0.3s ease',
                    }}
                >
                    {option.label}
                </Button>
            ))}

            {/* Sliding indicator */}
            <Box
                sx={{
                    position: 'absolute',
                    left: indicator.left,
                    width: indicator.width,
                    height: '100%',
                    borderRadius: 2,
                    backgroundColor: `${color}.main`,
                    transition: 'left 0.3s ease, width 0.3s ease',
                    zIndex: 1,
                    pointerEvents: 'none',
                    willChange: 'left, width',
                }}
            />
        </Box>
    )
}
