'use client'

import {List, ListSubheader, Collapse, Icon, Box} from '@mui/material'
import {useState, ReactNode} from 'react'

interface CollapsibleSectionProps {
    title: string
    count: number
    children: ReactNode
}

export default function CollapsibleSection({title, count, children}: CollapsibleSectionProps) {
    const [isOpen, setIsOpen] = useState(true)

    return (
        <List disablePadding component="ol" sx={{width: '100%'}}>
            <ListSubheader
                component="div"
                onClick={() => setIsOpen(!isOpen)}
                role="button"
                tabIndex={0}
                aria-expanded={isOpen}
                sx={{
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    },
                }}
            >
                <Box display="flex" alignItems="center" sx={{mr: 1}}>
                    {isOpen ? (
                        <Icon baseClassName="fas" className="fa-solid fa-chevron-up" fontSize="inherit" />
                    ) : (
                        <Icon baseClassName="fas" className="fa-solid fa-chevron-down" fontSize="inherit" />
                    )}
                </Box>
                {title} ({count})
            </ListSubheader>
            <Collapse in={isOpen} timeout="auto">
                {children}
            </Collapse>
        </List>
    )
}
