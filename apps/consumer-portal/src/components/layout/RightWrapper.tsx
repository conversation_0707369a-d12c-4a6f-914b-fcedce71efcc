'use client'

import {Box} from '@mui/material'
import {styled} from '@mui/material/styles'

const RightContentWrapper = styled(Box)(({theme}) => ({
    display: 'flex',
    width: '100%',
    flexDirection: 'column',
    alignItems: 'center',
    //border: '1px solid black',
    maxWidth: 640,
    [theme.breakpoints.down('lg')]: {
        maxWidth: '100%',
    },
}))

export default function RightWrapper({children}: {children: React.ReactNode}) {
    return <RightContentWrapper>{children}</RightContentWrapper>
}
