'use client'

import {Grid} from '@mui/material'
import {styled} from '@mui/material/styles'

const DashboardShellStyled = styled(Grid)(({theme}) => ({
    gap: theme.spacing(5),
    height: '100%',
    flexWrap: 'nowrap',
    [theme.breakpoints.down('lg')]: {
        gap: 0,
        maxWidth: '100%',
    },
}))

export default function DashboardShell({children}: {children: React.ReactNode}) {
    return (
        <DashboardShellStyled container flexDirection="column" alignItems="center" alignSelf="stretch">
            {children}
        </DashboardShellStyled>
    )
}
