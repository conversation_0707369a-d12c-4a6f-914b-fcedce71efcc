'use client'

import {Box} from '@mui/material'
import {styled} from '@mui/material/styles'
import {useUXActions} from '../Context/UXActions'

const DRAWER_WIDTH = 375

const ContentWrapperStyled = styled(Box)(({theme}) => ({
    display: 'flex',
    gap: theme.spacing(5),
    alignSelf: 'stretch',
    justifyContent: 'center',
    [theme.breakpoints.down('lg')]: {
        flexWrap: 'wrap',
    },
    transition: theme.transitions.create(['all'], {
        duration: theme.transitions.duration.enteringScreen,
    }),
    marginLeft: 0,
    marginRight: 0,
    '&.leftDrawerOpen': {
        [theme.breakpoints.up('lg')]: {
            marginLeft: DRAWER_WIDTH,
        },
    },
    '&.rightDrawerOpen': {
        [theme.breakpoints.up('lg')]: {
            marginRight: DRAWER_WIDTH,
        },
    },
    willChange: 'margin-left, margin-right',
}))

export default function ContentWrapper({children}: {children: React.ReactNode}) {
    const {leftDrawerOpen, rightDrawerOpen} = useUXActions()

    // Create className string based on drawer states
    const classNames = [leftDrawerOpen ? 'leftDrawerOpen' : '', rightDrawerOpen ? 'rightDrawerOpen' : '']
        .filter(Boolean)
        .join(' ')

    return <ContentWrapperStyled className={classNames}>{children}</ContentWrapperStyled>
}
