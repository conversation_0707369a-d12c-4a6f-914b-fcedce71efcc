'use client'

import {Box} from '@mui/material'
import {styled} from '@mui/material/styles'

const LeftContentWrapper = styled(Box)(({theme}) => ({
    display: 'flex',
    width: '100%',
    flexDirection: 'column',
    alignItems: 'center',
    //border: '1px solid black',
    maxWidth: 343,
    [theme.breakpoints.down('lg')]: {
        maxWidth: '100%',
    },
}))

export default function LeftWrapper({children}: {children: React.ReactNode}) {
    return <LeftContentWrapper>{children}</LeftContentWrapper>
}
