'use client'

import {styled} from '@mui/material/styles'

const AppShellStyled = styled('div')(({theme}) => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    background: 'var(--mui-palette-background-default)',
    height: '100vh'
}))

export default function AppShell({children}: {children: React.ReactNode}) {
    return <AppShellStyled>{children}</AppShellStyled>
}
