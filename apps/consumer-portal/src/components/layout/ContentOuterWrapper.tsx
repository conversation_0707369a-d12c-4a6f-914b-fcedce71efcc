'use client'

import {styled} from '@mui/material/styles'

const ContentOuterWrapperStyled = styled('div')(({theme}) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: theme.spacing(4),
    flex: '1 0 0',
    padding: theme.spacing(3),
    paddingTop: 0,
    width: '100%',
}))

export default function ContentOuterWrapper({children}: {children: React.ReactNode}) {
    return <ContentOuterWrapperStyled>{children}</ContentOuterWrapperStyled>
}
