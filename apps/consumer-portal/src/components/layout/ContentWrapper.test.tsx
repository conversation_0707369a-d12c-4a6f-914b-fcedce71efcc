import {describe, it, expect} from 'vitest'
import {render, screen} from '@testing-library/react'
import ContentWrapper from './ContentWrapper'
import UXActionsProvider, {useUXActions} from '../Context/UXActions'
import {renderHook, act} from '@testing-library/react'

describe('ContentWrapper', () => {
    it('renders without drawer classes by default', () => {
        render(
            <UXActionsProvider>
                <ContentWrapper>
                    <div>Content</div>
                </ContentWrapper>
            </UXActionsProvider>,
        )

        const wrapper = screen.getByText('Content').parentElement
        expect(wrapper).not.toHaveClass('leftDrawerOpen')
        expect(wrapper).not.toHaveClass('rightDrawerOpen')
    })

    it('adds leftDrawerOpen class when left drawer is open', () => {
        const {result} = renderHook(() => useUXActions(), {
            wrapper: ({children}) => (
                <UXActionsProvider>
                    <ContentWrapper>
                        <div>Content</div>
                    </ContentWrapper>
                    {children}
                </UXActionsProvider>
            ),
        })

        act(() => {
            result.current.setLeftDrawerState(true)
        })

        const wrapper = screen.getByText('Content').parentElement
        expect(wrapper).toHaveClass('leftDrawerOpen')
        expect(wrapper).not.toHaveClass('rightDrawerOpen')
    })

    it('adds rightDrawerOpen class when right drawer is open', () => {
        const {result} = renderHook(() => useUXActions(), {
            wrapper: ({children}) => (
                <UXActionsProvider>
                    <ContentWrapper>
                        <div>Content</div>
                    </ContentWrapper>
                    {children}
                </UXActionsProvider>
            ),
        })

        act(() => {
            result.current.setRightDrawerState(true)
        })

        const wrapper = screen.getByText('Content').parentElement
        expect(wrapper).not.toHaveClass('leftDrawerOpen')
        expect(wrapper).toHaveClass('rightDrawerOpen')
    })

    it('adds both classes when both drawers are open', () => {
        const {result} = renderHook(() => useUXActions(), {
            wrapper: ({children}) => (
                <UXActionsProvider>
                    <ContentWrapper>
                        <div>Content</div>
                    </ContentWrapper>
                    {children}
                </UXActionsProvider>
            ),
        })

        act(() => {
            result.current.setLeftDrawerState(true)
            result.current.setRightDrawerState(true)
        })

        const wrapper = screen.getByText('Content').parentElement
        expect(wrapper).toHaveClass('leftDrawerOpen')
        expect(wrapper).toHaveClass('rightDrawerOpen')
    })
})
