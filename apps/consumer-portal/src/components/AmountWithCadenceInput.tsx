import {Box, TextField, type TextFieldProps} from '@mui/material'
import {ToggleGroup} from './ToggleGroup'

export type Cadence = 'monthly' | 'weekly' | 'daily'

export interface AmountWithCadenceValue {
    amount: number | null
    cadence: Cadence
}

export interface AmountWithCadenceInputProps {
    value: AmountWithCadenceValue
    onChange: (value: AmountWithCadenceValue) => void
    amountProps?: Partial<TextFieldProps>
    disabled?: boolean
}

export default function AmountWithCadenceInput({
    value,
    onChange,
    amountProps,
    disabled = false,
}: AmountWithCadenceInputProps) {
    // Handle amount input with validation
    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let newAmount = e.target.value

        // If the value is empty or "0", keep it as is (represents no limit)
        if (newAmount === '' || newAmount === '0') {
            onChange({...value, amount: null})
            return
        }

        // If it's a negative number or not a number, set to empty
        const numValue = Number(newAmount)
        if (isNaN(numValue) || numValue < 0) {
            onChange({...value, amount: null})
            return
        }

        // Otherwise set the valid value
        onChange({...value, amount: numValue * 100})
    }

    // Handle cadence change
    const handleCadenceChange = (newCadence: string) => {
        onChange({...value, cadence: newCadence as Cadence})
    }

    return (
        <Box sx={{display: 'flex', flexDirection: 'row', gap: 3, wrap: 'nowrap', alignItems: 'center'}}>
            <TextField
                label="Amount"
                fullWidth
                value={value.amount ? value.amount / 100 : ''}
                placeholder="0.00"
                onChange={handleAmountChange}
                type="number"
                disabled={disabled}
                slotProps={{
                    input: {
                        startAdornment: (
                            <Box component="span" sx={{mr: 1}}>
                                $
                            </Box>
                        ),
                    },
                    htmlInput: {
                        min: 0,
                        autoComplete: 'off',
                    },
                }}
                {...amountProps}
            />
            <ToggleGroup
                value={value.cadence}
                onChange={handleCadenceChange}
                options={[
                    {value: 'monthly', label: 'month'},
                    {value: 'weekly', label: 'week'},
                    {value: 'daily', label: 'day'},
                ]}
                color="secondary"
                fullWidth
                disabled={disabled}
            />
        </Box>
    )
}
