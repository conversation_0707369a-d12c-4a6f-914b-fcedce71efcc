import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen} from '@/test-utils'
import WelcomeBanner, {LoadingWelcomeBanner} from './WelcomeBanner'
import {useUserData} from '@/hooks/useUserData'

// Mock the useUserData hook
vi.mock('@/hooks/useUserData', () => ({
    useUserData: vi.fn(),
}))

describe('WelcomeBanner', () => {
    const mockUser = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        programHost: 'test.host',
        programName: 'sandbox',
        roles: ['user'],
        creditAccountId: '123',
        personId: '456',
        isAuthorizedUser: true,
        authorizedUserId: '789',
        id: '101112',
        name: '<PERSON>',
        image: null,
    }

    beforeEach(() => {
        vi.clearAllMocks()
    })

    describe('Component', () => {
        it('displays user first name when data is loaded', () => {
            vi.mocked(useUserData).mockReturnValue({
                data: mockUser,
                isLoading: false,
                error: null,
                isError: false,
                isPending: false,
                isSuccess: true,
                status: 'success',
                fetchStatus: 'idle',
            } as any)

            render(<WelcomeBanner />)
            expect(screen.getByText('Hi John!')).toBeInTheDocument()
        })

        it('renders children when provided', () => {
            vi.mocked(useUserData).mockReturnValue({
                data: mockUser,
                isLoading: false,
                error: null,
                isError: false,
                isPending: false,
                isSuccess: true,
                status: 'success',
                fetchStatus: 'idle',
            } as any)

            render(
                <WelcomeBanner>
                    <div>Test Content</div>
                </WelcomeBanner>,
            )
            expect(screen.getByText('Test Content')).toBeInTheDocument()
        })

        it('returns null when no user data is available', () => {
            vi.mocked(useUserData).mockReturnValue({
                data: null,
                isLoading: false,
                error: null,
                isError: false,
                isPending: false,
                isSuccess: true,
                status: 'success',
                fetchStatus: 'idle',
            } as any)

            const {container} = render(<WelcomeBanner />)
            expect(container.firstChild).toBeNull()
        })
    })
})
