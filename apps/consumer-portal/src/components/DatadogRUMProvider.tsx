'use client'

import {useSession} from 'next-auth/react'
import {useEffect, useRef} from 'react'
import {datadogRum} from '@datadog/browser-rum'

export default function DatadogRUMProvider({env, version}: {env: string; version: string}) {
    const {data: session, status} = useSession()
    const isInitialized = useRef(false)

    useEffect(() => {
        if (!isInitialized.current && status === 'authenticated' && session?.user?.id) {
            datadogRum.init({
                applicationId: 'd520d9fc-c8b8-4d12-89bf-77e70662b733',
                clientToken: 'pub96006baf8aa2a4b44d8119f81cd42c07',
                site: 'us3.datadoghq.com',
                service: 'consumer-portal-client',
                env: env,
                version: version,
                sessionSampleRate: 100,
                sessionReplaySampleRate: 100,
                trackUserInteractions: true,
                trackResources: true,
                trackLongTasks: true,
                silentMultipleInit: true,
                defaultPrivacyLevel: 'mask-user-input',
                allowedTracingUrls: [
                    {match: /^https:\/\/.*\.tallied\.io\//, propagatorTypes: ['datadog', 'tracecontext']},
                    {match: /^https:\/\/.*\.basistheory\.com\//, propagatorTypes: ['datadog', 'tracecontext']},
                    {match: /^https:\/\/.*\.amazonaws\.com\//, propagatorTypes: ['datadog', 'tracecontext']},
                ],
            })
            
            datadogRum.setUser({
                id: session.user.id,
                name: `${session.user.firstName} ${session.user.lastName}`,
                email: session.user.email ?? undefined,
                roles: session.user.roles,
                creditAccountId: session.user.creditAccountId,
                authorizedUserId: session.user.authorizedUserId,
                personId: session.user.personId,
            })

            isInitialized.current = true
        } else if (status === 'unauthenticated') {
            datadogRum.addAction('User is unauthenticated')
            datadogRum.stopSession()
            datadogRum.clearUser()
        }
    }, [env, session, status, version])

    return null
}
