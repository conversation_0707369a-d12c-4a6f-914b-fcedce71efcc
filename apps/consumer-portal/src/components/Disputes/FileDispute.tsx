'use client'

import {But<PERSON>} from '@mui/material'
import {useState} from 'react'
import Disputes from '@/components/Disputes/Disputes'
import type { GetTransactionEvents200ResponseEventsInner } from '@/services/controllers/transactionEvents'

interface TransactionProps extends GetTransactionEvents200ResponseEventsInner {}

export default function FileDispute(props: TransactionProps) {
    const [modalOpen, setModalOpen] = useState(false)
    return (
        <>
            <Button variant="contained" onClick={() => setModalOpen(true)}>
                Report a Problem
            </Button>
            {modalOpen && <Disputes open={modalOpen} onClose={() => setModalOpen(false)} transaction={props} />}
        </>
    )
}
