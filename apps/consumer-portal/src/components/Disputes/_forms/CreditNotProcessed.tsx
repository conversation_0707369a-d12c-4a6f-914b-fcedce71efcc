'use client'

import {useFormStatus} from 'react-dom'
import {
    Button,
    FormGroup,
    FormControlLabel,
    Grid,
    Typography,
    TextField,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Collapse,
    FormLabel,
    Radio,
    RadioGroup,
    Icon,
} from '@mui/material'
import {DatePicker} from '@mui/x-date-pickers/DatePicker'
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'
import dayjs from 'dayjs'
import type {DisputeFormState} from '../DisputeTypes'
import {DisputeProductType, type CreateDisputeRequest} from '@/services/controllers/disputes'
import {useState} from 'react'

interface CreditNotProcessedFormProps {
    formState: DisputeFormState['creditNotProcessed']
    onChange: (fields: Partial<Extract<CreateDisputeRequest, {reason: 'creditNotProcessed'}>>) => void
    onPrevious: () => void
    onBlur: (fieldName: string) => void
}

export default function CreditNotProcessedForm({formState, onChange, onPrevious, onBlur}: CreditNotProcessedFormProps) {
    const [productType, setProductType] = useState<DisputeProductType | undefined>()
    const [productReturned, setProductReturned] = useState<'yes' | 'no' | null>(null)

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Grid container flexDirection="column" gap={2}>
                <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                    &quot;I requested a refund for this purchase but never received it&quot;
                </Typography>
                <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                    Tell us more...
                </Typography>
                <Typography fontSize={16} color="textSecondary">
                    Providing more details will help us to process your report but is not required.
                </Typography>
                <FormGroup sx={{gap: 3}}>
                    <FormControl>
                        <InputLabel id="purchaseType" shrink>
                            Purchase Type
                        </InputLabel>
                        <Select
                            label="Purchase Type"
                            labelId="purchaseType"
                            displayEmpty
                            name="productType"
                            value={productType || ''}
                            onChange={e => {
                                setProductType(e.target.value as DisputeProductType)
                            }}
                            renderValue={(selected: string) => {
                                if (!selected || selected.length === 0) {
                                    return <Typography>What kind of purchase was it</Typography>
                                }
                                const key = Object.keys(DisputeProductType).find(
                                    (key): key is keyof typeof DisputeProductType =>
                                        DisputeProductType[key as keyof typeof DisputeProductType] === selected,
                                )
                                return key || 'Unknown Product Type'
                            }}
                        >
                            {(Object.keys(DisputeProductType) as Array<keyof typeof DisputeProductType>).map(
                                productType => (
                                    <MenuItem key={productType} value={DisputeProductType[productType]}>
                                        {productType}
                                    </MenuItem>
                                ),
                            )}
                        </Select>
                    </FormControl>

                    <Collapse
                        in={productType === DisputeProductType.Merchandise}
                        sx={{
                            display: productType === DisputeProductType.Merchandise ? 'initial' : 'none',
                        }}
                        data-testid="merchandise-question-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <FormControl>
                                <FormLabel>Did you return your purchase?</FormLabel>
                                <RadioGroup
                                    row
                                    name="productReturned"
                                    value={productReturned}
                                    onChange={e => setProductReturned(e.target.value as 'yes' | 'no')}
                                >
                                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                                    <FormControlLabel value="no" control={<Radio />} label="No" />
                                </RadioGroup>
                            </FormControl>
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={productReturned === 'yes' && productType === DisputeProductType.Merchandise}
                        sx={{
                            display:
                                productReturned === 'yes' && productType === DisputeProductType.Merchandise
                                    ? 'initial'
                                    : 'none',
                        }}
                        data-testid="merchandise-return-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <DatePicker
                                label="When did you ship it?"
                                name="returnDate"
                                value={formState.returnDate?.value ? dayjs(formState.returnDate.value) : null}
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error: formState.returnDate?.touched && !!formState.returnDate?.error,
                                        helperText: formState.returnDate?.touched
                                            ? formState.returnDate?.error
                                            : undefined,
                                        onBlur: () => onBlur('returnDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) => onChange({returnDate: value?.toDate()})}
                            />

                            <FormControl>
                                <InputLabel id="carrier" shrink>
                                    Carrier
                                </InputLabel>
                                <Select
                                    label="Carrier"
                                    labelId="carrier"
                                    displayEmpty
                                    name="returnShippingCarrier"
                                    value={formState.returnShippingCarrier?.value || ''}
                                    onChange={e => onChange({returnShippingCarrier: e.target.value})}
                                    onBlur={() => onBlur('returnShippingCarrier')}
                                    error={
                                        formState.returnShippingCarrier?.touched &&
                                        !!formState.returnShippingCarrier?.error
                                    }
                                    renderValue={(selected: string) => {
                                        if (!selected || selected.length === 0) {
                                            return <Typography>Select shipping carrier</Typography>
                                        }
                                        return selected
                                    }}
                                >
                                    <MenuItem value="USPS">USPS</MenuItem>
                                    <MenuItem value="UPS">UPS</MenuItem>
                                    <MenuItem value="FedEx">FedEx</MenuItem>
                                    <MenuItem value="DHL">DHL</MenuItem>
                                    <MenuItem value="Amazon Logistics">Amazon Logistics</MenuItem>
                                    <MenuItem value="OnTrac">OnTrac</MenuItem>
                                    <MenuItem value="Other">Other</MenuItem>
                                </Select>
                            </FormControl>

                            <TextField
                                size="medium"
                                label="Tracking Number"
                                placeholder="Provide your tracking number if you have one"
                                name="returnShippingTracking"
                                value={formState.returnShippingTracking?.value || ''}
                                onChange={e => onChange({returnShippingTracking: e.target.value})}
                                onBlur={() => onBlur('returnShippingTracking')}
                                error={
                                    formState.returnShippingTracking?.touched &&
                                    !!formState.returnShippingTracking?.error
                                }
                                helperText={
                                    formState.returnShippingTracking?.touched
                                        ? formState.returnShippingTracking?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <FormControl>
                                <FormLabel>Did you receive confirmation that the shipment arrived?</FormLabel>
                                <RadioGroup
                                    row
                                    name="returnedSuccess"
                                    value={
                                        formState.returnedSuccess
                                            ? formState.returnedSuccess?.value
                                                ? 'yes'
                                                : 'no'
                                            : null
                                    }
                                    onChange={e => onChange({returnedSuccess: e.target.value === 'yes'})}
                                    onBlur={() => onBlur('returnedSuccess')}
                                >
                                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                                    <FormControlLabel value="no" control={<Radio />} label="No" />
                                </RadioGroup>
                            </FormControl>
                            <TextField
                                size="medium"
                                label="Reason for Return"
                                placeholder="Please provide a brief description of the reason for your return"
                                name="returnDescription"
                                value={formState.returnDescription?.value || ''}
                                onChange={e => onChange({returnDescription: e.target.value})}
                                onBlur={() => onBlur('returnDescription')}
                                error={formState.returnDescription?.touched && !!formState.returnDescription?.error}
                                helperText={
                                    formState.returnDescription?.touched
                                        ? formState.returnDescription?.error
                                        : undefined
                                }
                                rows={3}
                                multiline
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={productType === DisputeProductType.Digital}
                        sx={{display: productType === DisputeProductType.Digital ? 'initial' : 'none'}}
                        data-testid="digital-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <TextField
                                size="medium"
                                label="Reason for Return"
                                placeholder="Please provide a brief description of your purchase and the reason that you requested a refund"
                                name="returnDescription"
                                value={formState.returnDescription?.value || ''}
                                onChange={e => onChange({returnDescription: e.target.value})}
                                onBlur={() => onBlur('returnDescription')}
                                error={formState.returnDescription?.touched && !!formState.returnDescription?.error}
                                helperText={
                                    formState.returnDescription?.touched
                                        ? formState.returnDescription?.error
                                        : undefined
                                }
                                rows={3}
                                multiline
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={productType === DisputeProductType.Service}
                        sx={{display: productType === DisputeProductType.Service ? 'initial' : 'none'}}
                        data-testid="service-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <DatePicker
                                label="Cancellation Date?"
                                name="cancellationDate"
                                value={
                                    formState.cancellationDate?.value ? dayjs(formState.cancellationDate.value) : null
                                }
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error:
                                            formState.cancellationDate?.touched && !!formState.cancellationDate?.error,
                                        helperText: formState.cancellationDate?.touched
                                            ? formState.cancellationDate?.error
                                            : undefined,
                                        onBlur: () => onBlur('cancellationDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) => onChange({cancellationDate: value?.toDate()})}
                            />
                            <TextField
                                size="medium"
                                label="Cancellation Number"
                                placeholder="Provide the cancellation number if you have one"
                                name="cancellationId"
                                value={formState.cancellationId?.value || ''}
                                onChange={e => onChange({cancellationId: e.target.value})}
                                onBlur={() => onBlur('cancellationId')}
                                error={formState.cancellationId?.touched && !!formState.cancellationId?.error}
                                helperText={
                                    formState.cancellationId?.touched ? formState.cancellationId?.error : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <TextField
                                size="medium"
                                label="Reason for Cancellation"
                                placeholder="Please describe the reason that you cancelled the service"
                                name="cancellationDescription"
                                value={formState.cancellationDescription?.value || ''}
                                onChange={e => onChange({cancellationDescription: e.target.value})}
                                onBlur={() => onBlur('cancellationDescription')}
                                error={
                                    formState.cancellationDescription?.touched &&
                                    !!formState.cancellationDescription?.error
                                }
                                helperText={
                                    formState.cancellationDescription?.touched
                                        ? formState.cancellationDescription?.error
                                        : undefined
                                }
                                rows={3}
                                multiline
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                        </Grid>
                    </Collapse>
                </FormGroup>

                <Grid container flexDirection="column" gap={2}>
                    <SubmitButton />
                    <Button
                        fullWidth
                        variant="text"
                        startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                        onClick={onPrevious}
                    >
                        Previous
                    </Button>
                </Grid>
            </Grid>
        </LocalizationProvider>
    )
}

function SubmitButton({disabled = false}: {disabled?: boolean}) {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending} disabled={disabled}>
            Submit
        </Button>
    )
}
