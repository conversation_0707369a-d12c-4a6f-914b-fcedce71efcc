'use client'

import {useFormStatus} from 'react-dom'
import {Button, Grid, Typography, TextField, ListItemButton, Paper, Icon} from '@mui/material'
import type {DisputeFormState} from '../DisputeTypes'
import type {CreateDisputeRequest} from '@/services/controllers/disputes'
import {useState} from 'react'
interface DuplicateChargeFormProps {
    formState: DisputeFormState['duplicateCharge']
    onChange: (fields: Partial<Extract<CreateDisputeRequest, {reason: 'duplicateCharge'}>>) => void
    onPrevious: () => void
    onBlur: (fieldName: string) => void
}

export default function DuplicateChargeForm({formState, onChange, onPrevious, onBlur}: DuplicateChargeFormProps) {
    const [selectedTransaction, setSelectedTransaction] = useState<string | null>(null)

    return (
        <Grid container flexDirection="column" gap={2}>
            <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                &quot;I was charged twice for the same purchase&quot;
            </Typography>
            <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                Tell us more...
            </Typography>
            <Typography fontSize={16} color="textSecondary">
                Providing more details will help us to process your report but is not required.
            </Typography>
            <Grid container flexDirection="column" gap={3}>
                <Typography>Select the charge that this is a duplicate of</Typography>
                <Grid container flexDirection="column" gap={3} data-testid="transaction-list">
                    <TransactionCard
                        transactionId={'b7916e1a-b846-53db-8936-d7cbb3dbf6af'}
                        selected={formState.priorTransactionId?.value === 'b7916e1a-b846-53db-8936-d7cbb3dbf6af'}
                        onSelect={transactionId => onChange({priorTransactionId: transactionId})}
                    />
                    <TransactionCard
                        transactionId={'2'}
                        selected={formState.priorTransactionId?.value === '2'}
                        onSelect={transactionId => onChange({priorTransactionId: transactionId})}
                    />
                    <TransactionCard
                        transactionId={'3'}
                        selected={formState.priorTransactionId?.value === '3'}
                        onSelect={transactionId => onChange({priorTransactionId: transactionId})}
                    />
                    <NoTransactionCard
                        transactionId={'NOT_FOUND'}
                        selected={formState.priorTransactionId?.value === 'NOT_FOUND'}
                        onSelect={transactionId => onChange({priorTransactionId: transactionId})}
                    />
                </Grid>
            </Grid>
            <TextField
                size="medium"
                label="Additional Information"
                placeholder="Please provide any details that might be helpful in helping us to understand what went wrong."
                name="explanation"
                value={formState.explanation?.value || ''}
                onChange={e => onChange({explanation: e.target.value})}
                onBlur={() => onBlur('explanation')}
                rows={3}
                multiline
                error={formState.explanation?.touched && !!formState.explanation?.error}
                helperText={formState.explanation?.touched ? formState.explanation?.error : undefined}
                slotProps={{
                    inputLabel: {
                        shrink: true,
                    },
                }}
            />

            <Grid container flexDirection="column" gap={2}>
                <SubmitButton />
                <Button
                    fullWidth
                    variant="text"
                    startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                    onClick={onPrevious}
                >
                    Previous
                </Button>
            </Grid>
        </Grid>
    )
}

function SubmitButton({disabled = false}: {disabled?: boolean}) {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending} disabled={disabled}>
            Submit
        </Button>
    )
}

function TransactionCard({
    transactionId,
    selected,
    onSelect,
}: {
    transactionId: string
    selected: boolean
    onSelect: (transaction: string) => void
}) {
    return (
        <Paper elevation={2} data-testid={`transaction-card-${transactionId}`}>
            <ListItemButton
                selected={selected}
                onClick={() => onSelect(transactionId)}
                sx={{
                    '&.Mui-selected': {
                        backgroundColor: selected ? 'var(--mui-palette-primary-light)' : 'inherit',
                    },
                }}
            >
                <Grid
                    container
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{width: '100%'}}
                >
                    <Typography fontWeight={600}>Transaction</Typography>
                    <Typography fontWeight={400} color="textSecondary">
                        Transaction
                    </Typography>
                    <Typography fontWeight={600} color="textSecondary">
                        Transaction
                    </Typography>
                </Grid>
            </ListItemButton>
        </Paper>
    )
}

function NoTransactionCard({
    transactionId,
    selected,
    onSelect,
}: {
    transactionId: string
    selected: boolean
    onSelect: (transaction: string) => void
}) {
    return (
        <Paper elevation={2} data-testid="no-transaction-card">
            <ListItemButton
                selected={selected}
                onClick={() => onSelect(transactionId)}
                sx={{
                    '&.Mui-selected': {
                        backgroundColor: selected ? 'var(--mui-palette-primary-light)' : 'inherit',
                    },
                }}
            >
                <Grid container flexDirection="row" justifyContent="center" alignItems="center" sx={{width: '100%'}}>
                    <Typography fontWeight={600}>I don&apos;t see it here</Typography>
                </Grid>
            </ListItemButton>
        </Paper>
    )
}
