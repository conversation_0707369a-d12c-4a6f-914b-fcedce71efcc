'use client'

import {useFormStatus} from 'react-dom'
import {Button, FormGroup, Grid, Typography, TextField, Icon} from '@mui/material'
import {DatePicker} from '@mui/x-date-pickers/DatePicker'
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'
import dayjs from 'dayjs'
import type {DisputeFormState} from '../DisputeTypes'
import {type CreateDisputeRequest} from '@/services/controllers/disputes'

interface SubscriptionCanceledFormProps {
    formState: DisputeFormState['subscriptionCanceled']
    onChange: (fields: Partial<Extract<CreateDisputeRequest, {reason: 'subscriptionCanceled'}>>) => void
    onPrevious: () => void
    fieldErrors?: Record<string, string>
    onBlur: (fieldName: string) => void
}

export default function SubscriptionCanceledForm({
    formState,
    onChange,
    onPrevious,
    fieldErrors,
    onBlur,
}: SubscriptionCanceledFormProps) {
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Grid container flexDirection="column" gap={2}>
                <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                    &quot;I cancelled this subscription but am still being charged&quot;
                </Typography>
                <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                    Tell us more...
                </Typography>
                <Typography fontSize={16} color="textSecondary">
                    Providing more details will help us to process your report but is not required.
                </Typography>
                <FormGroup sx={{gap: 3}}>
                    <DatePicker
                        label="When did you cancel?"
                        name="cancellationDate"
                        value={formState.cancellationDate?.value ? dayjs(formState.cancellationDate.value) : null}
                        slotProps={{
                            textField: {
                                error: !!formState.cancellationDate?.error,
                                helperText: formState.cancellationDate?.error,
                                InputLabelProps: {
                                    shrink: true,
                                },
                                onBlur: () => onBlur('cancellationDate'),
                            },
                        }}
                        onChange={(value: dayjs.Dayjs | null) => onChange({cancellationDate: value?.toDate()})}
                    />
                    <TextField
                        size="medium"
                        label="Cancellation Reference Number"
                        placeholder="Provide the cancellation reference number if you have one"
                        name="cancellationId"
                        value={formState.cancellationId?.value || ''}
                        onChange={e => onChange({cancellationId: e.target.value})}
                        onBlur={() => onBlur('cancellationId')}
                        error={!!formState.cancellationId?.error}
                        helperText={formState.cancellationId?.error}
                        slotProps={{
                            inputLabel: {
                                shrink: true,
                            },
                        }}
                    />
                </FormGroup>

                <Grid container flexDirection="column" gap={2}>
                    <SubmitButton />
                    <Button
                        fullWidth
                        variant="text"
                        startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                        onClick={onPrevious}
                    >
                        Previous
                    </Button>
                </Grid>
            </Grid>
        </LocalizationProvider>
    )
}

function SubmitButton() {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending}>
            Submit
        </Button>
    )
}
