'use client'

import {useFormStatus} from 'react-dom'
import {Button, Checkbox, FormGroup, FormControlLabel, Grid, Typography, TextField, Icon} from '@mui/material'
import type {DisputeFormState} from '../DisputeTypes'
import type {CreateDisputeRequest} from '@/services/controllers/disputes'

interface InvalidAuthorizationFormProps {
    formState: DisputeFormState['invalidAuthorization'] | DisputeFormState['fraud']
    onChange: (
        fields: Partial<Extract<CreateDisputeRequest, {reason: 'invalidAuthorization'} | {reason: 'fraud'}>>,
    ) => void
    onPrevious: () => void
    onBlur: (fieldName: string) => void
}

export default function InvalidAuthorizationForm({
    formState,
    onChange,
    onPrevious,
    onBlur,
}: InvalidAuthorizationFormProps) {
    return (
        <Grid container flexDirection="column" gap={2}>
            <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                &quot;I didn&apos;t make this purchase and/or this purchase was fraudulent&quot;
            </Typography>
            <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                Tell us more...
            </Typography>
            <Typography fontSize={16} color="textSecondary">
                Providing more details will help us to process your report but is not required.
            </Typography>
            <FormGroup sx={{gap: 3}}>
                <FormControlLabel
                    control={
                        <Checkbox
                            name="reason"
                            checked={formState.reason?.value === 'fraud'}
                            onChange={e => {
                                onChange({reason: e.target.checked ? 'fraud' : 'invalidAuthorization'})
                                onBlur('reason')
                            }}
                        />
                    }
                    label="This purchase was fraudulent"
                />
                {formState.reason?.touched && formState.reason?.error && (
                    <Typography color="error" variant="caption">
                        {formState.reason.error}
                    </Typography>
                )}
                <TextField
                    size="medium"
                    label="Additional Details"
                    placeholder="Is there anything else you'd like to share?"
                    name="explanation"
                    value={formState.explanation?.value || ''}
                    onChange={e => onChange({explanation: e.target.value})}
                    onBlur={() => onBlur('explanation')}
                    rows={3}
                    multiline
                    error={formState.explanation?.touched && !!formState.explanation?.error}
                    helperText={formState.explanation?.touched ? formState.explanation?.error : undefined}
                    slotProps={{
                        inputLabel: {
                            shrink: true,
                        },
                    }}
                />
            </FormGroup>

            <Grid container flexDirection="column" gap={2}>
                <SubmitButton />
                <Button
                    fullWidth
                    variant="text"
                    startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                    onClick={onPrevious}
                >
                    Previous
                </Button>
            </Grid>
        </Grid>
    )
}

function SubmitButton({disabled = false}: {disabled?: boolean}) {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending} disabled={disabled}>
            Submit
        </Button>
    )
}
