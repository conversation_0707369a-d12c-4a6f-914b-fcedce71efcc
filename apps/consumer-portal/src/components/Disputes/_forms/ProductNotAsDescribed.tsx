'use client'

import {useFormStatus} from 'react-dom'
import {
    Button,
    FormGroup,
    FormControlLabel,
    Grid,
    Typography,
    TextField,
    FormControl,
    FormLabel,
    Radio,
    RadioGroup,
    Collapse,
    InputLabel,
    MenuItem,
    Select,
    Icon,
} from '@mui/material'
import type {DisputeFormState} from '../DisputeTypes'
import {DisputeProductType, type CreateDisputeRequest} from '@/services/controllers/disputes'
import {useState} from 'react'
import {DatePicker} from '@mui/x-date-pickers/DatePicker'
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'
import dayjs from 'dayjs'

interface ProductNotAsDescribedFormProps {
    formState: DisputeFormState['productNotAsDescribed']
    onChange: (fields: Partial<Extract<CreateDisputeRequest, {reason: 'productNotAsDescribed'}>>) => void
    onPrevious: () => void
    onBlur: (fieldName: string) => void
}

export default function ProductNotAsDescribedForm({
    formState,
    onChange,
    onPrevious,
    onBlur,
}: ProductNotAsDescribedFormProps) {
    const [productReturned, setProductReturned] = useState<'yes' | 'no' | null>(null)

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Grid container flexDirection="column" gap={2}>
                <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                    &quot;The product I purchased was not as described&quot;
                </Typography>
                <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                    Tell us more...
                </Typography>
                <FormGroup sx={{gap: 3}}>
                    <FormControl>
                        <FormLabel htmlFor="contactedSuccess">
                            Have you already tried to contact the merchant about this problem?
                        </FormLabel>
                        <RadioGroup
                            row
                            name="contactedSuccess"
                            aria-label="contactedSuccess"
                            value={
                                formState.contactedSuccess ? (formState.contactedSuccess?.value ? 'yes' : 'no') : null
                            }
                            onChange={e => {
                                onChange({contactedSuccess: e.target.value === 'yes'})
                                onBlur('contactedSuccess')
                            }}
                        >
                            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                            <FormControlLabel value="no" control={<Radio />} label="No" />
                        </RadioGroup>
                    </FormControl>

                    <Collapse
                        in={formState.contactedSuccess?.value === false}
                        sx={{
                            display: formState.contactedSuccess?.value === false ? 'initial' : 'none',
                        }}
                        data-testid="contact-error-section"
                    >
                        <Typography color="error">
                            Please try contacting the merchant before filing a dispute.
                        </Typography>
                    </Collapse>

                    <Collapse
                        in={formState.contactedSuccess?.value === true}
                        sx={{
                            display: formState.contactedSuccess?.value === true ? 'initial' : 'none',
                        }}
                        data-testid="contact-details-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <DatePicker
                                label="When did you contact them?"
                                name="contactDate"
                                value={formState.contactDate?.value ? dayjs(formState.contactDate.value) : null}
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error: !!formState.contactDate?.error,
                                        helperText: formState.contactDate?.error || '',
                                        onBlur: () => onBlur('contactDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) => {
                                    onChange({contactDate: value?.toDate()})
                                }}
                            />

                            <TextField
                                size="medium"
                                label="Contact Description"
                                placeholder="Please describe your interaction with the merchant (optional)"
                                name="contactDescription"
                                value={formState.contactDescription?.value || ''}
                                onChange={e => onChange({contactDescription: e.target.value})}
                                onBlur={() => onBlur('contactDescription')}
                                rows={3}
                                multiline
                                error={formState.contactDescription?.touched && !!formState.contactDescription?.error}
                                helperText={
                                    formState.contactDescription?.touched
                                        ? formState.contactDescription?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={(formState.contactedSuccess?.value && formState.contactDate?.value) !== undefined}
                        sx={{
                            display:
                                (formState.contactedSuccess?.value && formState.contactDate?.value) !== undefined
                                    ? 'initial'
                                    : 'none',
                        }}
                        data-testid="product-type-section"
                    >
                        <Grid container flexDirection="column">
                            <FormControl>
                                <InputLabel id="purchaseType" shrink>
                                    Purchase Type
                                </InputLabel>
                                <Select
                                    label="Purchase Type"
                                    labelId="purchaseType"
                                    displayEmpty
                                    name="productType"
                                    value={formState.productType?.value || ''}
                                    onChange={e => onChange({productType: e.target.value as DisputeProductType})}
                                    onBlur={() => onBlur('productType')}
                                    error={formState.productType?.touched && !!formState.productType?.error}
                                    renderValue={(selected: string) => {
                                        if (!selected || selected.length === 0) {
                                            return <Typography>What kind of purchase was it</Typography>
                                        }
                                        const key = Object.keys(DisputeProductType).find(
                                            (key): key is keyof typeof DisputeProductType =>
                                                DisputeProductType[key as keyof typeof DisputeProductType] === selected,
                                        )
                                        return key || 'Unknown Product Type'
                                    }}
                                >
                                    {(Object.keys(DisputeProductType) as Array<keyof typeof DisputeProductType>).map(
                                        productType => (
                                            <MenuItem key={productType} value={DisputeProductType[productType]}>
                                                {productType}
                                            </MenuItem>
                                        ),
                                    )}
                                </Select>
                                {formState.productType?.touched && formState.productType?.error && (
                                    <Typography color="error" variant="caption">
                                        {formState.productType.error}
                                    </Typography>
                                )}
                            </FormControl>
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={
                            formState.contactedSuccess?.value === true &&
                            (formState.productType?.value === DisputeProductType.Merchandise ||
                                formState.productType?.value === DisputeProductType.Digital)
                        }
                        sx={{
                            display:
                                formState.contactedSuccess?.value === true &&
                                (formState.productType?.value === DisputeProductType.Merchandise ||
                                    formState.productType?.value === DisputeProductType.Digital)
                                    ? 'initial'
                                    : 'none',
                        }}
                        data-testid="product-details-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <TextField
                                size="medium"
                                label="Order ID"
                                placeholder="Provide your Order ID if you have one"
                                name="orderId"
                                value={formState.orderId?.value || ''}
                                onChange={e => onChange({orderId: e.target.value})}
                                onBlur={() => onBlur('orderId')}
                                error={formState.orderId?.touched && !!formState.orderId?.error}
                                helperText={formState.orderId?.touched ? formState.orderId?.error : undefined}
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <DatePicker
                                label="When did your purchase arrive?"
                                name="productDeliveryDate"
                                value={
                                    formState.productDeliveryDate?.value
                                        ? dayjs(formState.productDeliveryDate.value)
                                        : null
                                }
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error:
                                            formState.productDeliveryDate?.touched &&
                                            !!formState.productDeliveryDate?.error,
                                        helperText: formState.productDeliveryDate?.touched
                                            ? formState.productDeliveryDate?.error
                                            : undefined,
                                        onBlur: () => onBlur('productDeliveryDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) => {
                                    onChange({productDeliveryDate: value?.toDate()})
                                    onBlur('productDeliveryDate')
                                }}
                            />
                            <TextField
                                size="medium"
                                label="Purchase Description"
                                placeholder="Please provide a brief description of your purchase and the condition in which it arrived. Be sure to include any problem areas."
                                name="productDescription"
                                value={formState.productDescription?.value || ''}
                                onChange={e => onChange({productDescription: e.target.value})}
                                onBlur={() => onBlur('productDescription')}
                                rows={4}
                                multiline
                                error={formState.productDescription?.touched && !!formState.productDescription?.error}
                                helperText={
                                    formState.productDescription?.touched
                                        ? formState.productDescription?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <FormControl>
                                <FormLabel htmlFor="productReturned">Did you return your purchase?</FormLabel>
                                <RadioGroup
                                    row
                                    name="productReturned"
                                    aria-label="productReturned"
                                    value={productReturned}
                                    onChange={e => setProductReturned(e.target.value as 'yes' | 'no')}
                                >
                                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                                    <FormControlLabel value="no" control={<Radio />} label="No" />
                                </RadioGroup>
                            </FormControl>
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={
                            productReturned === 'yes' && formState.productType?.value === DisputeProductType.Merchandise
                        }
                        sx={{
                            display:
                                productReturned === 'yes' &&
                                formState.productType?.value === DisputeProductType.Merchandise
                                    ? 'initial'
                                    : 'none',
                        }}
                        data-testid="merchandise-return-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <DatePicker
                                label="When did you ship it?"
                                name="returnDate"
                                value={formState.returnDate?.value ? dayjs(formState.returnDate.value) : null}
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error: formState.returnDate?.touched && !!formState.returnDate?.error,
                                        helperText: formState.returnDate?.touched
                                            ? formState.returnDate?.error
                                            : undefined,
                                        onBlur: () => onBlur('returnDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) => onChange({returnDate: value?.toDate()})}
                            />

                            <FormControl>
                                <InputLabel id="carrier" shrink>
                                    Carrier
                                </InputLabel>
                                <Select
                                    label="Carrier"
                                    labelId="carrier"
                                    displayEmpty
                                    name="returnShippingCarrier"
                                    value={formState.returnShippingCarrier?.value || ''}
                                    onChange={e => onChange({returnShippingCarrier: e.target.value})}
                                    onBlur={() => onBlur('returnShippingCarrier')}
                                    renderValue={(selected: string) => {
                                        if (!selected || selected.length === 0) {
                                            return <Typography>Select shipping carrier</Typography>
                                        }
                                        return selected
                                    }}
                                >
                                    <MenuItem value="USPS">USPS</MenuItem>
                                    <MenuItem value="UPS">UPS</MenuItem>
                                    <MenuItem value="FedEx">FedEx</MenuItem>
                                    <MenuItem value="DHL">DHL</MenuItem>
                                    <MenuItem value="Amazon Logistics">Amazon Logistics</MenuItem>
                                    <MenuItem value="OnTrac">OnTrac</MenuItem>
                                    <MenuItem value="Other">Other</MenuItem>
                                </Select>
                            </FormControl>

                            <TextField
                                size="medium"
                                label="Tracking Number"
                                placeholder="Provide your tracking number if you have one"
                                name="returnShippingTracking"
                                value={formState.returnShippingTracking?.value || ''}
                                onChange={e => onChange({returnShippingTracking: e.target.value})}
                                onBlur={() => onBlur('returnShippingTracking')}
                                error={
                                    formState.returnShippingTracking?.touched &&
                                    !!formState.returnShippingTracking?.error
                                }
                                helperText={
                                    formState.returnShippingTracking?.touched
                                        ? formState.returnShippingTracking?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />

                            <FormControl>
                                <FormLabel>Did you receive confirmation that the shipment arrived?</FormLabel>
                                <RadioGroup
                                    row
                                    name="returnedSuccess"
                                    aria-label="returnedSuccess"
                                    value={
                                        formState.returnedSuccess
                                            ? formState.returnedSuccess?.value
                                                ? 'yes'
                                                : 'no'
                                            : null
                                    }
                                    onChange={e => onChange({returnedSuccess: e.target.value === 'yes'})}
                                    onBlur={() => onBlur('returnedSuccess')}
                                >
                                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                                    <FormControlLabel value="no" control={<Radio />} label="No" />
                                </RadioGroup>
                            </FormControl>
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={productReturned === 'yes' && formState.productType?.value === DisputeProductType.Digital}
                        sx={{
                            display:
                                productReturned === 'yes' && formState.productType?.value === DisputeProductType.Digital
                                    ? 'initial'
                                    : 'none',
                        }}
                        data-testid="digital-return-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <DatePicker
                                label="When did you return it?"
                                name="returnDate"
                                value={formState.returnDate?.value ? dayjs(formState.returnDate.value) : null}
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error: formState.returnDate?.touched && !!formState.returnDate?.error,
                                        helperText: formState.returnDate?.touched
                                            ? formState.returnDate?.error
                                            : undefined,
                                        onBlur: () => onBlur('returnDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) => {
                                    onChange({returnDate: value?.toDate()})
                                    onBlur('returnDate')
                                }}
                            />
                            <FormControl>
                                <FormLabel>Did you receive confirmation that the shipment arrived?</FormLabel>
                                <RadioGroup
                                    row
                                    name="returnedSuccess"
                                    aria-label="returnedSuccess"
                                    value={
                                        formState.returnedSuccess
                                            ? formState.returnedSuccess?.value
                                                ? 'yes'
                                                : 'no'
                                            : null
                                    }
                                    onChange={e => onChange({returnedSuccess: e.target.value === 'yes'})}
                                    onBlur={() => onBlur('returnedSuccess')}
                                >
                                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                                    <FormControlLabel value="no" control={<Radio />} label="No" />
                                </RadioGroup>
                            </FormControl>
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={formState.productType?.value === DisputeProductType.Service}
                        sx={{
                            display: formState.productType?.value === DisputeProductType.Service ? 'initial' : 'none',
                        }}
                        data-testid="service-details-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <TextField
                                size="medium"
                                label="Purchase Description"
                                placeholder="Please provide a brief description of your purchase. Be sure to include any problem areas."
                                name="productDescription"
                                value={formState.productDescription?.value || ''}
                                onChange={e => onChange({productDescription: e.target.value})}
                                onBlur={() => onBlur('productDescription')}
                                rows={4}
                                multiline
                                error={formState.productDescription?.touched && !!formState.productDescription?.error}
                                helperText={
                                    formState.productDescription?.touched
                                        ? formState.productDescription?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <TextField
                                size="medium"
                                label="Order ID"
                                placeholder="Provide your Order ID if you have one"
                                name="orderId"
                                value={formState.orderId?.value || ''}
                                onChange={e => onChange({orderId: e.target.value})}
                                onBlur={() => onBlur('orderId')}
                                error={formState.orderId?.touched && !!formState.orderId?.error}
                                helperText={formState.orderId?.touched ? formState.orderId?.error : undefined}
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                        </Grid>
                    </Collapse>
                </FormGroup>

                <Grid container flexDirection="column" gap={2}>
                    <SubmitButton disabled={!formState.contactedSuccess?.value || !formState.contactDate?.value} />
                    <Button
                        fullWidth
                        variant="text"
                        startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                        onClick={onPrevious}
                    >
                        Previous
                    </Button>
                </Grid>
            </Grid>
        </LocalizationProvider>
    )
}

function SubmitButton({disabled = false}: {disabled?: boolean}) {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending} disabled={disabled}>
            Submit
        </Button>
    )
}
