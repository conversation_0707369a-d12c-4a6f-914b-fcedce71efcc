import {render, screen} from '@testing-library/react'
import {describe, it, beforeEach, expect, vi} from 'vitest'
import userEvent from '@testing-library/user-event'
import SubscriptionCanceledForm from '../SubscriptionCanceled'
import type {FormField, SubscriptionCanceledFields} from '../../DisputeTypes'
import {LocalizationProvider} from '@mui/x-date-pickers'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'

// Mock the SubmitButton component
vi.mock('../SubscriptionCanceled', async () => {
    const actual = await vi.importActual('../SubscriptionCanceled')
    const SubmitButton = ({disabled}: {disabled?: boolean}) => (
        <button type="submit" disabled={disabled}>
            Submit
        </button>
    )
    return {
        ...actual,
        default: actual.default,
        SubmitButton,
    }
})

vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

const mockOnChange = vi.fn()
const mockOnPrevious = vi.fn()
const mockOnBlur = vi.fn()

const defaultProps = {
    formState: {
        cancellationDate: {value: null, touched: false, error: undefined} as unknown as FormField<Date>,
        cancellationId: {value: '', touched: false, error: undefined} as FormField<string>,
    } satisfies SubscriptionCanceledFields,
    onChange: mockOnChange,
    onPrevious: mockOnPrevious,
    onBlur: mockOnBlur,
}

const WithProviders = ({children}: {children: React.ReactNode}) => {
    return <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
}

describe('SubscriptionCanceledForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders initial form state correctly', () => {
        render(<SubscriptionCanceledForm {...defaultProps} />, {wrapper: WithProviders})

        // Check initial heading
        expect(screen.getByText(/I cancelled this subscription but am still being charged/i)).toBeInTheDocument()

        // Check form fields are present
        expect(screen.getByLabelText(/When did you cancel?/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/Cancellation Reference Number/i)).toBeInTheDocument()

        // Submit button should be enabled as there are no required fields
        expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
    })

    describe('Form Field Interactions', () => {
        it('handles cancellation ID input', async () => {
            render(<SubscriptionCanceledForm {...defaultProps} />, {wrapper: WithProviders})

            const cancellationIdField = screen.getByLabelText(/Cancellation Reference Number/i)
            await userEvent.type(cancellationIdField, 'a')

            expect(mockOnChange).toHaveBeenCalledWith({
                cancellationId: 'a',
            })
            await userEvent.tab()
            
            expect(mockOnBlur).toHaveBeenCalledWith('cancellationId')
        })

        it('shows error message when cancellation ID has error', () => {
            render(
                <SubscriptionCanceledForm
                    {...{
                        ...defaultProps,
                        formState: {
                            ...defaultProps.formState,
                            cancellationId: {
                                value: '',
                                touched: true,
                                error: 'Test error message',
                            } as FormField<string>,
                        },
                    }}
                />,
                {wrapper: WithProviders},
            )

            expect(screen.getByText('Test error message')).toBeInTheDocument()
        })

        it('triggers onBlur when cancellation ID field loses focus', async () => {
            render(<SubscriptionCanceledForm {...defaultProps} />, {wrapper: WithProviders})

            const cancellationIdField = screen.getByLabelText(/Cancellation Reference Number/i)
            await userEvent.click(cancellationIdField)
            await userEvent.tab()

            expect(mockOnBlur).toHaveBeenCalledWith('cancellationId')
        })
    })

    it('calls onPrevious when previous button is clicked', async () => {
        render(<SubscriptionCanceledForm {...defaultProps} />, {wrapper: WithProviders})

        const previousButton = screen.getByRole('button', {name: /previous/i})
        await userEvent.click(previousButton)

        expect(mockOnPrevious).toHaveBeenCalled()
    })
})
