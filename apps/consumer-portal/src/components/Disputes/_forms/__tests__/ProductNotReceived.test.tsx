import {render, screen, within} from '@testing-library/react'
import {describe, it, beforeEach, expect, vi} from 'vitest'
import userEvent from '@testing-library/user-event'
import ProductNotReceivedForm from '../ProductNotReceived'
import {DisputeProductType} from '@/services/controllers/disputes'
import type {FormField, ProductNotReceivedFields} from '../../DisputeTypes'
import {LocalizationProvider} from '@mui/x-date-pickers'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'

// Mock the SubmitButton component
vi.mock('../ProductNotReceived', async () => {
    const actual = await vi.importActual('../ProductNotReceived')
    const SubmitButton = ({disabled}: {disabled?: boolean}) => (
        <button type="submit" disabled={disabled}>
            Submit
        </button>
    )
    return {
        ...actual,
        default: actual.default,
        SubmitButton,
    }
})

vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

const mockOnChange = vi.fn()
const mockOnPrevious = vi.fn()
const mockOnBlur = vi.fn()

const defaultProps = {
    formState: {
        productType: {value: null, touched: false, error: undefined} as unknown as FormField<DisputeProductType>,
        productDescription: {value: '', touched: false, error: undefined} as FormField<string>,
        orderId: {value: '', touched: false, error: undefined} as FormField<string>,
        expectedDeliveryDate: {value: null, touched: false, error: undefined} as unknown as FormField<Date>,
        serviceEndDate: {value: null, touched: false, error: undefined} as unknown as FormField<Date>,
    } satisfies ProductNotReceivedFields,
    onChange: mockOnChange,
    onPrevious: mockOnPrevious,
    onBlur: mockOnBlur,
}

const WithProviders = ({children}: {children: React.ReactNode}) => {
    return <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
}

describe('ProductNotReceivedForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders initial form state correctly', () => {
        render(<ProductNotReceivedForm {...defaultProps} />, {wrapper: WithProviders})

        // Check initial heading
        expect(screen.getByText(/I didn't receive this purchase/i)).toBeInTheDocument()

        // Check initial question about purchase type
        expect(screen.getByLabelText(/Purchase Type/i)).toBeInTheDocument()

        // Submit button should be enabled as there are no required fields
        expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
    })

    describe('Product Type Selection Flow', () => {
        beforeEach(async () => {
            render(<ProductNotReceivedForm {...defaultProps} />, {wrapper: WithProviders})
        })

        it('shows merchandise specific fields when merchandise is selected', async () => {
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const merchandiseOption = screen.getByRole('option', {name: /Merchandise/i})
            await userEvent.click(merchandiseOption)

            const productSection = screen.getByTestId('product-details-section')
            expect(within(productSection).getByLabelText(/Order ID/i)).toBeInTheDocument()
            expect(within(productSection).getByLabelText(/Expected Delivery Date/i)).toBeInTheDocument()
            expect(within(productSection).getByLabelText(/Purchase Description/i)).toBeInTheDocument()
        })

        it('shows digital specific fields when digital is selected', async () => {
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const digitalOption = screen.getByRole('option', {name: /Digital/i})
            await userEvent.click(digitalOption)

            const productSection = screen.getByTestId('product-details-section')
            expect(within(productSection).getByLabelText(/Order ID/i)).toBeInTheDocument()
            expect(within(productSection).getByLabelText(/Expected Delivery Date/i)).toBeInTheDocument()
            expect(within(productSection).getByLabelText(/Purchase Description/i)).toBeInTheDocument()
        })

        it('shows service specific fields when service is selected', async () => {
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const serviceOption = screen.getByRole('option', {name: /Service/i})
            await userEvent.click(serviceOption)

            const serviceSection = screen.getByTestId('service-details-section')
            expect(within(serviceSection).getByLabelText(/Order ID/i)).toBeInTheDocument()
            expect(
                within(serviceSection).getByLabelText(/When did you realize the service was disrupted?/i),
            ).toBeInTheDocument()
            expect(within(serviceSection).getByLabelText(/Purchase Description/i)).toBeInTheDocument()
        })
    })

    describe('Form Field Interactions', () => {
        beforeEach(async () => {
            render(<ProductNotReceivedForm {...defaultProps} />, {wrapper: WithProviders})

            // Select merchandise type to show all fields
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const merchandiseOption = screen.getByRole('option', {name: /Merchandise/i})
            await userEvent.click(merchandiseOption)

            await screen.findByTestId('product-details-section')
        })

        it('handles product description input', async () => {
            const productSection = screen.getByTestId('product-details-section')
            const descriptionField = within(productSection).getByLabelText(/Purchase Description/i)
            await userEvent.type(descriptionField, 'a')

            expect(mockOnChange).toHaveBeenCalledWith({
                productDescription: 'a',
            })
        })

        it('handles order ID input', async () => {
            const productSection = screen.getByTestId('product-details-section')
            const orderIdField = within(productSection).getByLabelText(/Order ID/i)
            await userEvent.type(orderIdField, 'a')

            expect(mockOnChange).toHaveBeenCalledWith({
                orderId: 'a',
            })
        })
    })

    it('calls onPrevious when previous button is clicked', async () => {
        render(<ProductNotReceivedForm {...defaultProps} />, {wrapper: WithProviders})

        const previousButton = screen.getByRole('button', {name: /previous/i})
        await userEvent.click(previousButton)

        expect(mockOnPrevious).toHaveBeenCalled()
    })
})
