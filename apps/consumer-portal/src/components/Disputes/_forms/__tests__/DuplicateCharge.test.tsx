import {render, screen, within} from '@testing-library/react'
import {describe, it, beforeEach, expect, vi} from 'vitest'
import userEvent from '@testing-library/user-event'
import DuplicateChargeForm from '../DuplicateCharge'
import type {FormField, DuplicateChargeFields} from '../../DisputeTypes'

// Mock the SubmitButton component
vi.mock('../DuplicateCharge', async () => {
    const actual = await vi.importActual('../DuplicateCharge')
    const SubmitButton = ({disabled}: {disabled?: boolean}) => (
        <button type="submit" disabled={disabled}>
            Submit
        </button>
    )
    return {
        ...actual,
        default: actual.default,
        SubmitButton,
    }
})

vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

const mockOnChange = vi.fn()
const mockOnPrevious = vi.fn()
const mockOnBlur = vi.fn()

const defaultProps = {
    formState: {
        priorTransactionId: {value: '', touched: false, error: undefined} as FormField<string>,
        explanation: {value: '', touched: false, error: undefined} as FormField<string>,
    } satisfies DuplicateChargeFields,
    onChange: mockOnChange,
    onPrevious: mockOnPrevious,
    onBlur: mockOnBlur,
}

describe('DuplicateChargeForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders initial form state correctly', () => {
        render(<DuplicateChargeForm {...defaultProps} />)

        // Check initial heading
        expect(screen.getByText(/I was charged twice for the same purchase/i)).toBeInTheDocument()

        // Check transaction list is present
        expect(screen.getByTestId('transaction-list')).toBeInTheDocument()

        // Check all transaction cards are present
        expect(screen.getByTestId('transaction-card-b7916e1a-b846-53db-8936-d7cbb3dbf6af')).toBeInTheDocument()
        expect(screen.getByTestId('transaction-card-2')).toBeInTheDocument()
        expect(screen.getByTestId('transaction-card-3')).toBeInTheDocument()
        expect(screen.getByTestId('no-transaction-card')).toBeInTheDocument()

        // Check explanation field is present
        expect(screen.getByLabelText(/Additional Information/i)).toBeInTheDocument()

        // Submit button should be enabled as there are no required fields
        expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
    })

    describe('Transaction Selection', () => {
        it('handles transaction selection', async () => {
            render(<DuplicateChargeForm {...defaultProps} />)

            const transactionCard = screen.getByTestId('transaction-card-b7916e1a-b846-53db-8936-d7cbb3dbf6af')
            const button = within(transactionCard).getByRole('button')
            await userEvent.click(button)

            expect(mockOnChange).toHaveBeenCalledWith(
                expect.objectContaining({
                    priorTransactionId: 'b7916e1a-b846-53db-8936-d7cbb3dbf6af',
                }),
            )
        })

        it('handles "not found" transaction selection', async () => {
            render(<DuplicateChargeForm {...defaultProps} />)

            const noTransactionCard = screen.getByTestId('no-transaction-card')
            const button = within(noTransactionCard).getByRole('button')
            await userEvent.click(button)

            expect(mockOnChange).toHaveBeenCalledWith(
                expect.objectContaining({
                    priorTransactionId: 'NOT_FOUND',
                }),
            )
        })

        it('updates selected state when transaction is selected', async () => {
            const {rerender} = render(<DuplicateChargeForm {...defaultProps} />)

            const transactionCard = screen.getByTestId('transaction-card-2')
            const button = within(transactionCard).getByRole('button')
            await userEvent.click(button)

            // Re-render with updated form state
            rerender(
                <DuplicateChargeForm
                    {...{
                        ...defaultProps,
                        formState: {
                            ...defaultProps.formState,
                            priorTransactionId: {value: '2', touched: true, error: undefined} as FormField<string>,
                        },
                    }}
                />,
            )

            const selectedCard = screen.getByTestId('transaction-card-2')
            expect(within(selectedCard).getByRole('button')).toHaveClass('Mui-selected')
            expect(mockOnChange).toHaveBeenCalled()
        })
    })

    describe('Additional Information', () => {
        it('triggers onChange when typing in explanation field', async () => {
            render(<DuplicateChargeForm {...defaultProps} />)

            const explanationField = screen.getByLabelText(/Additional Information/i)
            await userEvent.type(explanationField, 'a')

            expect(mockOnChange).toHaveBeenCalled()
        })

        it('triggers onBlur when explanation field loses focus', async () => {
            render(<DuplicateChargeForm {...defaultProps} />)

            const explanationField = screen.getByLabelText(/Additional Information/i)
            await userEvent.click(explanationField)
            await userEvent.tab()

            expect(mockOnBlur).toHaveBeenCalledWith('explanation')
        })
    })

    it('calls onPrevious when previous button is clicked', async () => {
        render(<DuplicateChargeForm {...defaultProps} />)

        const previousButton = screen.getByRole('button', {name: /previous/i})
        await userEvent.click(previousButton)

        expect(mockOnPrevious).toHaveBeenCalled()
    })
})
