import {render, screen} from '@testing-library/react'
import {describe, it, beforeEach, expect, vi} from 'vitest'
import userEvent from '@testing-library/user-event'
import OtherForm from '../Other'
import type {FormField, OtherFields} from '../../DisputeTypes'

// Mock the SubmitButton component
vi.mock('../Other', async () => {
    const actual = await vi.importActual('../Other')
    const SubmitButton = ({disabled}: {disabled?: boolean}) => (
        <button type="submit" disabled={disabled}>
            Submit
        </button>
    )
    return {
        ...actual,
        default: actual.default,
        SubmitButton,
    }
})

vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

const mockOnChange = vi.fn()
const mockOnPrevious = vi.fn()
const mockOnBlur = vi.fn()

const defaultProps = {
    formState: {
        explanation: {value: '', touched: false, error: undefined} as FormField<string>,
    } satisfies OtherFields,
    onChange: mockOnChange,
    onPrevious: mockOnPrevious,
    onBlur: mockOnBlur,
}

describe('OtherForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders initial form state correctly', () => {
        render(<OtherForm {...defaultProps} />)

        // Check initial heading
        expect(screen.getByText(/Something else went wrong/i)).toBeInTheDocument()

        // Check explanation field is present
        expect(screen.getByLabelText(/What went wrong?/i)).toBeInTheDocument()

        // Submit button should be disabled initially
        expect(screen.getByRole('button', {name: /submit/i})).toBeDisabled()
    })

    describe('Submit Button', () => {
        it('enables submit button when explanation is filled', async () => {
            render(
                <OtherForm
                    {...{
                        ...defaultProps,
                        formState: {
                            explanation: {
                                value: 'Test explanation',
                                touched: true,
                                error: undefined,
                            } as FormField<string>,
                        },
                    }}
                />,
            )

            expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
        })

        it('keeps submit button disabled when explanation is empty', async () => {
            render(
                <OtherForm
                    {...{
                        ...defaultProps,
                        formState: {
                            explanation: {value: '', touched: true, error: undefined} as FormField<string>,
                        },
                    }}
                />,
            )

            expect(screen.getByRole('button', {name: /submit/i})).toBeDisabled()
        })
    })

    describe('Explanation Field', () => {
        it('triggers onChange and updates submit button state when typing', async () => {
            const {rerender} = render(<OtherForm {...defaultProps} />)

            // Initially submit should be disabled
            expect(screen.getByRole('button', {name: /submit/i})).toBeDisabled()

            const explanationField = screen.getByLabelText(/What went wrong?/i)
            await userEvent.type(explanationField, 'a')

            expect(mockOnChange).toHaveBeenCalledWith({
                explanation: 'a',
            })

            // Rerender with updated form state
            rerender(
                <OtherForm
                    {...{
                        ...defaultProps,
                        formState: {
                            explanation: {
                                value: 'a',
                                touched: true,
                                error: undefined,
                            } as FormField<string>,
                        },
                    }}
                />,
            )

            // Submit should now be enabled
            expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
        })

        it('triggers onBlur when field loses focus', async () => {
            render(<OtherForm {...defaultProps} />)

            const explanationField = screen.getByLabelText(/What went wrong?/i)
            await userEvent.click(explanationField)
            await userEvent.tab()

            expect(mockOnBlur).toHaveBeenCalledWith('explanation')
        })
    })

    it('calls onPrevious when previous button is clicked', async () => {
        render(<OtherForm {...defaultProps} />)

        const previousButton = screen.getByRole('button', {name: /previous/i})
        await userEvent.click(previousButton)

        expect(mockOnPrevious).toHaveBeenCalled()
    })
})
