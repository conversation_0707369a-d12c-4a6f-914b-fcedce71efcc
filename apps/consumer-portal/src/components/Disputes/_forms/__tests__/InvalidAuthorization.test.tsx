import {render, screen, within} from '@testing-library/react'
import {describe, it, beforeEach, expect, vi} from 'vitest'
import userEvent from '@testing-library/user-event'
import InvalidAuthorizationForm from '../InvalidAuthorization'
import type {FormField, FraudFields, InvalidAuthorizationFields} from '../../DisputeTypes'

// Mock the SubmitButton component
vi.mock('../InvalidAuthorization', async () => {
    const actual = await vi.importActual('../InvalidAuthorization')
    const SubmitButton = ({disabled}: {disabled?: boolean}) => (
        <button type="submit" disabled={disabled}>
            Submit
        </button>
    )
    return {
        ...actual,
        default: actual.default,
        SubmitButton,
    }
})

vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

const mockOnChange = vi.fn()
const mockOnPrevious = vi.fn()
const mockOnBlur = vi.fn()

const defaultProps = {
    formState: {
        reason: {value: 'invalidAuthorization', touched: false, error: undefined} as FormField<'invalidAuthorization'>,
        explanation: {value: '', touched: false, error: undefined} as FormField<string>,
    } satisfies InvalidAuthorizationFields,
    onChange: mockOnChange,
    onPrevious: mockOnPrevious,
    onBlur: mockOnBlur,
}

describe('InvalidAuthorizationForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders initial form state correctly', () => {
        render(<InvalidAuthorizationForm {...defaultProps} />)

        // Check initial heading
        expect(
            screen.getByText(/I didn't make this purchase and\/or this purchase was fraudulent/i),
        ).toBeInTheDocument()

        // Check fraud checkbox is present and unchecked
        const fraudCheckbox = screen.getByRole('checkbox', {name: /This purchase was fraudulent/i})
        expect(fraudCheckbox).not.toBeChecked()

        // Check explanation field is present
        expect(screen.getByLabelText(/Additional Details/i)).toBeInTheDocument()

        // Submit button should be enabled as there are no required fields
        expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
    })

    describe('Fraud Checkbox', () => {
        it('changes reason to fraud when checked', async () => {
            render(<InvalidAuthorizationForm {...defaultProps} />)

            const fraudCheckbox = screen.getByRole('checkbox', {name: /This purchase was fraudulent/i})
            await userEvent.click(fraudCheckbox)

            expect(mockOnChange).toHaveBeenCalledWith(
                expect.objectContaining({
                    reason: 'fraud',
                }),
            )
            expect(mockOnBlur).toHaveBeenCalledWith('reason')
        })

        it('changes reason back to invalidAuthorization when unchecked', async () => {
            // Render with fraud initially checked
            render(
                <InvalidAuthorizationForm
                    {...{
                        ...defaultProps,
                        formState: {
                            ...defaultProps.formState,
                            reason: {value: 'fraud', touched: true, error: undefined} as FormField<'fraud'>,
                        } satisfies FraudFields,
                    }}
                />,
            )

            const fraudCheckbox = screen.getByRole('checkbox', {name: /This purchase was fraudulent/i})
            expect(fraudCheckbox).toBeChecked()

            await userEvent.click(fraudCheckbox)

            expect(mockOnChange).toHaveBeenCalledWith(
                expect.objectContaining({
                    reason: 'invalidAuthorization',
                }),
            )
            expect(mockOnBlur).toHaveBeenCalledWith('reason')
        })

        it('shows error message when reason has error', () => {
            render(
                <InvalidAuthorizationForm
                    {...{
                        ...defaultProps,
                        formState: {
                            ...defaultProps.formState,
                            reason: {
                                value: 'invalidAuthorization',
                                touched: true,
                                error: 'Test error message',
                            } as FormField<'invalidAuthorization'>,
                        } satisfies InvalidAuthorizationFields,
                    }}
                />,
            )

            expect(screen.getByText('Test error message')).toBeInTheDocument()
        })
    })

    describe('Additional Information', () => {
        it('triggers onChange when typing in explanation field', async () => {
            render(<InvalidAuthorizationForm {...defaultProps} />)

            const explanationField = screen.getByLabelText(/Additional Details/i)
            await userEvent.type(explanationField, 'a')

            expect(mockOnChange).toHaveBeenCalled()
        })

        it('triggers onBlur when explanation field loses focus', async () => {
            render(<InvalidAuthorizationForm {...defaultProps} />)

            const explanationField = screen.getByLabelText(/Additional Details/i)
            await userEvent.click(explanationField)
            await userEvent.tab()

            expect(mockOnBlur).toHaveBeenCalledWith('explanation')
        })
    })

    it('calls onPrevious when previous button is clicked', async () => {
        render(<InvalidAuthorizationForm {...defaultProps} />)

        const previousButton = screen.getByRole('button', {name: /previous/i})
        await userEvent.click(previousButton)

        expect(mockOnPrevious).toHaveBeenCalled()
    })
})
