import {render, screen, within} from '@testing-library/react'
import {describe, it, beforeEach, expect, vi} from 'vitest'
import userEvent from '@testing-library/user-event'
import CreditNotProcessedForm from '../CreditNotProcessed'
import type {FormField, CreditNotProcessedFields} from '../../DisputeTypes'
import {LocalizationProvider} from '@mui/x-date-pickers'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'

// Mock the SubmitButton component
vi.mock('../CreditNotProcessed', async () => {
    const actual = await vi.importActual('../CreditNotProcessed')
    const SubmitButton = ({disabled}: {disabled?: boolean}) => (
        <button type="submit" disabled={disabled}>
            Submit
        </button>
    )
    return {
        ...actual,
        default: actual.default,
        SubmitButton,
    }
})

vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

const mockOnChange = vi.fn()
const mockOnPrevious = vi.fn()
const mockOnBlur = vi.fn()

const defaultProps = {
    formState: {
        returnDate: {value: null, touched: false, error: undefined} as unknown as FormField<Date>,
        returnShippingCarrier: {value: '', touched: false, error: undefined} as FormField<string>,
        returnShippingTracking: {value: '', touched: false, error: undefined} as FormField<string>,
        returnedSuccess: {value: null, touched: false, error: undefined} as unknown as FormField<boolean>,
        returnDescription: {value: '', touched: false, error: undefined} as FormField<string>,
        cancellationDate: {value: null, touched: false, error: undefined} as unknown as FormField<Date>,
        cancellationId: {value: '', touched: false, error: undefined} as FormField<string>,
        cancellationDescription: {value: '', touched: false, error: undefined} as FormField<string>,
    } satisfies CreditNotProcessedFields,
    onChange: mockOnChange,
    onPrevious: mockOnPrevious,
    onBlur: mockOnBlur,
}

const WithProviders = ({children}: {children: React.ReactNode}) => {
    return <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
}

describe('CreditNotProcessedForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders initial form state correctly', () => {
        render(<CreditNotProcessedForm {...defaultProps} />, {wrapper: WithProviders})

        // Check initial heading
        expect(screen.getByText(/I requested a refund for this purchase but never received it/i)).toBeInTheDocument()

        // Check initial question about purchase type
        expect(screen.getByLabelText(/Purchase Type/i)).toBeInTheDocument()

        // There are no required fields on this form. Make sure the submit button is enabled
        expect(screen.getByRole('button', {name: /submit/i})).not.toBeDisabled()
    })

    describe('Product Type Selection Flow', () => {
        beforeEach(async () => {
            render(<CreditNotProcessedForm {...defaultProps} />, {wrapper: WithProviders})
        })

        it('shows merchandise specific fields when merchandise is selected', async () => {
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const merchandiseOption = screen.getByRole('option', {name: /Merchandise/i})
            await userEvent.click(merchandiseOption)

            const questionSection = screen.getByTestId('merchandise-question-section')
            expect(within(questionSection).getByText(/Did you return your purchase?/i)).toBeInTheDocument()
        })

        it('shows digital specific fields when digital is selected', async () => {
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const digitalOption = screen.getByRole('option', {name: /Digital/i})
            await userEvent.click(digitalOption)

            const digitalSection = screen.getByTestId('digital-section')
            expect(within(digitalSection).getByLabelText(/Reason for Return/i)).toBeInTheDocument()
        })

        it('shows service specific fields when service is selected', async () => {
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const serviceOption = screen.getByRole('option', {name: /Service/i})
            await userEvent.click(serviceOption)

            const serviceSection = screen.getByTestId('service-section')
            expect(within(serviceSection).getByLabelText(/Cancellation Date/i)).toBeInTheDocument()
            expect(within(serviceSection).getByLabelText(/Cancellation Number/i)).toBeInTheDocument()
            expect(within(serviceSection).getByLabelText(/Reason for Cancellation/i)).toBeInTheDocument()
        })
    })

    describe('Return Flow for Merchandise', () => {
        beforeEach(async () => {
            render(<CreditNotProcessedForm {...defaultProps} />, {wrapper: WithProviders})

            // Select merchandise
            const productTypeSelect = screen.getByLabelText(/Purchase Type/i)
            await userEvent.click(productTypeSelect)
            const merchandiseOption = screen.getByRole('option', {name: /Merchandise/i})
            await userEvent.click(merchandiseOption)
        })

        it('shows shipping fields when product was returned', async () => {
            const questionSection = screen.getByTestId('merchandise-question-section')
            const yesRadio = within(questionSection).getByLabelText('Yes')
            await userEvent.click(yesRadio)

            const returnSection = screen.getByTestId('merchandise-return-section')
            expect(within(returnSection).getByLabelText(/When did you ship it?/i)).toBeInTheDocument()
            expect(within(returnSection).getByLabelText(/Carrier/i)).toBeInTheDocument()
            expect(within(returnSection).getByLabelText(/Tracking Number/i)).toBeInTheDocument()
            expect(
                within(returnSection).getByText(/Did you receive confirmation that the shipment arrived?/i),
            ).toBeInTheDocument()
        })

        it('handles carrier selection', async () => {
            const questionSection = screen.getByTestId('merchandise-question-section')
            const yesRadio = within(questionSection).getByLabelText('Yes')
            await userEvent.click(yesRadio)

            const returnSection = screen.getByTestId('merchandise-return-section')
            const carrierSelect = within(returnSection).getByLabelText(/Carrier/i)
            await userEvent.click(carrierSelect)
            const upsOption = screen.getByRole('option', {name: /UPS/i})
            await userEvent.click(upsOption)

            expect(mockOnChange).toHaveBeenCalledWith(
                expect.objectContaining({
                    returnShippingCarrier: 'UPS',
                }),
            )
        })
    })

    it('calls onPrevious when previous button is clicked', async () => {
        render(<CreditNotProcessedForm {...defaultProps} />, {wrapper: WithProviders})

        const previousButton = screen.getByRole('button', {name: /previous/i})
        await userEvent.click(previousButton)

        expect(mockOnPrevious).toHaveBeenCalled()
    })
})
