'use client'

import {useFormStatus} from 'react-dom'
import {Button, FormGroup, Grid, Typography, TextField, Icon} from '@mui/material'
import type {DisputeFormState} from '../DisputeTypes'
import type {CreateDisputeRequest} from '@/services/controllers/disputes'

interface OtherFormProps {
    formState: DisputeFormState['other']
    onChange: (fields: Partial<Extract<CreateDisputeRequest, {reason: 'other'}>>) => void
    onPrevious: () => void
    onBlur: (fieldName: string) => void
}

export default function OtherForm({formState, onChange, onPrevious, onBlur}: OtherFormProps) {
    return (
        <Grid container flexDirection="column" gap={2}>
            <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                &quot;Something else went wrong&quot;
            </Typography>
            <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                Tell us more...
            </Typography>
            <Typography fontSize={16} color="textSecondary">
                Please provide any details that might be helpful in helping us to understand what went wrong.
            </Typography>
            <FormGroup sx={{gap: 3}}>
                <TextField
                    size="medium"
                    label="What went wrong?"
                    placeholder="Please provide any details that might be helpful in helping us to understand what went wrong."
                    name="explanation"
                    value={formState.explanation?.value || ''}
                    onChange={e => onChange({explanation: e.target.value})}
                    onBlur={() => onBlur('explanation')}
                    rows={3}
                    multiline
                    error={formState.explanation?.touched && !!formState.explanation?.error}
                    helperText={formState.explanation?.touched ? formState.explanation?.error : undefined}
                    slotProps={{
                        inputLabel: {
                            shrink: true,
                        },
                    }}
                />
            </FormGroup>

            <Grid container flexDirection="column" gap={2}>
                <SubmitButton disabled={!formState.explanation?.value} />
                <Button
                    fullWidth
                    variant="text"
                    startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                    onClick={onPrevious}
                >
                    Previous
                </Button>
            </Grid>
        </Grid>
    )
}

function SubmitButton({disabled = false}: {disabled?: boolean}) {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending} disabled={disabled}>
            Submit
        </Button>
    )
}
