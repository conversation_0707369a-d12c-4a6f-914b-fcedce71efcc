'use client'

import {useFormStatus} from 'react-dom'
import {
    Button,
    FormGroup,
    Grid,
    Typography,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Collapse,
    Icon,
} from '@mui/material'
import {DatePicker} from '@mui/x-date-pickers/DatePicker'
import {LocalizationProvider} from '@mui/x-date-pickers/LocalizationProvider'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'
import type {DisputeFormState} from '../DisputeTypes'
import {type CreateDisputeRequest, DisputeProductType} from '@/services/controllers/disputes'
import dayjs from 'dayjs'

interface ProductNotReceivedProps {
    formState: DisputeFormState['productNotReceived']
    onChange: (fields: Partial<Extract<CreateDisputeRequest, {reason: 'productNotReceived'}>>) => void
    onPrevious: () => void
    onBlur: (fieldName: string) => void
}

export default function ProductNotReceivedForm({formState, onChange, onPrevious, onBlur}: ProductNotReceivedProps) {
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Grid container flexDirection="column" gap={2}>
                <Typography fontSize={20} fontWeight={600} lineHeight="160%" color="textSecondary">
                    &quot;I didn&apos;t receive this purchase&quot;
                </Typography>
                <Typography fontSize={20} fontWeight={600} lineHeight="143%" color="textPrimary">
                    Tell us more...
                </Typography>
                <Typography fontSize={16} color="textSecondary">
                    Providing more details will help us to process your report but is not required.
                </Typography>
                <FormGroup sx={{gap: 3}}>
                    <FormControl error={formState.productType?.touched && !!formState.productType?.error}>
                        <InputLabel id="purchaseType" shrink>
                            Purchase Type
                        </InputLabel>
                        <Select
                            labelId="purchaseType"
                            displayEmpty
                            id="purchaseType"
                            value={formState.productType?.value || ''}
                            label="Purchase Type"
                            onChange={e => onChange({productType: e.target.value as DisputeProductType})}
                            onBlur={() => onBlur('productType')}
                            error={formState.productType?.touched && !!formState.productType?.error}
                            renderValue={(selected: string) => {
                                if (!selected || selected.length === 0) {
                                    return <Typography>What kind of purchase was it</Typography>
                                }
                                const key = Object.keys(DisputeProductType).find(
                                    (key): key is keyof typeof DisputeProductType =>
                                        DisputeProductType[key as keyof typeof DisputeProductType] === selected,
                                )
                                return key || 'Unknown Product Type'
                            }}
                        >
                            <MenuItem value={DisputeProductType.Merchandise}>Merchandise</MenuItem>
                            <MenuItem value={DisputeProductType.Digital}>Digital Item</MenuItem>
                            <MenuItem value={DisputeProductType.Service}>Service</MenuItem>
                        </Select>
                        {formState.productType?.touched && formState.productType?.error && (
                            <Typography color="error" variant="caption">
                                {formState.productType.error}
                            </Typography>
                        )}
                    </FormControl>

                    <Collapse
                        in={
                            formState.productType?.value === DisputeProductType.Merchandise ||
                            formState.productType?.value === DisputeProductType.Digital
                        }
                        sx={{
                            display:
                                formState.productType?.value === DisputeProductType.Merchandise ||
                                formState.productType?.value === DisputeProductType.Digital
                                    ? 'initial'
                                    : 'none',
                        }}
                        data-testid="product-details-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <TextField
                                size="medium"
                                label="Purchase Description"
                                placeholder="Please provide a brief description of your purchase"
                                name="productDescription"
                                value={formState.productDescription?.value || ''}
                                onChange={e => onChange({productDescription: e.target.value})}
                                onBlur={() => onBlur('productDescription')}
                                rows={3}
                                multiline
                                error={formState.productDescription?.touched && !!formState.productDescription?.error}
                                helperText={
                                    formState.productDescription?.touched
                                        ? formState.productDescription?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <TextField
                                size="medium"
                                label="Order ID"
                                placeholder="Provide your Order ID if you have one"
                                name="orderId"
                                value={formState.orderId?.value || ''}
                                onChange={e => onChange({orderId: e.target.value})}
                                onBlur={() => onBlur('orderId')}
                                error={formState.orderId?.touched && !!formState.orderId?.error}
                                helperText={formState.orderId?.touched ? formState.orderId?.error : undefined}
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <DatePicker
                                label="Expected Delivery Date"
                                name="expectedDeliveryDate"
                                value={
                                    formState.expectedDeliveryDate?.value
                                        ? dayjs(formState.expectedDeliveryDate.value)
                                        : null
                                }
                                slotProps={{
                                    textField: {
                                        error: !!formState.expectedDeliveryDate?.error,
                                        helperText: formState.expectedDeliveryDate?.error || '',
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        onBlur: () => onBlur('expectedDeliveryDate'),
                                    },
                                }}
                                onChange={(value: dayjs.Dayjs | null) =>
                                    onChange({expectedDeliveryDate: value?.toDate()})
                                }
                            />
                        </Grid>
                    </Collapse>

                    <Collapse
                        in={formState.productType?.value === DisputeProductType.Service}
                        sx={{
                            display: formState.productType?.value === DisputeProductType.Service ? 'initial' : 'none',
                        }}
                        data-testid="service-details-section"
                    >
                        <Grid container flexDirection="column" gap={3}>
                            <TextField
                                size="medium"
                                label="Purchase Description"
                                placeholder="Please provide a brief description of your purchase"
                                name="productDescription"
                                value={formState.productDescription?.value || ''}
                                onChange={e => onChange({productDescription: e.target.value})}
                                onBlur={() => onBlur('productDescription')}
                                rows={3}
                                multiline
                                error={formState.productDescription?.touched && !!formState.productDescription?.error}
                                helperText={
                                    formState.productDescription?.touched
                                        ? formState.productDescription?.error
                                        : undefined
                                }
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <TextField
                                size="medium"
                                label="Order ID"
                                placeholder="Provide your Order ID if you have one"
                                name="orderId"
                                value={formState.orderId?.value || ''}
                                onChange={e => onChange({orderId: e.target.value})}
                                onBlur={() => onBlur('orderId')}
                                error={formState.orderId?.touched && !!formState.orderId?.error}
                                helperText={formState.orderId?.touched ? formState.orderId?.error : undefined}
                                slotProps={{
                                    inputLabel: {
                                        shrink: true,
                                    },
                                }}
                            />
                            <DatePicker
                                label="When did you realize the service was disrupted?"
                                name="serviceEndDate"
                                value={formState.serviceEndDate?.value ? dayjs(formState.serviceEndDate.value) : null}
                                slotProps={{
                                    textField: {
                                        InputLabelProps: {
                                            shrink: true,
                                        },
                                        error: formState.serviceEndDate?.touched && !!formState.serviceEndDate?.error,
                                        helperText: formState.serviceEndDate?.touched
                                            ? formState.serviceEndDate?.error
                                            : undefined,
                                        onBlur: () => onBlur('serviceEndDate'),
                                    },
                                }}
                                onChange={value => {
                                    onChange({serviceEndDate: value?.toDate()})
                                }}
                            />
                        </Grid>
                    </Collapse>
                </FormGroup>

                <Grid container flexDirection="column" gap={2}>
                    <SubmitButton />
                    <Button
                        fullWidth
                        variant="text"
                        startIcon={<Icon baseClassName="fas" className="fa-chevron-left" />}
                        onClick={onPrevious}
                    >
                        Previous
                    </Button>
                </Grid>
            </Grid>
        </LocalizationProvider>
    )
}

function SubmitButton({disabled = false}: {disabled?: boolean}) {
    const {pending} = useFormStatus()

    return (
        <Button type="submit" fullWidth variant="contained" loading={pending} disabled={disabled}>
            Submit
        </Button>
    )
}
