'use client'

import {startTransition, useActionState} from 'react'
import {useEffect} from 'react'
import {
    Avatar,
    Box,
    Dialog,
    DialogContent,
    DialogTitle,
    IconButton,
    ListItemAvatar,
    ListItem,
    ListItemText,
    useMediaQuery,
    type Theme,
    Typography,
    Grid,
    Card,
    List,
    ListItemButton,
    Button,
    Alert,
    Icon,
} from '@mui/material'
import {formatTransactionDate} from '@/utils/dateFormatters'
import {formatCurrency} from '@tallied-technologies/common'
import {useDisputeForm} from './useDisputeForm'
import InvalidAuthorizationForm from './_forms/InvalidAuthorization'
import {type DisputeReason, disputeReasonDetails} from './DisputeTypes'
import ProductNotReceivedForm from './_forms/ProductNotReceived'
import ProductNotAsDescribedForm from './_forms/ProductNotAsDescribed'
import type {Money} from '@/services/controllers/disputes'
import CreditNotProcessedForm from './_forms/CreditNotProcessed'
import SubscriptionCanceledForm from './_forms/SubscriptionCanceled'
import LoadingSpinnerAnimation from '../LoadingSpinnerAnimation'
import DuplicateChargeForm from './_forms/DuplicateCharge'
import OtherForm from './_forms/Other'
import LoadingDots from '../LoadingDots'
import type {GetTransactionEvents200ResponseEventsInner} from '@/services/controllers/transactionEvents'

interface TransactionProps extends GetTransactionEvents200ResponseEventsInner {}

export default function Disputes({
    open,
    onClose,
    transaction,
}: {
    open: boolean
    onClose: () => void
    transaction: TransactionProps
}) {
    const isDesktop = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'))

    const {selectedReason, setSelectedReason, formState, updateFormFields, handleSubmit, handleBlur} = useDisputeForm({
        transactionId: transaction.transactionId,
        creditAccountId: transaction.creditAccountId,
        amount: transaction.amount,
    })
    const [formSubmissionState, formAction] = useActionState(handleSubmit, {
        status: 'idle' as const,
        message: null,
        fieldErrors: undefined,
    })

    useEffect(() => {
        let timeoutId: NodeJS.Timeout
        let isActive = true

        if (formSubmissionState.status === 'polling') {
            // Call formAction immediately to start polling
            startTransition(() => {
                if (isActive) {
                    formAction()
                }
            })

            // Set up periodic polling
            timeoutId = setTimeout(function poll() {
                startTransition(() => {
                    if (isActive) {
                        formAction()
                    }
                })
                timeoutId = setTimeout(poll, 500)
            }, 500)
        }

        return () => {
            isActive = false
            if (timeoutId) {
                clearTimeout(timeoutId)
            }
        }
    }, [formSubmissionState.status, formAction])

    if (formSubmissionState.status === 'success') {
        return (
            <Dialog open={open} maxWidth="sm" fullWidth fullScreen={!isDesktop} onClose={onClose}>
                <DialogTitle>Dispute Submitted</DialogTitle>
                <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={onClose}>
                    <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="inherit" />
                </IconButton>
                <DialogContent>
                    <Grid container flexDirection="column" alignItems="center" gap={2} sx={{minHeight: 400}}>
                        <Typography color="text.secondary" align="center" sx={{flex: 1, alignContent: 'center'}}>
                            Dispute has been created for your transaction at {transaction.merchantInfo?.descriptor} for
                            the amount of {formatCurrency(transaction.amount.amount / 100)}
                        </Typography>
                        <Button fullWidth variant="contained" onClick={onClose}>
                            Close
                        </Button>
                    </Grid>
                </DialogContent>
            </Dialog>
        )
    }

    if (formSubmissionState.status === 'timeout') {
        return (
            <Dialog open={open} maxWidth="sm" fullWidth fullScreen={!isDesktop} onClose={onClose}>
                <DialogTitle>Dispute Processing</DialogTitle>
                <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={onClose}>
                    <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="inherit" />
                </IconButton>
                <DialogContent>
                    <Grid container flexDirection="column" alignItems="center" gap={2} sx={{minHeight: 400}}>
                        <Typography color="text.secondary" align="center" sx={{flex: 1, alignContent: 'center'}}>
                            Processing your dispute is taking longer than expected. You will be notified when the status
                            of your dispute changes.
                        </Typography>
                        <Button fullWidth variant="contained" onClick={onClose}>
                            Close
                        </Button>
                    </Grid>
                </DialogContent>
            </Dialog>
        )
    }

    if (formSubmissionState.status === 'polling') {
        return (
            <Dialog open={open} maxWidth="sm" fullWidth fullScreen={!isDesktop}>
                <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={onClose}>
                    <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="inherit" />
                </IconButton>
                <DialogContent>
                    <Grid container flexDirection="column" alignItems="center" sx={{py: 4}}>
                        <LoadingSpinnerAnimation />
                        <LoadingDots width={180} />
                    </Grid>
                </DialogContent>
            </Dialog>
        )
    }

    return (
        <Dialog open={open} maxWidth="sm" fullWidth fullScreen={!isDesktop} onClose={onClose}>
            <DialogTitle id="report-problem-dispute">Report a Problem</DialogTitle>
            <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={onClose}>
                <Icon baseClassName="fas" className="fa-solid fa-xmark" fontSize="inherit" />
            </IconButton>
            <DialogContent sx={{pt: 0}}>
                <form action={formAction}>
                    <Grid container flexDirection="column" gap={2}>
                        {selectedReason && formSubmissionState.status === 'error' && (
                            <Alert severity="error" sx={{mt: 2}} role="alert" aria-live="polite">
                                {typeof formSubmissionState.message === 'string' && formSubmissionState.message}
                                {formSubmissionState.fieldErrors && (
                                    <List dense sx={{mt: 1, pl: 2}}>
                                        {Object.entries(formSubmissionState.fieldErrors).map(([field, error]) => (
                                            <ListItem
                                                key={field}
                                                sx={{display: 'list-item', listStyleType: 'disc', p: 0}}
                                            >
                                                <Typography variant="body2">{error}</Typography>
                                            </ListItem>
                                        ))}
                                    </List>
                                )}
                            </Alert>
                        )}
                        <DisputedTransaction transaction={transaction} />
                        {!selectedReason ? (
                            <DisputeReasonSelector onClick={setSelectedReason} onClose={onClose} />
                        ) : (
                            <>
                                {(selectedReason === 'invalidAuthorization' || selectedReason === 'fraud') && (
                                    <InvalidAuthorizationForm
                                        formState={formState.invalidAuthorization}
                                        onChange={fields => updateFormFields(selectedReason, fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {selectedReason === 'productNotReceived' && (
                                    <ProductNotReceivedForm
                                        formState={formState.productNotReceived}
                                        onChange={fields => updateFormFields('productNotReceived', fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {selectedReason === 'productNotAsDescribed' && (
                                    <ProductNotAsDescribedForm
                                        formState={formState.productNotAsDescribed}
                                        onChange={fields => updateFormFields('productNotAsDescribed', fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {selectedReason === 'creditNotProcessed' && (
                                    <CreditNotProcessedForm
                                        formState={formState.creditNotProcessed}
                                        onChange={fields => updateFormFields('creditNotProcessed', fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {selectedReason === 'subscriptionCanceled' && (
                                    <SubscriptionCanceledForm
                                        formState={formState.subscriptionCanceled}
                                        onChange={fields => updateFormFields('subscriptionCanceled', fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {selectedReason === 'duplicateCharge' && (
                                    <DuplicateChargeForm
                                        formState={formState.duplicateCharge}
                                        onChange={fields => updateFormFields('duplicateCharge', fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {selectedReason === 'other' && (
                                    <OtherForm
                                        formState={formState.other}
                                        onChange={fields => updateFormFields('other', fields)}
                                        onPrevious={() => setSelectedReason(undefined)}
                                        onBlur={handleBlur}
                                    />
                                )}
                                {/* Add other form components for different reasons */}
                            </>
                        )}
                    </Grid>
                </form>
            </DialogContent>
        </Dialog>
    )
}

// This needs to read from the transaction object
function DisputedTransaction({transaction}: {transaction: TransactionProps}) {
    return (
        <Box sx={{border: '1px solid var(--mui-palette-error-main)', borderRadius: 2}}>
            <ListItem disableGutters sx={{px: 2}}>
                <ListItemAvatar>
                    <Avatar>
                        <Icon baseClassName="fas" className="fa-solid fa-utensils" fontSize="inherit" />
                    </Avatar>
                </ListItemAvatar>
                <ListItemText
                    primary={
                        <Typography fontWeight={600}>
                            {transaction.merchantInfo?.descriptor ?? 'Unknown Merchant'}
                        </Typography>
                    }
                    secondary={
                        <Typography fontWeight={600} color="textSecondary">
                            {formatTransactionDate(transaction.transactedOn)}
                        </Typography>
                    }
                    sx={{m: 0}}
                />
                <ListItemText
                    primary={<span>&nbsp;</span>}
                    secondary={
                        <Typography fontWeight={600} fontSize={24} color="textPrimary">
                            {formatCurrency(transaction.amount.amount / 100)}
                        </Typography>
                    }
                    sx={{justifyItems: 'end', m: 0}}
                />
            </ListItem>
        </Box>
    )
}

function DisputeReasonSelector({onClick, onClose}: {onClick: (reason: DisputeReason) => void; onClose: () => void}) {
    return (
        <Grid container flexDirection="column" gap={1.5}>
            <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                What went wrong?
            </Typography>
            <List sx={{gap: 1.5, display: 'flex', flexDirection: 'column'}}>
                {Object.keys(disputeReasonDetails)
                    .filter(type => type !== 'fraud')
                    // Fraud cases are handled within the InvalidAuthorizationForm component
                    // to streamline the user experience for both fraud and unauthorized charges
                    .map(disputeReason => (
                        <ListItem
                            key={disputeReason}
                            disablePadding
                            sx={{
                                borderRadius: 2,
                                border: '1px solid transparent',
                                '&:hover,&:focus': {
                                    border: '1px solid var(--mui-palette-primary-main)',
                                },
                            }}
                        >
                            <Card sx={{width: '100%', borderRadius: 2, border: '1px solid var(--mui-palette-divider)'}}>
                                <ListItemButton
                                    sx={{
                                        p: 2,
                                        pr: 1,
                                        '&:hover': {
                                            backgroundColor: 'var(--mui-palette-primary-light)',
                                        },
                                    }}
                                    onClick={() => onClick(disputeReason as DisputeReason)}
                                >
                                    <Typography
                                        fontSize={16}
                                        fontWeight={400}
                                        lineHeight="143%"
                                        color="textPrimary"
                                        flex={1}
                                    >
                                        {disputeReasonDetails[disputeReason as DisputeReason]}
                                    </Typography>
                                    <Icon
                                        baseClassName="fas"
                                        className="fa-solid fa-chevron-right"
                                        fontSize="inherit"
                                    />
                                </ListItemButton>
                            </Card>
                        </ListItem>
                    ))}
            </List>
            <Button fullWidth variant="text" onClick={onClose}>
                Cancel
            </Button>
        </Grid>
    )
}
