import {render, screen, within} from '@testing-library/react'
import {describe, it, expect, vi, beforeEach} from 'vitest'
import userEvent from '@testing-library/user-event'
import Disputes from '../Disputes'
import {LocalizationProvider} from '@mui/x-date-pickers'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'
import type {
    GetTransactionEvents200ResponseEventsInner,
    GetTransactionEvents200ResponseEventsInnerStateEnum,
} from '@/services/controllers/transactionEvents'

// Mock validateSession
vi.mock('@/auth', () => ({
    validateSession: vi.fn().mockResolvedValue({
        user: {
            creditAccountId: 'test-credit-account-id',
            userId: 'test-user-id',
        },
    }),
}))

// Mock form components
vi.mock('../_forms/InvalidAuthorization', () => ({
    default: (props: any) => (
        <div>
            <div>Invalid Authorization Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

vi.mock('../_forms/ProductNotReceived', () => ({
    default: (props: any) => (
        <div>
            <div>Product Not Received Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

vi.mock('../_forms/ProductNotAsDescribed', () => ({
    default: (props: any) => (
        <div>
            <div>Product Not As Described Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

vi.mock('../_forms/CreditNotProcessed', () => ({
    default: (props: any) => (
        <div>
            <div>Credit Not Processed Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

vi.mock('../_forms/SubscriptionCanceled', () => ({
    default: (props: any) => (
        <div>
            <div>Subscription Canceled Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

vi.mock('../_forms/DuplicateCharge', () => ({
    default: (props: any) => (
        <div>
            <div>Duplicate Charge Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

vi.mock('../_forms/Other', () => ({
    default: (props: any) => (
        <div>
            <div>Other Form</div>
            <button onClick={props.onPrevious}>Previous</button>
        </div>
    ),
}))

// Mock the useMediaQuery hook
vi.mock('@mui/material', async () => {
    const actual = await vi.importActual('@mui/material')
    return {
        ...actual,
        useMediaQuery: () => true, // Desktop view
    }
})

// Mock the useFormStatus and useActionState hooks
vi.mock('react-dom', () => ({
    useFormStatus: () => ({
        pending: false,
        data: null,
        method: 'GET',
        action: null,
    }),
}))

vi.mock('react', async () => {
    const actual = await vi.importActual('react')
    return {
        ...actual,
        startTransition: (cb: () => void) => cb(),
        useActionState: () => [{status: 'idle', message: null, fieldErrors: undefined}, vi.fn()],
    }
})

const mockOnClose = vi.fn()

// Mock transaction data
const mockTransaction: GetTransactionEvents200ResponseEventsInner = {
    amount: {
        amount: 1000, // $10.00
        currency: 'USD',
    },
    transactionId: 'tx_123456',
    creditAccountId: 'test-credit-account-id',
    eventId: 'evt_123',
    state: 'posted' as GetTransactionEvents200ResponseEventsInnerStateEnum,
    transactedOn: '2024-12-02T10:00:00Z' as unknown as Date, // December 2, 2024
    type: 'creditAccount/create',
    merchantInfo: {
        descriptor: 'Verifi*las',
        locality: 'San Francisco',
        administrativeArea: 'CA',
        country: 'USA',
        mcc: '5812',
        mid: '************',
    },
}

const WithProviders = ({children}: {children: React.ReactNode}) => {
    return <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
}

describe('Disputes', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders all dispute reason buttons and allows selection', async () => {
        render(<Disputes open={true} onClose={mockOnClose} transaction={mockTransaction} />)

        // Check that all dispute reason buttons are present
        expect(screen.getByText(/I didn't make this purchase/i)).toBeInTheDocument()
        expect(screen.getByText(/I didn't receive this purchase/i)).toBeInTheDocument()
        expect(screen.getByText(/The product I purchased was not as described/i)).toBeInTheDocument()
        expect(screen.getByText(/I cancelled a one time purchase and haven't received a refund/i)).toBeInTheDocument()
        expect(screen.getByText(/I cancelled this subscription but am still being charged/i)).toBeInTheDocument()
        expect(screen.getByText(/I was charged twice for the same purchase/i)).toBeInTheDocument()
        expect(screen.getByText(/Something else went wrong/i)).toBeInTheDocument()

        // Test selecting each form type and verify the correct form is rendered
        const reasons = [
            {
                buttonText: /I didn't make this purchase/i,
                formText: 'Invalid Authorization Form',
            },
            {
                buttonText: /I didn't receive this purchase/i,
                formText: 'Product Not Received Form',
            },
            {
                buttonText: /The product I purchased was not as described/i,
                formText: 'Product Not As Described Form',
            },
            {
                buttonText: /I cancelled a one time purchase and haven't received a refund/i,
                formText: 'Credit Not Processed Form',
            },
            {
                buttonText: /I cancelled this subscription but am still being charged/i,
                formText: 'Subscription Canceled Form',
            },
            {
                buttonText: /I was charged twice for the same purchase/i,
                formText: 'Duplicate Charge Form',
            },
            {
                buttonText: /Something else went wrong/i,
                formText: 'Other Form',
            },
        ]

        for (const {buttonText, formText} of reasons) {
            // Click the reason button
            const button = screen.getByText(buttonText)
            await userEvent.click(button)

            // Verify the correct form is rendered
            expect(screen.getByText(formText)).toBeInTheDocument()

            // Verify the Previous button works
            const previousButton = screen.getByRole('button', {name: /previous/i})
            await userEvent.click(previousButton)

            // Verify we're back to the reason selection
            expect(screen.getByText(/What went wrong?/i)).toBeInTheDocument()
        }
    })

    it('allows closing the dialog', async () => {
        render(<Disputes open={true} onClose={mockOnClose} transaction={mockTransaction} />)

        const closeButton = screen.getByRole('button', {name: /^cancel$/i})
        await userEvent.click(closeButton)

        expect(mockOnClose).toHaveBeenCalled()
    })

    it('displays transaction details', () => {
        render(<Disputes open={true} onClose={mockOnClose} transaction={mockTransaction} />)

        // Check that transaction details are displayed
        expect(screen.getByText('Verifi*las')).toBeInTheDocument()
        expect(screen.getByText('$10.00')).toBeInTheDocument()
        expect(screen.getByText('December 2, 2024')).toBeInTheDocument()
    })
})
