import {renderHook, act} from '@testing-library/react'
import {describe, it, expect, vi, beforeEach} from 'vitest'
import {useDisputeForm} from '../useDisputeForm'
import {createDispute} from '@/actions/disputes'
import type {Money} from '@/services/controllers/disputes'

vi.mock('@/actions/disputes', () => ({
    createDispute: vi.fn(),
}))

const mockTransaction = {
    transactionId: 'test-transaction-id',
    creditAccountId: 'test-account-id',
    amount: {
        amount: 1000,
        currency: 'USD',
    } as Money,
}

describe('useDisputeForm', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    describe('Initial State', () => {
        it('initializes with empty form state and no selected reason', () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            expect(result.current.selectedReason).toBeUndefined()
            expect(result.current.formState).toEqual({
                creditNotProcessed: {},
                duplicateCharge: {},
                fraud: {},
                invalidAuthorization: {},
                other: {},
                productNotAsDescribed: {},
                productNotReceived: {},
                subscriptionCanceled: {},
            })
        })
    })

    describe('Form Field Updates', () => {
        it('updates form fields for productNotAsDescribed reason', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            await act(async () => {
                result.current.setSelectedReason('productNotAsDescribed')
                result.current.updateFormFields('productNotAsDescribed', {
                    contactedSuccess: true,
                    contactDate: new Date('2024-01-01'),
                })
            })

            expect(result.current.formState.productNotAsDescribed).toMatchObject({
                contactedSuccess: {value: true, touched: false},
                contactDate: {value: expect.any(Date), touched: false},
            })
        })

        it('validates future dates for contactDate', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))
            const futureDate = new Date()
            futureDate.setDate(futureDate.getDate() + 1)

            await act(async () => {
                result.current.setSelectedReason('productNotAsDescribed')
                result.current.updateFormFields('productNotAsDescribed', {
                    contactDate: futureDate,
                })
            })

            expect(result.current.formState.productNotAsDescribed.contactDate?.error).toBe(
                'Contact date cannot be in the future',
            )
        })
    })

    describe('Field Validation', () => {
        it('validates required fields for Other reason', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            await act(async () => {
                result.current.setSelectedReason('other')
                result.current.updateFormFields('other', {
                    explanation: '',
                })
                result.current.handleBlur('explanation')
            })

            expect(result.current.formState.other.explanation?.error).toBe('Explanation is required')
        })

        it('validates subscription cancellation date', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))
            const futureDate = new Date()
            futureDate.setDate(futureDate.getDate() + 1)

            await act(async () => {
                result.current.setSelectedReason('subscriptionCanceled')
                result.current.updateFormFields('subscriptionCanceled', {
                    cancellationDate: futureDate,
                })
                result.current.handleBlur('cancellationDate')
            })

            expect(result.current.formState.subscriptionCanceled.cancellationDate?.error).toBe(
                'Cancellation date cannot be in the future',
            )
        })

        it('validates product not received expected delivery date for old dates', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            // Create a date exactly 121 days in the past
            const oldDate = new Date()
            oldDate.setDate(oldDate.getDate() - 121)

            await act(async () => {
                result.current.setSelectedReason('productNotReceived')
                result.current.updateFormFields('productNotReceived', {
                    expectedDeliveryDate: oldDate,
                })
            })

            // Verify the validation error is set correctly
            expect(result.current.formState.productNotReceived.expectedDeliveryDate?.error).toBe(
                'Dispute must be filed within 120 days of expected delivery date',
            )
        })

        it('validates product not received expected delivery date for future dates', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            // Create a date in the future
            const futureDate = new Date()
            futureDate.setDate(futureDate.getDate() + 10)

            await act(async () => {
                result.current.setSelectedReason('productNotReceived')
                result.current.updateFormFields('productNotReceived', {
                    expectedDeliveryDate: futureDate,
                })
            })

            // Verify the validation error is set correctly
            expect(result.current.formState.productNotReceived.expectedDeliveryDate?.error).toBe(
                'Expected delivery date cannot be in the future',
            )
        })

        it('validates product not received expected delivery date for valid dates', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            // Create a date within the valid range (30 days ago)
            const validDate = new Date()
            validDate.setDate(validDate.getDate() - 30)

            await act(async () => {
                result.current.setSelectedReason('productNotReceived')
                result.current.updateFormFields('productNotReceived', {
                    expectedDeliveryDate: validDate,
                })
            })

            // Verify no validation error is set
            expect(result.current.formState.productNotReceived.expectedDeliveryDate?.error).toBeUndefined()
        })
    })

    describe('Form Submission', () => {
        it('handles successful dispute creation', async () => {
            const mockDisputeId = 'test-dispute-id'
            vi.mocked(createDispute).mockResolvedValueOnce({
                success: true,
                data: {disputeId: mockDisputeId},
            })

            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            await act(async () => {
                result.current.setSelectedReason('other')
                result.current.updateFormFields('other', {
                    explanation: 'Test explanation',
                })
            })

            const submissionResult = await act(async () => {
                return await result.current.handleSubmit({status: 'idle'})
            })

            expect(submissionResult).toEqual({
                status: 'polling',
                message: 'Processing dispute...',
                disputeId: mockDisputeId,
                attempts: 0,
            })
            expect(createDispute).toHaveBeenCalledWith(
                expect.objectContaining({
                    reason: 'other',
                    explanation: 'Test explanation',
                    transactionId: mockTransaction.transactionId,
                }),
            )
        })

        it('handles validation errors during submission', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            // User first selects the reason
            await act(async () => {
                // Only set the reason, don't touch the explanation field
                result.current.setSelectedReason('other')
            })

            // When the form loads, the explanation field is undefined
            // The submit button is disabled, so we need to update the field
            await act(async () => {
                result.current.updateFormFields('other', {
                    explanation: undefined,
                })
            })

            // We need to validate the field to enable the submit button
            await act(async () => {
                result.current.handleBlur('explanation')
            })

            // Now the submit button is enabled, so we can submit the form
            const submissionResult = await act(async () => {
                return await result.current.handleSubmit({status: 'idle'})
            })

            expect(submissionResult).toEqual({
                status: 'error',
                message: 'Please fix the following errors:',
                fieldErrors: {
                    explanation: 'Explanation is required',
                },
            })
            expect(createDispute).not.toHaveBeenCalled()
        })

        it('handles API errors during submission', async () => {
            vi.mocked(createDispute).mockResolvedValueOnce({
                success: false,
                error: 'API Error',
                status: 500,
            })

            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            await act(async () => {
                result.current.setSelectedReason('other')
                result.current.updateFormFields('other', {
                    explanation: 'Test explanation',
                })
            })

            const submissionResult = await act(async () => {
                return await result.current.handleSubmit({status: 'idle'})
            })

            expect(submissionResult).toEqual({
                status: 'error',
                message: 'API Error',
            })
        })
    })

    describe('Form Reset', () => {
        it('resets form state and selected reason', async () => {
            const {result} = renderHook(() => useDisputeForm(mockTransaction))

            await act(async () => {
                result.current.setSelectedReason('other')
                result.current.updateFormFields('other', {
                    explanation: 'Test explanation',
                })
                result.current.resetForm()
            })

            expect(result.current.selectedReason).toBeUndefined()
            expect(result.current.formState).toEqual({
                creditNotProcessed: {},
                duplicateCharge: {},
                fraud: {},
                invalidAuthorization: {},
                other: {},
                productNotAsDescribed: {},
                productNotReceived: {},
                subscriptionCanceled: {},
            })
        })
    })
})
