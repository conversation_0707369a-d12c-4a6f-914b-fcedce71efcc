import type {DisputeProductType} from '@/services/controllers/disputes'

export type DisputeReason =
    | 'creditNotProcessed'
    | 'duplicateCharge'
    | 'fraud'
    | 'invalidAuthorization'
    | 'other'
    | 'productNotAsDescribed'
    | 'productNotReceived'
    | 'subscriptionCanceled'

export type FormField<T = any> = {
    value: T
    touched: boolean
    error?: string
}

// Define specific field types for each dispute reason
export type ProductNotReceivedFields = {
    productType?: FormField<DisputeProductType>
    productDescription?: FormField<string>
    orderId?: FormField<string>
    expectedDeliveryDate?: FormField<Date>
    serviceEndDate?: FormField<Date>
    explanation?: FormField<string>
}

export type ProductNotAsDescribedFields = {
    productType?: FormField<DisputeProductType>
    productDescription?: FormField<string>
    orderId?: FormField<string>
    contactDate?: FormField<Date>
    contactedSuccess?: FormField<boolean>
    contactDescription?: FormField<string>
    returnDate?: FormField<Date>
    returnShippingCarrier?: FormField<string>
    returnShippingTracking?: FormField<string>
    returnedSuccess?: FormField<boolean>
    returnDescription?: FormField<string>
    cancellationId?: FormField<string>
    cancellationDate?: FormField<Date>
    productCondition?: FormField<string>
    productDeliveryDate?: FormField<Date>
    explanation?: FormField<string>
}

export type InvalidAuthorizationFields = {
    reason?: FormField<'invalidAuthorization'>
    explanation?: FormField<string>
}

export type FraudFields = {
    reason?: FormField<'fraud'>
    explanation?: FormField<string>
}

export type CreditNotProcessedFields = {
    cancellationId?: FormField<string>
    cancellationDate?: FormField<Date>
    cancellationDescription?: FormField<string>
    returnDate?: FormField<Date>
    returnedSuccess?: FormField<boolean>
    returnShippingCarrier?: FormField<string>
    returnShippingTracking?: FormField<string>
    returnDescription?: FormField<string>
    explanation?: FormField<string>
}

export type SubscriptionCanceledFields = {
    cancellationDate?: FormField<Date>
    cancellationId?: FormField<string>
    explanation?: FormField<string>
}

export type DuplicateChargeFields = {
    priorTransactionId?: FormField<string>
    explanation?: FormField<string>
}

export type OtherFields = {
    explanation?: FormField<string>
}

export type DisputeFormState = {
    creditNotProcessed: CreditNotProcessedFields
    duplicateCharge: DuplicateChargeFields
    fraud: FraudFields
    invalidAuthorization: InvalidAuthorizationFields
    other: OtherFields
    productNotAsDescribed: ProductNotAsDescribedFields
    productNotReceived: ProductNotReceivedFields
    subscriptionCanceled: SubscriptionCanceledFields
}

export type FormSubmissionState = {
    status: 'idle' | 'submitting' | 'polling' | 'success' | 'error' | 'timeout'
    message?: string | null
    disputeId?: string
    fieldErrors?: Record<string, string>
    attempts?: number
}

export const disputeReasonDetails = {
    invalidAuthorization: `I didn't make this purchase`,
    productNotReceived: `I didn't receive this purchase`,
    productNotAsDescribed: `The product I purchased was not as described`,
    creditNotProcessed: `I cancelled a one time purchase and haven't received a refund`,
    subscriptionCanceled: `I cancelled this subscription but am still being charged`,
    duplicateCharge: `I was charged twice for the same purchase`,
    other: `Something else went wrong`,
    fraud: '',
}