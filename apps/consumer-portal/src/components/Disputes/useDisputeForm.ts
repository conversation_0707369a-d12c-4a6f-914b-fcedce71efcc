import {useState, useCallback} from 'react'
import type {
    DisputeReason,
    FormSubmissionState,
    DisputeFormState,
    FormField,
    ProductNotReceivedFields,
    ProductNotAsDescribedFields,
    InvalidAuthorizationFields,
    CreditNotProcessedFields,
    SubscriptionCanceledFields,
    OtherFields,
} from './DisputeTypes'
import {createDispute} from '@/actions/disputes'
import type {CreateDisputeRequest, Money, DisputeStatus} from '@/services/controllers/disputes'

type DisputeFormFields<T extends DisputeReason> = DisputeFormState[T]

const initialFormState: DisputeFormState = {
    creditNotProcessed: {},
    duplicateCharge: {},
    fraud: {},
    invalidAuthorization: {},
    other: {},
    productNotAsDescribed: {},
    productNotReceived: {},
    subscriptionCanceled: {},
} as const

export function useDisputeForm(transaction: {transactionId: string; creditAccountId: string; amount: Money}) {
    const [selectedReason, setSelectedReason] = useState<DisputeReason>()
    const [formState, setFormState] = useState<DisputeFormState>(initialFormState)
    const [lastDisputeId, setLastDisputeId] = useState<string>()

    const validateField = useCallback(
        (reason: DisputeReason, fieldName: string, value: any): string | undefined => {
            const now = new Date()

            switch (reason) {
                case 'productNotAsDescribed': {
                    const fields = formState[reason] as ProductNotAsDescribedFields
                    if (fieldName === 'contactDate') {
                        if (!value) return 'Contact date is required'
                        if (value > now) return 'Contact date cannot be in the future'
                    }
                    break
                }

                case 'productNotReceived': {
                    const fields = formState[reason] as ProductNotReceivedFields
                    if (fieldName === 'expectedDeliveryDate' && value) {
                        if (value > now) return 'Expected delivery date cannot be in the future'
                        const daysAfterExpectedDelivery = Math.ceil(
                            (now.getTime() - value.getTime()) / (1000 * 60 * 60 * 24),
                        )
                        if (daysAfterExpectedDelivery > 120) {
                            return 'Dispute must be filed within 120 days of expected delivery date'
                        }
                    }
                    break
                }

                case 'creditNotProcessed': {
                    const fields = formState[reason] as CreditNotProcessedFields
                    if (fieldName === 'cancellationDate') {
                        if (!value) return 'Cancellation date is required'
                        if (value > now) return 'Cancellation date cannot be in the future'
                    }
                    break
                }

                case 'subscriptionCanceled': {
                    const fields = formState[reason] as SubscriptionCanceledFields
                    if (fieldName === 'cancellationDate') {
                        if (!value) return 'Cancellation date is required'
                        if (value > now) return 'Cancellation date cannot be in the future'
                    }
                    break
                }

                case 'invalidAuthorization':
                case 'fraud': {
                    const fields = formState[reason] as InvalidAuthorizationFields

                    break
                }
                case 'other': {
                    const fields = formState[reason] as OtherFields
                    if (fieldName === 'explanation') {
                        if (!value) return 'Explanation is required'
                    }
                    break
                }
            }
        },
        [formState],
    )

    const updateFormFields = useCallback(
        <T extends DisputeReason>(reason: T, fieldUpdates: Partial<Extract<CreateDisputeRequest, {reason: T}>>) => {
            setFormState(prev => {
                const currentFields = prev[reason] as DisputeFormFields<T>
                const updatedFields = {...currentFields} as DisputeFormFields<T>

                Object.entries(fieldUpdates).forEach(([key, value]) => {
                    const fieldKey = key as keyof DisputeFormFields<T>
                    const currentField = currentFields[fieldKey] as FormField<unknown> | undefined

                    const error = validateField(reason, key, value)

                    // Create a new field if it doesn't exist, or update existing one
                    updatedFields[fieldKey] = {
                        value,
                        touched: currentField?.touched ?? false,
                        error,
                    } as DisputeFormFields<T>[keyof DisputeFormFields<T>]
                })

                return {
                    ...prev,
                    [reason]: updatedFields,
                }
            })
        },
        [validateField],
    )

    const handleBlur = useCallback(
        (fieldName: string) => {
            if (!selectedReason) return

            setFormState(prev => {
                const currentFields = prev[selectedReason] as Record<string, FormField>
                const field = currentFields[fieldName]

                if (!field) return prev

                const error = validateField(selectedReason, fieldName, field.value)

                return {
                    ...prev,
                    [selectedReason]: {
                        ...currentFields,
                        [fieldName]: {
                            ...field,
                            touched: true,
                            error,
                        },
                    },
                }
            })
        },
        [selectedReason, validateField],
    )

    const getFormValues = useCallback(
        <T extends DisputeReason>(reason: T): Extract<CreateDisputeRequest, {reason: T}> => {
            const fields = formState[reason] as DisputeFormFields<T>
            const values = {} as Extract<CreateDisputeRequest, {reason: T}>

            Object.entries(fields).forEach(([key, field]) => {
                if (field.value !== undefined) {
                    values[key as keyof typeof values] = field.value
                }
            })

            return {
                ...values,
                reason,
                creditAccountId: transaction.creditAccountId,
                transactionId: transaction.transactionId,
                amount: transaction.amount,
            }
        },
        [formState, transaction],
    )

    const validateForm = useCallback(
        (reason: DisputeReason): Record<string, string> => {
            const fields = formState[reason] as Record<string, FormField>
            const errors: Record<string, string> = {}

            Object.entries(fields).forEach(([key, field]) => {
                if (field.touched) {
                    const error = validateField(reason, key, field.value)
                    if (error) errors[key] = error
                }
            })

            return errors
        },
        [formState, validateField],
    )

    const resetForm = useCallback(() => {
        setFormState(initialFormState)
        setSelectedReason(undefined)
    }, [])

    const pollDisputeStatus = useCallback(
        async (
            disputeId: string,
            attempt = 0,
        ): Promise<{
            status: DisputeStatus | 'failed' | 'error' | 'pending'
            message?: string
            data?: any
        }> => {
            try {
                const response = await fetch(`/api/disputes?disputeId=${disputeId}`)

                // For the first 6 attempts, treat 404 as still processing
                if (response.status === 404 && attempt < 6) {
                    return {status: 'pending', message: 'Processing dispute...'}
                }

                if (!response.ok) {
                    // Toss to DD
                    // This would be 500's or 400's outside of the 404's
                    return {status: 'error'}
                }

                const data = await response.json()
                return {status: data.status, data}
            } catch (error) {
                // Toss to DD
                // This would be more or less the response.json failing to parse json.
                // Might convert the above !response.ok to a throw call to hit here.
                return {status: 'error'}
            }
        },
        [],
    )

    const handleSubmit = async (state: FormSubmissionState): Promise<FormSubmissionState> => {
        // If we're already polling, check the status
        if (state.status === 'polling' && lastDisputeId) {
            const {status, message, data} = await pollDisputeStatus(lastDisputeId, state.attempts || 0)

            if (status === 'created') {
                return {
                    status: 'success',
                    message: message || 'Dispute created successfully',
                    disputeId: lastDisputeId,
                }
            }

            if (status === 'failed') {
                let errorDetails = {}

                if (data?.errorDetails?.errors) {
                    errorDetails = data?.errorDetails?.errors?.reduce((acc: Record<string, string>, error: string) => {
                        acc['Form Error'] = error
                        return acc
                    }, {})
                }
                return {
                    status: 'error',
                    message: data?.errorDetails?.message || 'Failed to submit dispute',
                    disputeId: lastDisputeId,
                    fieldErrors: errorDetails,
                }
            }

            if (status === 'error') {
                return {
                    status: 'error',
                    message: message || 'Failed to create dispute',
                    disputeId: lastDisputeId,
                }
            }

            // If we've been polling too long (60 attempts * 500ms = 30 seconds)
            if (state.attempts && state.attempts >= 60) {
                return {
                    status: 'timeout',
                    message: 'Dispute creation timed out',
                    disputeId: lastDisputeId,
                }
            }

            // Continue polling
            return {
                status: 'polling',
                message: 'Processing dispute...',
                disputeId: lastDisputeId,
                attempts: (state.attempts || 0) + 1,
            }
        }

        // Initial submission
        if (!selectedReason) return {status: 'idle', message: 'Please select a reason', fieldErrors: undefined}

        const validationResult = validateForm(selectedReason)
        if (Object.keys(validationResult).length > 0) {
            return {
                status: 'error',
                message: 'Please fix the following errors:',
                fieldErrors: validationResult,
            }
        }

        try {
            const disputeData = getFormValues(selectedReason)
            const dispute = await createDispute(disputeData)

            if (!dispute.success) {
                throw new Error(dispute.error)
            }

            // Store the dispute ID for polling
            setLastDisputeId(dispute.data.disputeId)

            // Return polling state immediately after getting disputeId
            return {
                status: 'polling',
                message: 'Processing dispute...',
                disputeId: dispute.data.disputeId,
                attempts: 0,
            }
        } catch (error) {
            return {
                status: 'error',
                message: error instanceof Error ? error.message : 'Failed to submit dispute',
            }
        }
    }

    return {
        selectedReason,
        setSelectedReason,
        formState,
        updateFormFields,
        handleSubmit,
        resetForm,
        pollDisputeStatus,
        handleBlur,
        getFieldError: useCallback(
            (reason: DisputeReason, fieldName: string) => {
                const fields = formState[reason] as Record<string, FormField>
                return fields[fieldName]?.error
            },
            [formState],
        ),
        isFieldTouched: useCallback(
            (reason: DisputeReason, fieldName: string) => {
                const fields = formState[reason] as Record<string, FormField>
                return fields[fieldName]?.touched ?? false
            },
            [formState],
        ),
    }
}
