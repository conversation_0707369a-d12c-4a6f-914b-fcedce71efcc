'use client'

import {useState} from 'react'
import {formatCurrency, flipNumberSign} from '@tallied-technologies/common'
import type {GetTransactionEvents200ResponseEventsInner} from '@/services/controllers/transactionEvents'
import {
    Collapse,
    Divider,
    Grid,
    List,
    ListItem,
    ListItemText,
    Typography,
    CircularProgress,
    Box,
    styled,
    ListItemButton,
    ListItemAvatar,
    Avatar,
    Icon,
    Chip,
    type ChipProps,
} from '@mui/material'
import {formatTransactionDate, formatDisputeDate} from '@/utils/dateFormatters'
import FileDispute from '@/components/Disputes/FileDispute'
import {useDispute} from '@/hooks/useDisputeData'
import type {DisputeStatusAll, GetDisputes200ResponseDisputesInner} from '@/services/controllers/disputes'
import {disputeReasonDetails} from './Disputes/DisputeTypes'
import {useEnrichedTransaction} from '@/hooks/useEnrichedTransaction'

interface TransactionProps extends GetTransactionEvents200ResponseEventsInner {}

const TransactionContainer = styled(Grid, {
    shouldForwardProp: prop => prop !== 'underDispute',
})<{underDispute: boolean}>(({theme, underDispute}) => ({
    borderLeft: '2px solid',
    borderLeftColor: underDispute ? theme.palette.error.main : 'transparent',
}))

export default function Transaction(props: TransactionProps) {
    const [open, setOpen] = useState(false)
    const {data: dispute, isLoading} = useDispute(props.disputeId || '', {enabled: open && !!props.disputeId})
    const {
        data: enrichedTransaction,
        isLoading: isEnrichedLoading,
        isError: isEnrichedError,
    } = useEnrichedTransaction(props, open)

    return (
        <TransactionContainer
            container
            flexDirection="column"
            underDispute={!!props.disputeId}
            aria-label={props.disputeId ? 'Transaction under dispute' : 'Transaction'}
        >
            <TransactionItem transaction={props} onClick={() => setOpen(!open)} open={open} />
            <Collapse in={open}>
                <Grid container flexDirection="column" gap={2} p={2} aria-hidden={!open}>
                    {isEnrichedLoading ? (
                        <Box display="flex" justifyContent="center" p={2}>
                            <CircularProgress size={24} />
                        </Box>
                    ) : isEnrichedError ? (
                        <Box display="flex" justifyContent="center" p={2}>
                            <Typography color="error">Could not load additional transaction details</Typography>
                        </Box>
                    ) : (
                        <TansactionDetails 
                            isLoading={isLoading} 
                            transaction={{
                                ...props,
                                cardLastFour: enrichedTransaction?.cardLastFour,
                                firstName: enrichedTransaction?.firstName || '',
                                lastName: enrichedTransaction?.lastName || '',
                            }} 
                            dispute={dispute} 
                        />
                    )}
                </Grid>
                <Divider />
            </Collapse>
        </TransactionContainer>
    )
}

interface TransactionItemProps {
    transaction: TransactionProps
    onClick: () => void
    open: boolean
}

function TransactionItem({transaction, onClick, open}: TransactionItemProps) {
    return (
        <ListItemButton divider disableGutters onClick={onClick} sx={{px: 2}} aria-label="Transaction Row Button">
            <ListItemAvatar>
                <Avatar>M</Avatar>
            </ListItemAvatar>
            <ListItemText
                primary={transaction.merchantInfo?.descriptor ?? 'Transaction'}
                secondary={formatTransactionDate(transaction.transactedOn)}
                slotProps={{
                    primary: {
                        fontWeight: 600,
                    },
                    secondary: {
                        color: 'var(--mui-palette-text-secondary)',
                    },
                }}
            />
            <ListItemText
                primary={<TransactionAmountWithDiputeBubble transaction={transaction} />}
                secondary={
                    transaction.outstandingBalance ? (
                        formatCurrency(transaction.outstandingBalance.amount / 100)
                    ) : (
                        <span>&nbsp;</span>
                    )
                }
                sx={{justifyItems: 'end'}}
                slotProps={{
                    primary: {
                        fontWeight: 600,
                    },
                    secondary: {
                        fontStyle: 'italic',
                    },
                }}
            />
            <Icon
                baseClassName="fas"
                className={`fa-solid ${open ? 'fa-chevron-up' : 'fa-chevron-down'}`}
                fontSize="small"
                sx={{ml: 1}}
            />
        </ListItemButton>
    )
}

function TransactionAmountWithDiputeBubble({transaction}: {transaction: TransactionProps}) {
    return (
        <Box display="flex" alignItems="flex-end">
            {transaction.disputeId && (
                <Icon
                    baseClassName="fas"
                    className="fa-solid fa-comment"
                    fontSize="small"
                    sx={{mr: 1, color: 'var(--mui-palette-error-main)'}}
                />
            )}
            {formatCurrency(flipNumberSign(transaction.amount.amount) / 100)}
        </Box>
    )
}

function TansactionDetails({
    transaction,
    dispute,
    isLoading,
}: {
    transaction?: ReturnType<typeof useEnrichedTransaction>['data']
    dispute?: GetDisputes200ResponseDisputesInner | null
    isLoading: boolean
}) {
    if (isLoading || !transaction) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress size={24} />
            </Box>
        )
    }
    return (
        <>
            <List dense disablePadding aria-label="Transaction Row Details">
                {dispute && (
                    <ListItem>
                        <ListItemText>
                            <Typography component="span" color="error" fontWeight={600} sx={{mr: 1}}>
                                Disputed
                            </Typography>
                            <Typography component="span" color="text.secondary">
                                {formatDisputeDate(dispute.details.dispute.date)}
                            </Typography>
                        </ListItemText>
                    </ListItem>
                )}
                {transaction.state === 'pending' && transaction.transactedOn && (
                    <ListItem>
                        <ListItemText>
                            <Typography component="span" color="success" fontWeight={600} sx={{mr: 1}}>
                                Pending
                            </Typography>
                            <Typography component="span" color="text.secondary">
                                {formatTransactionDate(transaction.transactedOn)}
                            </Typography>
                        </ListItemText>
                    </ListItem>
                )}
                {transaction.state === 'posted' && transaction.postedOn && (
                    <ListItem>
                        <ListItemText>
                            <Typography component="span" color="success" fontWeight={600} sx={{mr: 1}}>
                                Posted
                            </Typography>
                            <Typography component="span" color="text.secondary">
                                {formatTransactionDate(transaction.postedOn)}
                            </Typography>
                        </ListItemText>
                    </ListItem>
                )}
                <ListItem>
                    <ListItemText>
                        <Typography component="span" color="text.primary" fontWeight={600} sx={{mr: 1}}>
                            Location
                        </Typography>
                        <Typography component="span" color="text.secondary">
                            {transaction.merchantInfo?.locality} {transaction.merchantInfo?.administrativeArea}
                        </Typography>
                    </ListItemText>
                </ListItem>
                <ListItem>
                    <ListItemText>
                        <Typography component="span" color="text.primary" fontWeight={600} sx={{mr: 1}}>
                            Contact
                        </Typography>
                        <Typography component="span" color="text.secondary"></Typography>
                    </ListItemText>
                </ListItem>
                <ListItem>
                    <ListItemText>
                        <Typography component="span" color="text.primary" fontWeight={600} sx={{mr: 1}}>
                            Reward Points Earned
                        </Typography>
                        <Typography component="span" color="text.secondary"></Typography>
                    </ListItemText>
                </ListItem>
                <ListItem>
                    <ListItemText>
                        <Typography component="span" color="text.primary" fontWeight={600} sx={{mr: 1}}>
                            Purchased By
                        </Typography>
                        <Typography component="span" color="text.secondary">
                            {transaction?.firstName} {transaction?.lastName}
                        </Typography>
                    </ListItemText>
                </ListItem>
                <ListItem>
                    <ListItemText>
                        <Typography component="span" color="text.primary" fontWeight={600} sx={{mr: 1}}>
                            Card Used
                        </Typography>
                        <Typography component="span" color="text.secondary">
                            {transaction?.cardLastFour}
                        </Typography>
                    </ListItemText>
                </ListItem>
            </List>
            {dispute && (
                <>
                    <Divider />
                    <List dense disablePadding>
                        <ListItem>
                            <ListItemText>
                                <Typography color="text.primary" fontWeight={600} sx={{mb: 1}}>
                                    Reason of dispute
                                </Typography>
                                <Typography color="text.secondary">
                                    {disputeReasonDetails[dispute.details.reason]}
                                </Typography>
                            </ListItemText>
                        </ListItem>
                        <ListItem>
                            <ListItemText>
                                <Grid container alignItems="center" justifyContent="space-between" sx={{mb: 1}}>
                                    <Typography color="text.primary" fontWeight={600}>
                                        Dispute Status
                                    </Typography>
                                    <DisputeStatusChip status={dispute.status} variant="outlined" />
                                </Grid>
                                <Typography color="text.secondary">
                                    A new dispute case was opened and is currently under review. If we decide we have
                                    enough information, we will forward your dispute case to the card networks/merchant.
                                    If we decide that we need more information we will send you a notification.
                                </Typography>
                            </ListItemText>
                        </ListItem>
                        <ListItem>
                            <ListItemText>
                                <Typography color="text.primary" fontWeight={600} textAlign="center">
                                    To edit or withdraw the dispute please contact support +1 (888) 867-5309
                                </Typography>
                            </ListItemText>
                        </ListItem>
                    </List>
                </>
            )}
            {transaction.state === 'posted' &&
                (!transaction.disputeId || (dispute && ['closed', 'failed'].includes(dispute.status))) && (
                    <FileDispute {...transaction} />
                )}
        </>
    )
}

interface DisputeStatusChipProps extends ChipProps {
    status?: DisputeStatusAll
}

function DisputeStatusChip({status, ...props}: DisputeStatusChipProps) {
    switch (status) {
        case 'closed':
            return <Chip label="Closed" color="success" {...props} />
        case 'failed':
            return <Chip label="Error" color="error" {...props} />
        case 'lost':
            return <Chip label="Lost" color="error" {...props} />
        case 'prepared':
            return <Chip label="New" color="warning" {...props} />
        case 'submitted':
            return <Chip label="New" color="info" {...props} />
        case 'won':
            return <Chip label="Won" color="success" {...props} />
        case 'created':
            return <Chip label="New" color="info" {...props} />
        default:
            return <Chip label="Unknown status" color="error" {...props} />
    }
}
