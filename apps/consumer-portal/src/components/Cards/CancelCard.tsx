'use client'

import {
    <PERSON><PERSON><PERSON>ontent,
    <PERSON>alogTitle,
    <PERSON>rid,
    <PERSON><PERSON><PERSON>,
    Button,
    DialogActions,
    Popover,
    Paper,
    Box,
    Snackbar,
    Alert,
    AlertTitle,
} from '@mui/material'
import type {Card, Type} from '@/services/controllers/cards'
import Image from 'next/image'
import {useState, type SyntheticEvent} from 'react'
import {cancelCard} from '@/actions/cards'
import {useQueryClient} from '@tanstack/react-query'
interface CancelCardProps {
    card: Card | null
    onFinish: () => void
}

export default function CancelCard({card, onFinish}: CancelCardProps) {
    const queryClient = useQueryClient()
    const [hasError, setHasError] = useState<null | string>(null)
    const [callingCancel, setCallingCancel] = useState(false)
    const [popoverAnchor, setPopoverAnchor] = useState<HTMLButtonElement | null>(null)
    const confirmPopover = Boolean(popoverAnchor)

    function handleOpenPopover(evt: SyntheticEvent<HTMLButtonElement>) {
        setPopoverAnchor(evt.currentTarget)
    }

    function closePopover() {
        setPopoverAnchor(null)
    }

    function onClose() {
        onFinish()
    }

    async function handleCancelCard() {
        if (!card?.cardId) return

        setCallingCancel(true)
        try {
            const result = await cancelCard({
                cardId: card.cardId,
                lastFour: card.lastFour,
                state: card.state,
            })

            if (!result.success) {
                setHasError(result.error)
                return
            }

            queryClient.invalidateQueries({queryKey: ['cards']})
            onClose()
        } catch (error) {
            setHasError(
                error instanceof Error ? error.message : 'There was an issue canceling your card. Please try again',
            )
        } finally {
            setCallingCancel(false)
        }
    }

    const aspectRatio = 1.586
    const cardWidth = 81

    if (!card) return null

    return (
        <>
            <Grid container gap={2} alignContent="flex-start" flex={1}>
                <Grid container gap={2} flexDirection="row">
                    <Image
                        src={getCardAsset(card.type)}
                        alt="Card Art"
                        width={cardWidth}
                        height={cardWidth / aspectRatio}
                        style={{objectFit: 'fill'}}
                    />
                    <Grid flexDirection="column">
                        <Typography flex={1} fontSize={12} fontWeight={400} color="textSecondary" alignContent="center">
                            Card Number
                        </Typography>
                        <Typography fontSize={16} fontWeight={600} lineHeight="160%">
                            •••• •••• •••• {card.lastFour}
                        </Typography>
                    </Grid>
                </Grid>
                <Typography fontSize={16} color="textSecondary">
                    At &quot;Company Name&quot; we take extra steps to help keep your account secure; you&apos;ll get a
                    new card with a different card number,{' '}
                    <Box component="span" sx={{color: 'var(--mui-palette-warning-dark)'}}>
                        your old card will be deactivated and your subscriptions/autopay will need to be updated.
                    </Box>
                </Typography>
            </Grid>
            <DialogActions sx={{flexDirection: 'column'}}>
                <Button variant="contained" fullWidth color="error" onClick={handleOpenPopover} loading={callingCancel}>
                    Cancel My Card
                </Button>
                <Button variant="text" fullWidth color="primary" onClick={onClose}>
                    Keep My Card
                </Button>
                <Popover
                    open={confirmPopover}
                    anchorEl={popoverAnchor}
                    onClose={closePopover}
                    anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                    }}
                    transformOrigin={{
                        vertical: 'center',
                        horizontal: 'center',
                    }}
                    slotProps={{
                        paper: {
                            sx: {
                                maxWidth: 340,
                            },
                        },
                    }}
                >
                    <Paper>
                        <DialogTitle>
                            <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                                Cancel Card?
                            </Typography>
                        </DialogTitle>
                        <DialogContent>
                            <Typography fontSize={16} lineHeight="150%">
                                Cancelling your card will immediately prevent any new or recurring charges associated
                                with this card from being processed.
                            </Typography>
                        </DialogContent>
                        <DialogActions sx={{justifyContent: 'space-between', gap: 1}}>
                            <Button
                                variant="text"
                                color="primary"
                                onClick={() => {
                                    closePopover()
                                    onClose()
                                }}
                            >
                                Keep My Card
                            </Button>
                            <Button loading={callingCancel} variant="outlined" color="error" onClick={handleCancelCard}>
                                Cancel my card
                            </Button>
                        </DialogActions>
                    </Paper>
                </Popover>
            </DialogActions>
            {hasError && (
                <Snackbar
                    open={!!hasError}
                    autoHideDuration={5000}
                    onClose={() => setHasError(null)}
                    anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                >
                    <Alert
                        onClose={() => setHasError(null)}
                        severity="error"
                        variant="filled"
                        sx={{width: 350, backgroundColor: 'var(--mui-palette-error-main)'}}
                    >
                        <AlertTitle>Error</AlertTitle>
                        <Typography>{hasError}</Typography>
                    </Alert>
                </Snackbar>
            )}
        </>
    )
}

function getCardAsset(cardType: Type) {
    if (cardType === 'physical') {
        return '/assets/Tallied-Card-Physical.png'
    } else {
        return '/assets/Tallied-Card-Virtual.png'
    }
}
