import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen, waitFor} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import CancelCard from './CancelCard'
import {cancelCard} from '@/actions/cards'
import type {Card} from '@/services/controllers/cards'
import type {Result} from '@/actions/types'

// Mock the cancel card action
vi.mock('@/actions/cards', () => ({
    cancelCard: vi.fn(),
}))

// Mock the router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
    useRouter: () => ({push: mockPush}),
}))

describe('CancelCard', () => {
    const mockCard: Card = {
        cardId: 'card_123',
        creditAccountId: 'ca_123',
        type: 'physical',
        lastFour: '1234',
        expiration: {
            month: 12,
            year: 2025,
        },
        state: 'active',
        createdOn: new Date('2024-01-01'),
        updatedOn: new Date('2024-01-01'),
        token: '1234',
    }

    const successResult: Result<Card> = {
        success: true,
        data: {
            ...mockCard,
            state: 'canceled',
        },
    }

    const errorResult: Result<Card> = {
        success: false,
        error: 'Failed to cancel card',
    }

    beforeEach(() => {
        vi.clearAllMocks()
        vi.mocked(cancelCard).mockResolvedValue(successResult)
    })

    it('displays card information', () => {
        render(<CancelCard card={mockCard} onFinish={() => {}} />)

        expect(screen.getByText('•••• •••• •••• 1234')).toBeInTheDocument()
        expect(screen.getByText(/your old card will be deactivated/i)).toBeInTheDocument()
    })

    it('shows confirmation popover when cancel button is clicked', async () => {
        const user = userEvent.setup()
        render(<CancelCard card={mockCard} onFinish={() => {}} />)

        await user.click(screen.getByRole('button', {name: /cancel my card/i}))

        expect(screen.getByText(/cancel card\?/i)).toBeInTheDocument()
        expect(screen.getByText(/cancelling your card will immediately prevent/i)).toBeInTheDocument()
    })

    it('submits cancellation request with correct data', async () => {
        const user = userEvent.setup()
        const onFinish = vi.fn()
        render(<CancelCard card={mockCard} onFinish={onFinish} />)

        // Click the initial cancel button
        await user.click(screen.getByRole('button', {name: /cancel my card/i}))
        // Click the confirm cancel button in the popover
        await user.click(screen.getByRole('button', {name: /cancel my card/i}))

        expect(cancelCard).toHaveBeenCalledWith({
            cardId: mockCard.cardId,
            lastFour: mockCard.lastFour,
            state: mockCard.state,
        })
        expect(onFinish).toHaveBeenCalled()
    })

    it('closes popover when keep card is clicked', async () => {
        const user = userEvent.setup()
        const onFinish = vi.fn()
        render(<CancelCard card={mockCard} onFinish={onFinish} />)

        // Click the initial cancel button
        await user.click(screen.getByRole('button', {name: /cancel my card/i}))
        // Click keep card in popover
        await user.click(screen.getByRole('button', {name: /keep my card/i}))

        // Verify popover is closed
        expect(screen.queryByText(/cancel card\?/i)).not.toBeInTheDocument()
    })

    it('shows error message when cancellation fails', async () => {
        vi.mocked(cancelCard).mockResolvedValue(errorResult)

        const user = userEvent.setup()
        render(<CancelCard card={mockCard} onFinish={() => {}} />)

        // Click the initial cancel button
        await user.click(screen.getByRole('button', {name: /cancel my card/i}))
        // Click the confirm cancel button in the popover
        await user.click(screen.getByRole('button', {name: /cancel my card/i}))

        expect(await screen.findByText(/Failed to cancel card/i)).toBeInTheDocument()
    })

    it('handles loading state during cancellation', async () => {
        // Mock the cancelCard function to return a delayed promise
        vi.mocked(cancelCard).mockImplementation(
            () => new Promise(resolve => setTimeout(() => resolve(successResult), 100)),
        )

        const user = userEvent.setup()
        render(<CancelCard card={mockCard} onFinish={() => {}} />)

        // Click the initial cancel button to open the popover
        await user.click(screen.getByRole('button', {name: /cancel my card/i}))

        // Get the cancel button from the popover
        const cancelButton = screen.getByRole('button', {name: /cancel my card/i, pressed: undefined})
        await user.click(cancelButton)

        // Wait for loading state to be applied
        await waitFor(() => {
            expect(cancelButton).toBeDisabled()
        })

        // Wait for request to complete
        await waitFor(() => {
            expect(cancelButton).not.toBeDisabled()
        })
    })

    it('renders nothing if card is null', () => {
        const {container} = render(<CancelCard card={null} onFinish={() => {}} />)
        expect(container).toBeEmptyDOMElement()
    })

    it('calls onFinish when cancel button is clicked', async () => {
        const user = userEvent.setup()
        const onFinish = vi.fn()
        render(<CancelCard card={mockCard} onFinish={onFinish} />)

        await user.click(screen.getByRole('button', {name: /keep my card/i}))
        expect(onFinish).toHaveBeenCalled()
    })
})
