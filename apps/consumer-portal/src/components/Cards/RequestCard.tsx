'use client'

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Button,
    Snackbar,
    Alert,
    Box,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    TextField,
    IconButton,
    Icon,
    List,
    Card,
    ListItem,
    ListItemButton,
    type SelectChangeEvent,
} from '@mui/material'
import {useReducer} from 'react'
import {requestCard, type CardRequestOptions} from '@/actions/cards'
import {useUserData} from '@/hooks/useUserData'
import {useAuthorizedUsersData} from '@/hooks/useAuthorizedUserData'
import {stateNames} from '@/utils/enums'
import type {Address} from '@/services/controllers/cards'
import {useQueryClient} from '@tanstack/react-query'
import {AdapterDayjs} from '@mui/x-date-pickers/AdapterDayjs'
import {LocalizationProvider, DatePicker} from '@mui/x-date-pickers'
import dayjs from 'dayjs'
import {ToggleGroup} from '../ToggleGroup'
import {createAuthorizedUser} from '@/actions/authorized-users'
import SuccessAnimation from '@/components/SuccessAnimation'
import AmountWithCadenceInput, {type AmountWithCadenceValue} from '../AmountWithCadenceInput'

interface RequestCardProps {
    onFinish: () => void
}

// Possible values for who the card is for
type CardForOption = null | 'me' | 'new' | string // string = authorizedUserId (UUID)

// Helper functions for date validation and formatting
const isValidDate = (dateStr: string): boolean => {
    if (!dateStr.match(/^\d{2}\/\d{2}\/\d{4}$/)) return false

    const [month, day, year] = dateStr.split('/').map(Number)
    const date = new Date(year, month - 1, day)
    const today = new Date()

    // Check if date is valid and within allowed range
    return (
        date.getMonth() === month - 1 &&
        date.getDate() === day &&
        date.getFullYear() === year &&
        date <= today &&
        date.getFullYear() >= 1900
    )
}

// Helper function to format phone number to E.164 format
const formatPhoneToE164 = (phone: string): string => {
    // Strip all non-digit characters
    const digitsOnly = phone.replace(/\D/g, '')

    // Check if we have a valid US number (10 digits)
    if (digitsOnly.length === 10) {
        return `+1${digitsOnly}`
    }

    // If it already starts with a country code (11+ digits with 1 at the beginning)
    if (digitsOnly.length >= 11 && digitsOnly.startsWith('1')) {
        return `+${digitsOnly}`
    }

    // Return the original input if we can't format it
    // The validation will catch this later
    return phone
}

// Helper function to format phone for display (optional)
const formatPhoneForDisplay = (phone: string): string => {
    // Strip all non-digit characters
    const digitsOnly = phone.replace(/\D/g, '')

    // Format as (XXX) XXX-XXXX for 10-digit numbers
    if (digitsOnly.length === 10) {
        return `(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}`
    }

    // Return original digits if we can't format it
    return digitsOnly
}

// Define our state types
type RequestCardState = {
    // Card configuration
    card: {
        type: 'physical' | 'virtual'
        for: CardForOption
        nickname: string
    }

    // Shipping address (for physical cards)
    shippingAddress: {
        addressLine1: string
        addressLine2: string
        city: string
        state: string
        zip: string
    }

    // New authorized user data
    authorizedUser: {
        firstName: string
        middleName: string
        lastName: string
        dob: string
        ssn: string
        email: string
        phoneNumber: string
        spendLimit: {
            amount: number | null
            cadence: 'monthly' | 'weekly' | 'daily'
        }
        errors: {
            dob: string
            ssn: string
            email: string
            phoneNumber: string
        }
    }

    // UI state
    ui: {
        isSubmitting: boolean
        error: string | null
        showSuccess: boolean
    }
}

// Define our action types
type RequestCardAction =
    | {type: 'SET_CARD_TYPE'; payload: 'physical' | 'virtual'}
    | {type: 'SET_CARD_FOR'; payload: CardForOption}
    | {type: 'SET_CARD_NICKNAME'; payload: string}
    | {type: 'UPDATE_SHIPPING_ADDRESS'; payload: {field: string; value: string}}
    | {type: 'UPDATE_AUTHORIZED_USER'; payload: {field: string; value: string}}
    | {type: 'UPDATE_AUTHORIZED_USER_ERROR'; payload: {field: string; error: string}}
    | {type: 'UPDATE_SPEND_LIMIT'; payload: {field: 'amount' | 'cadence'; value: string | number | null}}
    | {type: 'SET_SUBMITTING'; payload: boolean}
    | {type: 'SET_ERROR'; payload: string | null}
    | {type: 'SET_SUCCESS'}

// Create our reducer function
function requestCardReducer(state: RequestCardState, action: RequestCardAction): RequestCardState {
    switch (action.type) {
        case 'SET_CARD_TYPE':
            return {
                ...state,
                card: {
                    ...state.card,
                    type: action.payload,
                },
            }

        case 'SET_CARD_FOR':
            return {
                ...state,
                card: {
                    ...state.card,
                    for: action.payload,
                },
            }

        case 'SET_CARD_NICKNAME':
            return {
                ...state,
                card: {
                    ...state.card,
                    nickname: action.payload,
                },
            }

        case 'UPDATE_SHIPPING_ADDRESS':
            return {
                ...state,
                shippingAddress: {
                    ...state.shippingAddress,
                    [action.payload.field]: action.payload.value,
                },
            }

        case 'UPDATE_AUTHORIZED_USER':
            return {
                ...state,
                authorizedUser: {
                    ...state.authorizedUser,
                    [action.payload.field]: action.payload.value,
                    errors: {
                        ...state.authorizedUser.errors,
                        [action.payload.field]: '', // Clear error when field is updated
                    },
                },
            }

        case 'UPDATE_AUTHORIZED_USER_ERROR':
            return {
                ...state,
                authorizedUser: {
                    ...state.authorizedUser,
                    errors: {
                        ...state.authorizedUser.errors,
                        [action.payload.field]: action.payload.error,
                    },
                },
            }

        case 'UPDATE_SPEND_LIMIT':
            return {
                ...state,
                authorizedUser: {
                    ...state.authorizedUser,
                    spendLimit: {
                        ...state.authorizedUser.spendLimit,
                        [action.payload.field]: action.payload.value,
                    },
                },
            }

        case 'SET_SUBMITTING':
            return {
                ...state,
                ui: {
                    ...state.ui,
                    isSubmitting: action.payload,
                },
            }

        case 'SET_ERROR':
            return {
                ...state,
                ui: {
                    ...state.ui,
                    error: action.payload,
                },
            }

        case 'SET_SUCCESS':
            return {
                ...state,
                ui: {
                    ...state.ui,
                    showSuccess: true,
                    isSubmitting: false,
                },
            }

        default:
            return state
    }
}

export default function RequestCard({onFinish}: RequestCardProps) {
    const queryClient = useQueryClient()
    const {data: shippingDefaults} = useUserData({select: data => data.primaryAddress})
    const {data: authorizedUsers} = useAuthorizedUsersData({
        select: data => data.authorizedUsers?.filter(user => user.status === 'active'),
    })

    // Initial state
    const initialState: RequestCardState = {
        card: {
            type: 'virtual',
            for: null,
            nickname: '',
        },
        shippingAddress: {
            addressLine1: shippingDefaults?.addressLine1 || '',
            addressLine2: shippingDefaults?.addressLine2 || '',
            city: shippingDefaults?.locality || '',
            state: shippingDefaults?.administrativeArea || '',
            zip: shippingDefaults?.postalCode || '',
        },
        authorizedUser: {
            firstName: '',
            middleName: '',
            lastName: '',
            dob: '',
            ssn: '',
            email: '',
            phoneNumber: '',
            spendLimit: {
                amount: null,
                cadence: 'monthly',
            },
            errors: {
                dob: '',
                ssn: '',
                email: '',
                phoneNumber: '',
            },
        },
        ui: {
            isSubmitting: false,
            error: null,
            showSuccess: false,
        },
    }

    const [state, dispatch] = useReducer(requestCardReducer, initialState)

    // Handler functions
    function onClose() {
        onFinish()
    }

    function handleCardTypeChange(value: string) {
        dispatch({
            type: 'SET_CARD_TYPE',
            payload: value as 'physical' | 'virtual',
        })
    }

    function handleCardForSelection(selectedOption: CardForOption) {
        dispatch({
            type: 'SET_CARD_FOR',
            payload: selectedOption,
        })
    }

    function handleNicknameChange(e: React.ChangeEvent<HTMLInputElement>) {
        dispatch({
            type: 'SET_CARD_NICKNAME',
            payload: e.target.value,
        })
    }

    function handleAddressChange(field: string, e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) {
        dispatch({
            type: 'UPDATE_SHIPPING_ADDRESS',
            payload: {field, value: e.target.value},
        })
    }

    function handleAuthorizedUserChange(field: string, value: string) {
        dispatch({
            type: 'UPDATE_AUTHORIZED_USER',
            payload: {field, value},
        })
    }

    function handleSpendLimitChange(field: 'amount' | 'cadence', value: string) {
        dispatch({
            type: 'UPDATE_SPEND_LIMIT',
            payload: {field, value},
        })
    }

    // Handle spend amount input with validation
    const handleSpendAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value

        // If the value is empty or "0", keep it as is (represents no limit)
        if (value === '' || value === '0') {
            handleSpendLimitChange('amount', value)
            return
        }

        // If it's a negative number or not a number, set to empty
        const numValue = parseFloat(value)
        if (isNaN(numValue) || numValue < 0) {
            handleSpendLimitChange('amount', '')
            return
        }

        // Otherwise set the valid value
        handleSpendLimitChange('amount', value)
    }

    // Handle date selection from DatePicker
    const handleDateChange = (date: dayjs.Dayjs | string | null) => {
        if (typeof date === 'string') {
            date = dayjs(date)
        }

        if (!date) {
            dispatch({
                type: 'UPDATE_AUTHORIZED_USER',
                payload: {field: 'dob', value: ''},
            })
            dispatch({
                type: 'UPDATE_AUTHORIZED_USER_ERROR',
                payload: {field: 'dob', error: 'Date of birth is required'},
            })
            return
        }

        // Format to MM/DD/YYYY for internal state and validation
        const formattedDate = date.format('MM/DD/YYYY')

        // Check if date is valid and within allowed range
        const isValid = isValidDate(formattedDate)

        dispatch({
            type: 'UPDATE_AUTHORIZED_USER',
            payload: {field: 'dob', value: formattedDate},
        })

        if (!isValid) {
            dispatch({
                type: 'UPDATE_AUTHORIZED_USER_ERROR',
                payload: {field: 'dob', error: 'Please select a valid date'},
            })
        }
    }

    // Handle SSN input
    const handleSsnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value.replace(/\D/g, '')
        if (value.length > 9) value = value.slice(0, 9)

        dispatch({
            type: 'UPDATE_AUTHORIZED_USER',
            payload: {field: 'ssn', value},
        })

        if (value.length !== 9) {
            dispatch({
                type: 'UPDATE_AUTHORIZED_USER_ERROR',
                payload: {field: 'ssn', error: 'SSN must be 9 digits'},
            })
        }
    }

    // Email validation
    const validateEmail = (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        const isValid = emailRegex.test(email)

        if (email && !isValid) {
            dispatch({
                type: 'UPDATE_AUTHORIZED_USER_ERROR',
                payload: {field: 'email', error: 'Please enter a valid email address'},
            })
        }

        return isValid
    }

    // Handle phone number input
    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value

        // Store display format for the input field
        const displayValue = formatPhoneForDisplay(inputValue)

        // Validate if we have a complete phone number to check
        let errorMessage = ''
        if (displayValue && displayValue.replace(/\D/g, '').length !== 10) {
            errorMessage = 'Please enter a valid 10-digit US phone number'
        }

        dispatch({
            type: 'UPDATE_AUTHORIZED_USER',
            payload: {field: 'phoneNumber', value: displayValue},
        })

        if (errorMessage) {
            dispatch({
                type: 'UPDATE_AUTHORIZED_USER_ERROR',
                payload: {field: 'phoneNumber', error: errorMessage},
            })
        }
    }

    // Get recipient name based on cardFor option
    const getRecipientName = (): string => {
        const cardFor = state.card.for

        if (cardFor === 'me') {
            return 'Your'
        } else if (cardFor === 'new') {
            return `${state.authorizedUser.firstName}'s`
        } else if (cardFor && typeof cardFor === 'string') {
            const user = authorizedUsers?.find(u => u.authorizedUserId === cardFor)
            return user ? `${user.firstName}'s` : 'The'
        }
        return 'The'
    }

    async function handleRequestCard() {
        dispatch({type: 'SET_SUBMITTING', payload: true})

        try {
            // Validate card recipient selection
            if (state.card.for === null) {
                dispatch({type: 'SET_ERROR', payload: 'Please select who this card is for'})
                return
            }

            // For new authorized users, validate required fields
            if (state.card.for === 'new') {
                const {firstName, lastName, dob, ssn, email, phoneNumber} = state.authorizedUser

                // Validate all required fields are present
                if (!firstName || !lastName || !dob || !ssn || !email) {
                    dispatch({type: 'SET_ERROR', payload: 'Please fill in all required fields for the authorized user'})
                    return
                }

                // Validate DOB
                if (!isValidDate(dob)) {
                    dispatch({
                        type: 'UPDATE_AUTHORIZED_USER_ERROR',
                        payload: {field: 'dob', error: 'Please enter a valid date (MM/DD/YYYY)'},
                    })
                    dispatch({type: 'SET_ERROR', payload: 'Please enter a valid date of birth'})
                    return
                }

                // Validate SSN (9 digits)
                if (ssn.length !== 9) {
                    dispatch({
                        type: 'UPDATE_AUTHORIZED_USER_ERROR',
                        payload: {field: 'ssn', error: 'SSN must be 9 digits'},
                    })
                    dispatch({type: 'SET_ERROR', payload: 'Please enter a valid 9-digit SSN'})
                    return
                }

                // Validate email
                if (!validateEmail(email)) {
                    dispatch({type: 'SET_ERROR', payload: 'Please enter a valid email address'})
                    return
                }

                // Validate phone if provided
                if (phoneNumber && phoneNumber.replace(/\D/g, '').length !== 10) {
                    dispatch({
                        type: 'UPDATE_AUTHORIZED_USER_ERROR',
                        payload: {field: 'phoneNumber', error: 'Please enter a valid 10-digit US phone number'},
                    })
                    dispatch({type: 'SET_ERROR', payload: 'Please enter a valid phone number'})
                    return
                }
            }

            // Validate address for physical cards
            if (
                state.card.type === 'physical' &&
                (!state.shippingAddress.addressLine1 ||
                    !state.shippingAddress.zip ||
                    !state.shippingAddress.city ||
                    !state.shippingAddress.state)
            ) {
                dispatch({type: 'SET_ERROR', payload: 'Address is required for physical cards.'})
                return
            }

            let authorizedUserId = state.card.for

            // Handle "new" authorized user flow
            if (state.card.for === 'new') {
                // Format phone to E.164 before sending to API
                const formattedPhone = state.authorizedUser.phoneNumber
                    ? formatPhoneToE164(state.authorizedUser.phoneNumber)
                    : ''

                const newAuthorizedUserResult = await createAuthorizedUser({
                    firstName: state.authorizedUser.firstName,
                    lastName: state.authorizedUser.lastName,
                    dob: state.authorizedUser.dob,
                    ssn: state.authorizedUser.ssn,
                    email: state.authorizedUser.email,
                    phoneNumber: formattedPhone || undefined,
                })

                if (!newAuthorizedUserResult.success) {
                    dispatch({type: 'SET_ERROR', payload: newAuthorizedUserResult.error})
                    return
                }

                queryClient.invalidateQueries({queryKey: ['authorizedUsers']})

                authorizedUserId = newAuthorizedUserResult.data.authorizedUserId
            }

            // Create the shipping address object if this is a physical card
            const shippingAddress: Address | undefined =
                state.card.type === 'physical'
                    ? {
                          addressLine1: state.shippingAddress.addressLine1,
                          addressLine2: state.shippingAddress.addressLine2,
                          postalCode: state.shippingAddress.zip,
                          locality: state.shippingAddress.city,
                          administrativeArea: state.shippingAddress.state,
                          country: 'USA',
                      }
                    : undefined

            // Determine if this is for an authorized user (not 'me')
            const isForAuthorizedUser = state.card.for !== 'me'

            // Build base request options based on card type
            const requestOptions: CardRequestOptions =
                state.card.type === 'physical'
                    ? {
                          type: 'physical',
                          shipping: shippingAddress!,
                      }
                    : {
                          type: 'virtual',
                      }

            // Add authorized user and spend controls if applicable
            if (isForAuthorizedUser) {
                if (!authorizedUserId) {
                    dispatch({type: 'SET_ERROR', payload: 'Invalid authorized user selected'})
                    return
                }

                Object.assign(requestOptions, {
                    authorizedUserId,
                    spendControls: state.authorizedUser.spendLimit.amount
                        ? [
                              {
                                  amount: state.authorizedUser.spendLimit.amount,
                                  cadence: state.authorizedUser.spendLimit.cadence,
                              },
                          ]
                        : undefined,
                })
            }

            // Make the request
            const result = await requestCard(requestOptions)
            if (!result.success) {
                dispatch({type: 'SET_ERROR', payload: result.error})
                return
            }

            queryClient.invalidateQueries({queryKey: ['cards']})

            // Show success state
            dispatch({type: 'SET_SUCCESS'})
        } catch (error) {
            dispatch({
                type: 'SET_ERROR',
                payload:
                    error instanceof Error ? error.message : 'There was an issue requesting a card. Please try again',
            })
        } finally {
            dispatch({type: 'SET_SUBMITTING', payload: false})
        }
    }

    function handleStateChange(e: SelectChangeEvent<string>) {
        dispatch({
            type: 'UPDATE_SHIPPING_ADDRESS',
            payload: {field: 'state', value: e.target.value},
        })
    }

    return (
        <>
            <Grid container flexDirection="column" flex={1} gap={2}>
                <Grid container sx={{pb: 2}}>
                    <Typography variant="h6" flex={1}>
                        {state.ui.showSuccess ? 'New Card Added!' : 'Request New Card'}
                    </Typography>
                    <IconButton
                        sx={{position: 'absolute', right: 8, top: 8}}
                        onClick={onClose}
                        aria-label="Close Request Card form"
                    >
                        <Icon baseClassName="fas" className="fa-solid fa-xmark" />
                    </IconButton>
                </Grid>

                {state.ui.showSuccess ? (
                    // Success screen
                    <Grid container flexDirection="column" gap={3} flex={1} justifyContent="space-between">
                        <Grid container flexDirection="column" gap={3} alignItems="center">
                            <SuccessAnimation />
                            <Typography fontSize={16} fontWeight={400} textAlign="center" sx={{px: 2}}>
                                {state.card.type === 'physical' ? (
                                    <>
                                        {getRecipientName()} new physical card has been added. It is on the way and will
                                        be delivered to the address that you provided
                                        {state.card.for !== 'me' && ` for ${getRecipientName().replace("'s", '')}`} in
                                        7-10 business days.
                                    </>
                                ) : (
                                    <>
                                        {getRecipientName()} new virtual card
                                        {state.card.nickname && `, with the nickname of '${state.card.nickname}',`} has
                                        been added. You may begin using this card right away!
                                    </>
                                )}
                            </Typography>
                        </Grid>
                        <Button
                            variant="contained"
                            fullWidth
                            color="primary"
                            startIcon={
                                <Icon baseClassName="fas" className="fa-solid fa-chevron-left" fontSize="small" />
                            }
                            onClick={onClose}
                        >
                            BACK TO MY CARDS
                        </Button>
                    </Grid>
                ) : state.card.for === null ? (
                    <WhoIsTheCardFor onSelect={handleCardForSelection} selectedOption={state.card.for} />
                ) : (
                    <Grid container flexDirection="column" gap={3} flex={1}>
                        {/* Show a confirmation message based on selection */}
                        <Typography fontSize={16} fontWeight={600} lineHeight="150%" color="textSecondary">
                            {state.card.for === 'me'
                                ? 'This card is for me.'
                                : state.card.for === 'new'
                                  ? 'This card is for someone else.'
                                  : `This card is for ${authorizedUsers?.find(u => u.authorizedUserId === state.card.for)?.firstName || ''} ${authorizedUsers?.find(u => u.authorizedUserId === state.card.for)?.lastName || ''}.`}
                        </Typography>

                        {/* New Authorized User Form */}
                        {state.card.for === 'new' && (
                            <Box sx={{display: 'flex', flexDirection: 'column', gap: 3}}>
                                <Typography fontSize={16} fontWeight={700} color="textSecondary">
                                    Cardholder Information
                                </Typography>

                                {/* Name fields */}
                                <Box sx={{display: 'flex', flexDirection: {xs: 'column', sm: 'row'}, gap: 1}}>
                                    <TextField
                                        label="First Name"
                                        fullWidth
                                        value={state.authorizedUser.firstName}
                                        onChange={e => handleAuthorizedUserChange('firstName', e.target.value)}
                                        slotProps={{
                                            inputLabel: {
                                                shrink: true,
                                            },
                                            htmlInput: {
                                                autoComplete: 'off',
                                            },
                                        }}
                                    />
                                    <TextField
                                        label="Last Name"
                                        fullWidth
                                        value={state.authorizedUser.lastName}
                                        onChange={e => handleAuthorizedUserChange('lastName', e.target.value)}
                                        slotProps={{
                                            inputLabel: {
                                                shrink: true,
                                            },
                                            htmlInput: {
                                                autoComplete: 'off',
                                            },
                                        }}
                                    />
                                </Box>

                                {/* DOB and SSN */}
                                <Box sx={{display: 'flex', flexDirection: {xs: 'column', sm: 'row'}, gap: 1}}>
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            label="Date of Birth"
                                            value={
                                                state.authorizedUser.dob
                                                    ? dayjs(state.authorizedUser.dob, 'MM/DD/YYYY')
                                                    : null
                                            }
                                            onChange={handleDateChange}
                                            format="MM/DD/YYYY"
                                            slotProps={{
                                                textField: {
                                                    fullWidth: true,
                                                    error: !!state.authorizedUser.errors.dob,
                                                    helperText: state.authorizedUser.errors.dob,
                                                    sx: {flex: 1},
                                                    InputLabelProps: {
                                                        shrink: true,
                                                    },
                                                    InputProps: {
                                                        autoComplete: 'off',
                                                    },
                                                },
                                            }}
                                            minDate={dayjs('1900-01-01')}
                                            maxDate={dayjs()}
                                        />
                                    </LocalizationProvider>
                                    <TextField
                                        label="Social Security Number"
                                        fullWidth
                                        value={state.authorizedUser.ssn}
                                        onChange={handleSsnChange}
                                        placeholder="XXX-XX-XXXX"
                                        error={!!state.authorizedUser.errors.ssn}
                                        helperText={state.authorizedUser.errors.ssn}
                                        type="password"
                                        slotProps={{
                                            inputLabel: {
                                                shrink: true,
                                            },
                                            htmlInput: {
                                                maxLength: 9,
                                                autoComplete: 'off',
                                            },
                                        }}
                                        sx={{flex: 1}}
                                    />
                                </Box>

                                {/* Email */}
                                <TextField
                                    label="Email Address"
                                    fullWidth
                                    value={state.authorizedUser.email}
                                    onChange={e => {
                                        handleAuthorizedUserChange('email', e.target.value)
                                        if (e.target.value) validateEmail(e.target.value)
                                    }}
                                    type="email"
                                    error={!!state.authorizedUser.errors.email}
                                    helperText={state.authorizedUser.errors.email}
                                    slotProps={{
                                        inputLabel: {
                                            shrink: true,
                                        },
                                        htmlInput: {
                                            autoComplete: 'off',
                                        },
                                    }}
                                />

                                {/* Phone */}
                                <TextField
                                    label="Phone Number (optional)"
                                    fullWidth
                                    value={state.authorizedUser.phoneNumber}
                                    onChange={handlePhoneChange}
                                    placeholder="(*************"
                                    error={!!state.authorizedUser.errors.phoneNumber}
                                    helperText={state.authorizedUser.errors.phoneNumber}
                                    slotProps={{
                                        inputLabel: {
                                            shrink: true,
                                        },
                                        htmlInput: {
                                            autoComplete: 'off',
                                        },
                                    }}
                                />
                            </Box>
                        )}

                        {/* Card Type Selection */}
                        <Grid container alignItems="flex-start" sx={{height: 40}}>
                            <ToggleGroup
                                value={state.card.type}
                                onChange={handleCardTypeChange}
                                options={[
                                    {value: 'virtual', label: 'Virtual Card'},
                                    {value: 'physical', label: 'Physical Card'},
                                ]}
                                color="secondary"
                                fullWidth
                            />
                        </Grid>

                        {/* Spend Controls - Show for any authorized user (not 'me') */}
                        {state.card.for !== 'me' && (
                            <Grid container flexDirection="column" alignItems="flex-start" gap={3} alignSelf="stretch">
                                <Typography fontSize={16} fontWeight={700} lineHeight="150%" color="textSecondary">
                                    Spend Limit (optional)
                                </Typography>
                                <AmountWithCadenceInput
                                    value={state.authorizedUser.spendLimit}
                                    onChange={(newValue: AmountWithCadenceValue) => {
                                        dispatch({
                                            type: 'UPDATE_SPEND_LIMIT',
                                            payload: {field: 'amount', value: newValue.amount},
                                        })
                                        dispatch({
                                            type: 'UPDATE_SPEND_LIMIT',
                                            payload: {field: 'cadence', value: newValue.cadence},
                                        })
                                    }}
                                />
                            </Grid>
                        )}

                        {/* Nickname Field */}
                        <Grid container flexDirection="column" alignItems="flex-start" gap={2} alignSelf="stretch">
                            <Typography fontSize={16} fontWeight={700} lineHeight="150%" color="textSecondary">
                                Would you like to give your card a nickname?
                            </Typography>
                            <TextField
                                label="Nickname (optional)"
                                fullWidth
                                value={state.card.nickname}
                                onChange={handleNicknameChange}
                                slotProps={{inputLabel: {shrink: true}, htmlInput: {autoComplete: 'off'}}}
                            />
                        </Grid>

                        {/* Physical Card Address (only shown if physical selected) */}
                        {state.card.type === 'physical' && (
                            <Grid container flexDirection="column" alignItems="flex-start" gap={2} alignSelf="stretch">
                                <Typography fontSize={16} fontWeight={700} lineHeight="30px" color="textSecondary">
                                    Delivery Address
                                </Typography>
                                <Grid container flexDirection="column" alignItems="flex-start" gap={3}>
                                    <TextField
                                        label="Street Address"
                                        fullWidth
                                        slotProps={{inputLabel: {shrink: true}}}
                                        value={state.shippingAddress.addressLine1}
                                        onChange={e => handleAddressChange('addressLine1', e)}
                                    />
                                    <Grid
                                        container
                                        flexDirection="row"
                                        gap={3}
                                        alignItems="flex-start"
                                        alignSelf="stretch"
                                    >
                                        <TextField
                                            label="Apt, Ste, Fl"
                                            fullWidth
                                            sx={{flex: 1}}
                                            slotProps={{inputLabel: {shrink: true}}}
                                            value={state.shippingAddress.addressLine2}
                                            onChange={e => handleAddressChange('addressLine2', e)}
                                        />
                                        <TextField
                                            label="Zip Code"
                                            fullWidth
                                            sx={{flex: 1}}
                                            slotProps={{inputLabel: {shrink: true}}}
                                            value={state.shippingAddress.zip}
                                            onChange={e => handleAddressChange('zip', e)}
                                        />
                                    </Grid>
                                    <Grid
                                        container
                                        flexDirection="row"
                                        gap={3}
                                        alignItems="flex-start"
                                        alignSelf="stretch"
                                        flex={1}
                                    >
                                        <TextField
                                            label="City"
                                            fullWidth
                                            sx={{flex: 1}}
                                            slotProps={{inputLabel: {shrink: true}}}
                                            value={state.shippingAddress.city}
                                            onChange={e => handleAddressChange('city', e)}
                                        />
                                        <FormControl fullWidth sx={{flex: 1}}>
                                            <InputLabel id="state-input-label">State</InputLabel>
                                            <Select
                                                label="State"
                                                labelId="state-input-label"
                                                value={state.shippingAddress.state}
                                                onChange={handleStateChange}
                                            >
                                                {stateNames.map(state => (
                                                    <MenuItem key={state} value={state}>
                                                        {state}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                </Grid>
                            </Grid>
                        )}

                        <Grid container flex={1} />

                        {/* Action Buttons */}
                        <Grid container justifyContent="flex-end" alignItems="flex-start" gap={3} alignSelf="stretch">
                            <Button
                                variant="contained"
                                loading={state.ui.isSubmitting}
                                fullWidth
                                color="primary"
                                onClick={handleRequestCard}
                            >
                                Request New Card
                            </Button>
                            <Button variant="text" fullWidth onClick={onClose}>
                                Cancel
                            </Button>
                        </Grid>
                    </Grid>
                )}
            </Grid>
            <Snackbar
                open={!!state.ui.error}
                anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                autoHideDuration={3000}
                onClose={() => dispatch({type: 'SET_ERROR', payload: null})}
            >
                <Alert severity="error">{state.ui.error}</Alert>
            </Snackbar>
        </>
    )
}

interface WhoIsTheCardForProps {
    onSelect: (option: CardForOption) => void
    selectedOption: CardForOption
}

function WhoIsTheCardFor({onSelect, selectedOption}: WhoIsTheCardForProps) {
    const {data: authorizedUsers} = useAuthorizedUsersData({
        select: data => data.authorizedUsers?.filter(user => user.status === 'active'),
    })

    return (
        <Grid container flexDirection="column" gap={1.5}>
            <Typography fontSize={16} color="textSecondary" fontWeight={600} lineHeight="150%">
                Who is the card for?
            </Typography>
            <List sx={{gap: 1.5, display: 'flex', flexDirection: 'column'}}>
                <ListItem disablePadding>
                    <Card
                        sx={{
                            width: '100%',
                            borderRadius: 2,
                            border:
                                selectedOption === 'me'
                                    ? '2px solid var(--mui-palette-primary-main)'
                                    : '1px solid var(--mui-palette-divider)',
                        }}
                    >
                        <ListItemButton onClick={() => onSelect('me')}>
                            <Typography fontSize={16} fontWeight={400} lineHeight="143%" color="textPrimary" flex={1}>
                                It&apos;s for me
                            </Typography>
                            <Icon baseClassName="fas" className="fa-solid fa-chevron-right" fontSize="inherit" />
                        </ListItemButton>
                    </Card>
                </ListItem>
                {authorizedUsers?.map(user => (
                    <ListItem disablePadding key={user.authorizedUserId}>
                        <Card
                            sx={{
                                width: '100%',
                                borderRadius: 2,
                                border:
                                    selectedOption === user.authorizedUserId
                                        ? '2px solid var(--mui-palette-primary-main)'
                                        : '1px solid var(--mui-palette-divider)',
                            }}
                        >
                            <ListItemButton onClick={() => onSelect(user.authorizedUserId)}>
                                <Typography
                                    fontSize={16}
                                    fontWeight={400}
                                    lineHeight="143%"
                                    color="textPrimary"
                                    flex={1}
                                >
                                    It&apos;s for {user.firstName} {user.lastName}
                                </Typography>
                                <Icon baseClassName="fas" className="fa-solid fa-chevron-right" fontSize="inherit" />
                            </ListItemButton>
                        </Card>
                    </ListItem>
                ))}
                <ListItem disablePadding>
                    <Card
                        sx={{
                            width: '100%',
                            borderRadius: 2,
                            border:
                                selectedOption === 'new'
                                    ? '2px solid var(--mui-palette-primary-main)'
                                    : '1px solid var(--mui-palette-divider)',
                        }}
                    >
                        <ListItemButton onClick={() => onSelect('new')}>
                            <Typography fontSize={16} fontWeight={400} lineHeight="143%" color="textPrimary" flex={1}>
                                It&apos;s for someone else
                            </Typography>
                            <Icon baseClassName="fas" className="fa-solid fa-chevron-right" fontSize="inherit" />
                        </ListItemButton>
                    </Card>
                </ListItem>
            </List>
        </Grid>
    )
}
