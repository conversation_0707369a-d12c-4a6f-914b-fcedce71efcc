'use client'

import {
    Typo<PERSON>,
    Dialog,
    DialogActions,
    DialogTitle,
    DialogContent,
    Box,
    InputLabel,
    OutlinedInput,
    Grid,
    Snackbar,
    Alert,
    AlertT<PERSON>le,
    <PERSON>ton,
} from '@mui/material'
import {useEffect, useRef, useState} from 'react'
import type {KeyboardEvent, ChangeEvent, ClipboardEvent, FormEvent} from 'react'
import {useBasisTheory, TextElement} from '@basis-theory/basis-theory-react'
import type {TextElement as TextElementType} from '@basis-theory/basis-theory-react/types'
import type {Card} from '@/services/controllers/cards'
import {activateCard} from '@/actions/cards'
import {useQueryClient} from '@tanstack/react-query'

interface ActivateCardProps {
    cardId?: Card['cardId']
    onClose: () => void
}

export default function ActivateCard({cardId, onClose}: ActivateCardProps) {
    const queryClient = useQueryClient()
    const [activationPending, setActivationPending] = useState(false)
    const [activationButtonEnabled, setActivationButtonEnabled] = useState(false)
    const [btSessionKey, setBTSessionKey] = useState<string | null>(null)
    const [activateResult, setActivateResult] = useState<'success' | 'error' | 'warning' | null>(null)

    const [lastFourCard, setLastFourCard] = useState(['', '', '', ''])
    const [lastFourTaxId, setLastFourTaxId] = useState(['', '', '', ''])
    const [cvv, setCvv] = useState('')

    const cardInputRefs = [
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
    ]
    const taxIdInputRefs = [
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
        useRef<HTMLInputElement>(null),
    ]

    // Basis Theory
    const {bt} = useBasisTheory('API_KEY', {elements: true})
    const btLast4Ref = useRef<TextElementType>(null)
    const btSSNRef = useRef<TextElementType>(null)
    const btCVCRef = useRef<TextElementType>(null)

    useEffect(() => {
        // Kick async thing to start the BT session and grab session key
        async function openBTSession() {
            await sleep(1000)
            setBTSessionKey('1234')

            setActivationButtonEnabled(true)
        }

        if (cardId) {
            openBTSession()
        }

        return () => {
            setBTSessionKey(null)
        }
    }, [cardId])

    const handleDigitChange = (
        index: number,
        value: string,
        setter: React.Dispatch<React.SetStateAction<string[]>>,
        refs: React.RefObject<HTMLInputElement>[],
    ) => {
        // Replace any non number with a random unicode character and then noop the digit change fn.
        // This stops non numbers from being entered (as input attr pattern doesn't actually work), but allows backspace and null strings
        const cleanValue = value.replace(/\D/g, 'ⶨ')
        if (cleanValue === 'ⶨ') return

        setter(prev => {
            const newValue = [...prev]
            newValue[index] = value
            return newValue
        })

        if (value && index < 3) {
            refs[index + 1].current?.focus()
        }
    }

    const handleCVVChange = (evt: ChangeEvent<HTMLInputElement>) => {
        setCvv(evt.currentTarget.value.replace(/\D/g, ''))
    }

    const handleKeyDown = (
        index: number,
        e: KeyboardEvent<HTMLInputElement>,
        refs: React.RefObject<HTMLInputElement>[],
    ) => {
        if (e.key === 'Backspace' && index > 0 && !e.currentTarget.value) {
            refs[index - 1].current?.focus()
        }
    }

    const handlePaste = (
        e: ClipboardEvent<HTMLInputElement>,
        setter: React.Dispatch<React.SetStateAction<string[]>>,
        refs: React.RefObject<HTMLInputElement>[],
    ) => {
        e.preventDefault()
        const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 4)
        const newValue = pastedData.split('').concat(Array(4 - pastedData.length).fill(''))
        setter(newValue)
        refs[Math.min(pastedData.length, 3)].current?.focus()
    }

    const handleActivate = async (evt: FormEvent) => {
        evt.preventDefault()

        if (!cardId) return

        // if (!btSessionKey) return // The activate button isn't even enabled until the key is set, but this is just another catch

        const last4Element = btLast4Ref.current
        const ssnElement = btSSNRef.current
        const cvcElement = btCVCRef.current

        setActivationPending(true)
        const result = await activateCard({cardId, lastFour: lastFourCard.join('')})

        if (!result.success) {
            console.error(result.error)
            setActivateResult('error')
            setActivationPending(false)
            return
        }

        queryClient.invalidateQueries({queryKey: ['cards']})

        if (result.data.state === 'active') {
            setActivateResult('success')
            setActivationPending(false)
            handleCloseActivationModal()
        } else {
            setActivateResult('error')
            setActivationPending(false)
        }
    }

    function handleCloseActivationModal() {
        // Clear the session key out of state
        setActivationButtonEnabled(false)
        onClose()
    }

    function closeToast() {
        setActivateResult(null)
    }

    return (
        <>
            <Dialog open={!!cardId} onClose={handleCloseActivationModal} maxWidth="sm" fullWidth>
                <form onSubmit={handleActivate}>
                    <DialogTitle>Activate Card</DialogTitle>
                    <DialogContent>
                        {activateResult === 'error' ? (
                            <Alert severity="error" variant="filled" sx={{mb: 1}}>
                                <AlertTitle>Activation Failed</AlertTitle>
                                Please try again
                            </Alert>
                        ) : null}
                        <Grid container flexDirection="column" gap={2} sx={{width: '100%', flex: 1}}>
                            <Grid flex={1} data-testid="card-number-section">
                                <InputLabel htmlFor="lastFour" sx={{mb: 1}}>
                                    Last 4 number of your card
                                </InputLabel>
                                <Box display="flex" justifyContent="space-between" sx={{maxWidth: '80%', m: '0 auto'}}>
                                    {lastFourCard.map((digit, index) => (
                                        <SingleDigitInput
                                            key={`card-${index}`}
                                            value={digit}
                                            onChange={evt =>
                                                handleDigitChange(
                                                    index,
                                                    evt.currentTarget.value,
                                                    setLastFourCard,
                                                    cardInputRefs,
                                                )
                                            }
                                            onKeyDown={evt => handleKeyDown(index, evt, cardInputRefs)}
                                            onPaste={evt => handlePaste(evt, setLastFourCard, cardInputRefs)}
                                            inputRef={cardInputRefs[index]}
                                            data-testid={`card-digit-${index}`}
                                        />
                                    ))}
                                </Box>
                            </Grid>
                            <Grid flex={1} data-testid="tax-id-section">
                                <InputLabel htmlFor="tin" sx={{mb: 1}}>
                                    Tax ID / SSN (last 4 digits)
                                </InputLabel>
                                <Box display="flex" justifyContent="space-between" sx={{maxWidth: '80%', m: '0 auto'}}>
                                    {lastFourTaxId.map((digit, index) => (
                                        <SingleDigitInput
                                            key={`taxid-${index}`}
                                            value={digit}
                                            onChange={evt =>
                                                handleDigitChange(
                                                    index,
                                                    evt.currentTarget.value,
                                                    setLastFourTaxId,
                                                    taxIdInputRefs,
                                                )
                                            }
                                            onKeyDown={evt => handleKeyDown(index, evt, taxIdInputRefs)}
                                            onPaste={evt => handlePaste(evt, setLastFourTaxId, taxIdInputRefs)}
                                            inputRef={taxIdInputRefs[index]}
                                            data-testid={`tax-digit-${index}`}
                                        />
                                    ))}
                                </Box>
                            </Grid>
                            <Grid flex={1} data-testid="cvv-section">
                                <InputLabel htmlFor="cid" sx={{mb: 1}}>
                                    Security code on back of card
                                </InputLabel>
                                <OutlinedInput
                                    type="text"
                                    id="cid"
                                    name="cid"
                                    inputProps={{
                                        maxLength: 6,
                                        inputMode: 'numeric',
                                        'data-testid': 'cvv-input',
                                    }}
                                    inputMode="numeric"
                                    value={cvv}
                                    onChange={handleCVVChange}
                                    fullWidth
                                    sx={{maxWidth: '80%', ml: '10%'}}
                                />
                            </Grid>
                        </Grid>
                        <Box visibility="hidden" display="none">
                            <TextElement
                                id="bt_last_four"
                                bt={bt}
                                ref={btLast4Ref}
                                readOnly
                                value={lastFourCard.join('')}
                            />
                            <TextElement id="bt_ssn" bt={bt} ref={btSSNRef} readOnly value={lastFourTaxId.join('')} />
                            <TextElement id="bt_cvc" bt={bt} ref={btCVCRef} readOnly value={cvv} />
                        </Box>
                    </DialogContent>
                    <DialogActions>
                        <Button
                            variant="contained"
                            type="submit"
                            disabled={!activationButtonEnabled}
                            loading={activationPending}
                        >
                            Confirm
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
            {activateResult === 'success' ? (
                <Snackbar
                    open={!!activateResult}
                    autoHideDuration={5000}
                    onClose={closeToast}
                    anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                >
                    <Alert
                        onClose={closeToast}
                        severity={activateResult === 'success' ? 'success' : 'error'}
                        variant="filled"
                        sx={{
                            width: 350,
                            backgroundColor:
                                activateResult === 'success'
                                    ? 'var(--mui-palette-primary-main)'
                                    : 'var(--mui-palette-error-main)',
                        }}
                    >
                        {activateResult === 'success' ? (
                            <>
                                <AlertTitle>Congratulations</AlertTitle>
                                <Typography>Your card has been activated!</Typography>
                            </>
                        ) : null}
                    </Alert>
                </Snackbar>
            ) : null}
        </>
    )
}

interface SingleDigitInputProps {
    value: string
    onChange: (e: ChangeEvent<HTMLInputElement>) => void
    onKeyDown: (e: KeyboardEvent<HTMLInputElement>) => void
    onPaste: (e: ClipboardEvent<HTMLInputElement>) => void
    inputRef: React.RefObject<HTMLInputElement>
    'data-testid'?: string
}

function SingleDigitInput({
    value,
    onChange,
    onKeyDown,
    onPaste,
    inputRef,
    'data-testid': testId,
}: SingleDigitInputProps) {
    return (
        <OutlinedInput
            type="text"
            inputProps={{
                maxLength: 1,
                ref: inputRef,
                inputMode: 'numeric',
                'data-testid': testId,
            }}
            inputMode="numeric"
            value={value}
            onChange={onChange}
            onKeyDown={onKeyDown}
            onPaste={onPaste}
            sx={{width: 50}}
        />
    )
}

// Util fn for async simulation
function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms))
}
