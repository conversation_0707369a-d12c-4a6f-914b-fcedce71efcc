import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest'
import {render, screen, waitFor} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import ReplaceCard from './ReplaceCard'
import {replaceCard} from '@/actions/cards'
import type {Card} from '@/services/controllers/cards'
import type {Result} from '@/actions/types'
import type {ReplaceCardSuccess} from '@/actions/cards'

// Configure a timeout for waitFor
const WAIT_TIMEOUT = 1000

// Mock the replace card action
vi.mock('@/actions/cards', () => ({
    replaceCard: vi.fn(),
}))

// Mock the useUserData hook
vi.mock('@/hooks/useUserData', () => ({
    useUserData: vi.fn().mockImplementation(({select} = {}) => {
        const data = {
            primaryAddress: {
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                locality: 'New York',
                administrativeArea: 'NY',
                postalCode: '10001',
                country: 'USA',
            },
        }

        return {
            data: select ? select(data) : data,
            isLoading: false,
            isError: false,
            error: null,
            status: 'success',
            isPending: false,
            isSuccess: true,
            isFetching: false,
            refetch: vi.fn(),
            remove: vi.fn(),
            fetchStatus: 'idle',
        }
    }),
}))

// Mock React Query client for invalidation calls
vi.mock('@tanstack/react-query', () => ({
    useQueryClient: vi.fn().mockReturnValue({
        invalidateQueries: vi.fn().mockResolvedValue(undefined),
    }),
}))

describe('ReplaceCard', () => {
    // Mock card data - physical card
    const mockPhysicalCard: Card = {
        cardId: 'card_123',
        creditAccountId: 'ca_123',
        type: 'physical',
        lastFour: '1234',
        expiration: {
            month: 12,
            year: 2025,
        },
        state: 'active',
        createdOn: new Date('2024-01-01'),
        updatedOn: new Date('2024-01-01'),
        token: 'token_123',
    }

    // Mock card with authorized user
    const mockAuthorizedUserCard: Card = {
        ...mockPhysicalCard,
        cardId: 'card_auth_123',
        authorizedUserId: 'auth_123',
        // Mock spendControls as a Set
        spendControls: new Set([
            {
                limit: {
                    amount: 1000,
                    currency: 'USD',
                },
                cadence: {
                    interval: 'monthly',
                },
                remaining: {
                    amount: 800,
                    currency: 'USD',
                },
            },
        ]),
        // Properly structured AuthorizationControls
        authorizationControls: {
            card: new Set([
                {
                    type: 'blockedMerchantCategories',
                    categories: new Set(['5812', '5814']),
                },
            ]),
        },
    }

    // Mock virtual card
    const mockVirtualCard: Card = {
        ...mockPhysicalCard,
        cardId: 'card_456',
        type: 'virtual',
    }

    // Success response mock
    const successResult: Result<ReplaceCardSuccess> = {
        success: true,
        data: {
            cardId: mockPhysicalCard.cardId,
            state: mockPhysicalCard.state,
            newCardId: 'card_new_123',
            newCardState: 'pendingActivation',
        },
    }

    // Error response mock
    const errorResult: Result<ReplaceCardSuccess> = {
        success: false,
        error: 'Failed to replace card',
    }

    beforeEach(() => {
        vi.clearAllMocks()
        vi.mocked(replaceCard).mockResolvedValue(successResult)
    })

    describe('Initial render', () => {
        it('displays the card information correctly for physical card', () => {
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Check card number
            expect(screen.getByText(/•••• •••• •••• 1234/)).toBeInTheDocument()

            // Check shipping address from person data
            expect(screen.getByText('123 Main St')).toBeInTheDocument()
            expect(screen.getByText('Apt 4B')).toBeInTheDocument()
            expect(screen.getByText('New York, NY 10001')).toBeInTheDocument()

            // Check for warning message
            expect(screen.getByText(/your old card will be deactivated/i)).toBeInTheDocument()
        })

        it('displays the card information correctly for virtual card', () => {
            render(<ReplaceCard card={mockVirtualCard} onFinish={() => {}} />)

            // Check card number
            expect(screen.getByText(/•••• •••• •••• 1234/)).toBeInTheDocument()

            // Check for warning message
            expect(screen.getByText(/your old card will be deactivated/i)).toBeInTheDocument()

            // Check that no shipping address is shown
            expect(screen.queryByText('Your new card will be sent to')).not.toBeInTheDocument()
            expect(screen.queryByText('123 Main St')).not.toBeInTheDocument()
        })

        it('throws error if card is null', () => {
            expect(() => render(<ReplaceCard card={null} onFinish={() => {}} />)).toThrow('Card ID is required')
        })
    })

    describe('Address form', () => {
        it('toggles between viewing and editing address for physical cards', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Initially in view mode
            expect(screen.getByText('Your new card will be sent to')).toBeInTheDocument()

            // Click change address
            await user.click(screen.getByText('change address'))

            // Now in edit mode
            expect(screen.getByLabelText('Street Address')).toBeInTheDocument()
            expect(screen.getByLabelText('City')).toBeInTheDocument()

            // Click save address
            await user.click(screen.getByRole('button', {name: 'Save Address'}))

            // Back to view mode
            expect(screen.getByText('Your new card will be sent to')).toBeInTheDocument()
        })

        it('does not show address form for virtual cards', () => {
            render(<ReplaceCard card={mockVirtualCard} onFinish={() => {}} />)

            // Check that no address form or display is shown
            expect(screen.queryByText('Your new card will be sent to')).not.toBeInTheDocument()
            expect(screen.queryByText('change address')).not.toBeInTheDocument()
        })

        it('allows editing the address fields', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Enter edit mode
            await user.click(screen.getByText('change address'))

            // Clear and fill new address details
            const streetField = screen.getByLabelText('Street Address')
            await user.clear(streetField)
            await user.type(streetField, '456 New St')

            const cityField = screen.getByLabelText('City')
            await user.clear(cityField)
            await user.type(cityField, 'Boston')

            // Handle state select dropdown
            const stateSelect = screen.getByLabelText('State')
            await user.click(stateSelect)
            await user.click(screen.getByText('MA'))

            const zipField = screen.getByLabelText('Zip Code')
            await user.clear(zipField)
            await user.type(zipField, '02118')

            // Save the changes
            await user.click(screen.getByRole('button', {name: 'Save Address'}))

            // Check new address is displayed
            expect(screen.getByText('456 New St')).toBeInTheDocument()
            expect(screen.getByText('Boston, MA 02118')).toBeInTheDocument()
        })
    })

    describe('Card replacement flow', () => {
        it('submits replace request with correct data for primary user physical card', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={onFinish} />)

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Click the confirm button in the popover
            await user.click(screen.getByRole('button', {name: /replace my card/i}))

            await waitFor(
                () => {
                    expect(replaceCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            // Verify correct parameters were passed to replaceCard
            expect(replaceCard).toHaveBeenCalledWith(
                {
                    cardId: mockPhysicalCard.cardId,
                    type: mockPhysicalCard.type,
                    lastFour: mockPhysicalCard.lastFour,
                    state: mockPhysicalCard.state,
                },
                {
                    addressLine1: '123 Main St',
                    addressLine2: 'Apt 4B',
                    postalCode: '10001',
                    locality: 'New York',
                    administrativeArea: 'NY',
                    country: 'USA',
                },
            )

            expect(onFinish).toHaveBeenCalled()
        })

        it('submits replace request with correct data for virtual card', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()
            render(<ReplaceCard card={mockVirtualCard} onFinish={onFinish} />)

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Click the confirm button in the popover
            await user.click(screen.getByRole('button', {name: /replace my card/i}))

            await waitFor(
                () => {
                    expect(replaceCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            // Verify correct parameters were passed to replaceCard (no shipping for virtual)
            expect(replaceCard).toHaveBeenCalledWith(
                {
                    cardId: mockVirtualCard.cardId,
                    type: mockVirtualCard.type,
                    lastFour: mockVirtualCard.lastFour,
                    state: mockVirtualCard.state,
                },
                undefined,
            )

            expect(onFinish).toHaveBeenCalled()
        })

        it('submits replace request with authorized user data preserved', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockAuthorizedUserCard} onFinish={() => {}} />)

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Click the confirm button in the popover
            await user.click(screen.getByRole('button', {name: /replace my card/i}))

            await waitFor(
                () => {
                    expect(replaceCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            // Verify authorizedUserId, spendControls, and authorizationControls were passed
            expect(replaceCard).toHaveBeenCalledWith(
                expect.objectContaining({
                    cardId: mockAuthorizedUserCard.cardId,
                    authorizedUserId: mockAuthorizedUserCard.authorizedUserId,
                    spendControls: mockAuthorizedUserCard.spendControls,
                    authorizationControls: mockAuthorizedUserCard.authorizationControls,
                }),
                expect.anything(),
            )
        })

        it('handles modified shipping address in the request', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Enter edit mode
            await user.click(screen.getByText('change address'))

            // Clear and fill new address details
            const streetField = screen.getByLabelText('Street Address')
            await user.clear(streetField)
            await user.type(streetField, '789 Different Ave')

            const zipField = screen.getByLabelText('Zip Code')
            await user.clear(zipField)
            await user.type(zipField, '90210')

            // Save the address
            await user.click(screen.getByRole('button', {name: 'Save Address'}))

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Click the confirm button in the popover
            await user.click(screen.getByRole('button', {name: /replace my card/i}))

            await waitFor(
                () => {
                    expect(replaceCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            // Verify the updated address was used
            expect(replaceCard).toHaveBeenCalledWith(
                expect.anything(),
                expect.objectContaining({
                    addressLine1: '789 Different Ave',
                    postalCode: '90210',
                }),
            )
        })

        it('calls onFinish after successful replacement', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()

            render(<ReplaceCard card={mockPhysicalCard} onFinish={onFinish} />)

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Click the confirm button in the popover
            await user.click(screen.getByRole('button', {name: /replace my card/i}))

            // Verify that the card was replaced and onFinish was called
            await waitFor(
                () => {
                    expect(replaceCard).toHaveBeenCalled()
                    expect(onFinish).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })
    })

    describe('Error handling', () => {
        it('shows error message when replace fails', async () => {
            vi.mocked(replaceCard).mockResolvedValue(errorResult)

            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Click the confirm button in the popover
            await user.click(screen.getByRole('button', {name: /replace my card/i}))

            await waitFor(
                () => {
                    expect(screen.getByText(/Failed to replace card/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })

        it('handles loading state during replacement', async () => {
            // Mock the replaceCard function to return a delayed promise
            vi.mocked(replaceCard).mockImplementation(
                () => new Promise(resolve => setTimeout(() => resolve(successResult), 100)),
            )

            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Click the replace button
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            const replaceConfirmButton = screen.getByRole('button', {name: /replace my card/i})

            // Click the confirm button in the popover
            await user.click(replaceConfirmButton)

            // Wait for loading state to be applied
            await waitFor(
                () => {
                    expect(replaceConfirmButton).toBeDisabled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            // Wait for request to complete
            await waitFor(
                () => {
                    expect(replaceCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })
    })

    describe('UI interactions', () => {
        it('disables replace button when editing address for physical cards', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Enter edit mode
            await user.click(screen.getByText('change address'))

            // Replace button should be disabled during editing
            expect(screen.getByRole('button', {name: /replace card/i})).toBeDisabled()

            // Save the address
            await user.click(screen.getByRole('button', {name: 'Save Address'}))

            // Replace button should be enabled again
            expect(screen.getByRole('button', {name: /replace card/i})).not.toBeDisabled()
        })

        it('never disables replace button due to editing address for virtual cards', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockVirtualCard} onFinish={() => {}} />)

            // Replace button should be enabled since virtual cards don't have address editing
            expect(screen.getByRole('button', {name: /replace card/i})).not.toBeDisabled()
        })

        it('calls onFinish when cancel button is clicked', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={onFinish} />)

            await user.click(screen.getByRole('button', {name: /cancel/i}))
            expect(onFinish).toHaveBeenCalled()
        })

        it('closes the popover when "Keep my card" is clicked', async () => {
            const user = userEvent.setup()
            render(<ReplaceCard card={mockPhysicalCard} onFinish={() => {}} />)

            // Open the popover
            await user.click(screen.getByRole('button', {name: /replace card/i}))

            // Confirm popover is shown
            expect(screen.getByText(/Replace Card\?/i)).toBeInTheDocument()

            // Click "Keep my card"
            await user.click(screen.getByRole('button', {name: /keep my card/i}))

            // Confirm popover is closed
            await waitFor(
                () => {
                    expect(screen.queryByText(/Replace Card\?/i)).not.toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })
    })
})
