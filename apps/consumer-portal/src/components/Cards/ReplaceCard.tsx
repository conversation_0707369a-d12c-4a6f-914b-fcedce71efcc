'use client'

import {
    <PERSON><PERSON><PERSON>ontent,
    DialogTitle,
    <PERSON>rid,
    <PERSON>po<PERSON>,
    Link as MUILink,
    Button,
    DialogActions,
    TextField,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Popover,
    Paper,
    Box,
    Snackbar,
    Alert,
    AlertTitle,
} from '@mui/material'
import type {Card, Type} from '@/services/controllers/cards'
import Image from 'next/image'
import {useState, type SyntheticEvent, useEffect} from 'react'
import {replaceCard} from '@/actions/cards'
import {stateNames} from '@/utils/enums'
import {useUserData} from '@/hooks/useUserData'
import {useQueryClient} from '@tanstack/react-query'

interface ReplaceCardProps {
    card: Card | null
    onFinish: () => void
}

export default function ReplaceCard({card, onFinish}: ReplaceCardProps) {
    if (!card || !card?.cardId) {
        throw new Error('Card ID is required')
    }

    const queryClient = useQueryClient()

    const {data: shippingDefaults} = useUserData({select: data => data.primaryAddress})

    const [hasError, setHasError] = useState<null | string>(null)
    const [callingReplace, setCallingReplace] = useState(false)
    const [addressToShip, setAddressToShip] = useState({
        addressLine1: shippingDefaults?.addressLine1 ?? '',
        addressLine2: shippingDefaults?.addressLine2 ?? '',
        city: shippingDefaults?.locality ?? '',
        state: shippingDefaults?.administrativeArea ?? '',
        zip: shippingDefaults?.postalCode ?? '',
    })

    const [editingAddress, setEditingAddress] = useState(false)

    const [popoverAnchor, setPopoverAnchor] = useState<HTMLButtonElement | null>(null)
    const confirmPopover = Boolean(popoverAnchor)

    function handleOpenPopover(evt: SyntheticEvent<HTMLButtonElement>) {
        // Validate address before opening confirmation
        setPopoverAnchor(evt.currentTarget)
    }

    function closePopover() {
        setPopoverAnchor(null)
    }

    function onClose() {
        onFinish()
    }

    async function handleReplaceCard() {
        if (card?.cardId) {
            setCallingReplace(true)

            try {
                // Include all required properties and optional properties if they exist
                const cardToReplace = {
                    cardId: card.cardId,
                    type: card.type,
                    lastFour: card.lastFour,
                    state: card.state,
                    // Conditionally add optional properties
                    ...(card.authorizedUserId && {authorizedUserId: card.authorizedUserId}),
                    ...(card.spendControls && {spendControls: card.spendControls}),
                    ...(card.authorizationControls && {authorizationControls: card.authorizationControls}),
                }

                // Only include shipping address if it's a physical card
                const shipping =
                    card.type === 'physical'
                        ? {
                              addressLine1: addressToShip.addressLine1,
                              addressLine2: addressToShip.addressLine2,
                              postalCode: addressToShip.zip,
                              locality: addressToShip.city,
                              administrativeArea: addressToShip.state,
                              country: 'USA',
                          }
                        : undefined

                const result = await replaceCard(cardToReplace, shipping)

                if (!result.success) {
                    setHasError(result.error)
                    return
                }

                queryClient.invalidateQueries({queryKey: ['cards']})

                onClose()
            } catch (error) {
                setHasError(
                    error instanceof Error ? error.message : 'There was an issue replacing your card. Please try again',
                )
            } finally {
                setCallingReplace(false)
            }
        }
    }

    function updateAddress(value: string, updateLine: string) {
        setAddressToShip(previousState => ({
            ...previousState,
            [updateLine]: value,
        }))

        // Clear general error message
        if (hasError) {
            setHasError(null)
        }
    }

    const aspectRatio = 1.586
    const cardWidth = 81

    return (
        <>
            <Grid container gap={2} alignContent="flex-start" flex={1}>
                <Grid container gap={2} flexDirection="row">
                    <Image
                        src={getCardAsset(card.type)}
                        alt="Card Art"
                        width={cardWidth}
                        height={cardWidth / aspectRatio}
                        style={{objectFit: 'fill'}}
                    />
                    <Grid flexDirection="column">
                        <Typography flex={1} fontSize={12} fontWeight={400} color="textSecondary" alignContent="center">
                            Card Number
                        </Typography>
                        <Typography fontSize={16} fontWeight={600} lineHeight="160%">
                            •••• •••• •••• {card.lastFour}
                        </Typography>
                    </Grid>
                </Grid>
                <Typography fontSize={16} color="textSecondary">
                    At &quot;Company Name&quot; we take extra steps to help keep your account secure; you&apos;ll get a
                    new card with a different card number,{' '}
                    <Box component="span" sx={{color: 'var(--mui-palette-warning-dark)'}}>
                        your old card will be deactivated and your subscriptions/autopay will need to be updated.
                    </Box>
                </Typography>

                {/* Only show address section for physical cards */}
                {card.type === 'physical' &&
                    (editingAddress ? (
                        <Grid container flexDirection="column" alignItems="flex-start" gap={2} alignSelf="stretch">
                            <Grid container flexDirection="column" alignItems="flex-start" gap={3}>
                                <TextField
                                    label="Street Address"
                                    fullWidth
                                    slotProps={{inputLabel: {shrink: true}}}
                                    value={addressToShip.addressLine1}
                                    onChange={evt => updateAddress(evt.target.value, 'addressLine1')}
                                />
                                <Grid container flexDirection="row" gap={3} alignItems="flex-start" alignSelf="stretch">
                                    <TextField
                                        label="Apt, Ste, Fl"
                                        fullWidth
                                        sx={{flex: 1}}
                                        slotProps={{inputLabel: {shrink: true}}}
                                        value={addressToShip.addressLine2}
                                        onChange={evt => updateAddress(evt.target.value, 'addressLine2')}
                                    />
                                    <TextField
                                        label="Zip Code"
                                        fullWidth
                                        sx={{flex: 1}}
                                        slotProps={{inputLabel: {shrink: true}}}
                                        value={addressToShip.zip}
                                        onChange={evt => updateAddress(evt.target.value, 'zip')}
                                    />
                                </Grid>
                                <Grid
                                    container
                                    flexDirection="row"
                                    gap={3}
                                    alignItems="flex-start"
                                    alignSelf="stretch"
                                    flex={1}
                                >
                                    <TextField
                                        label="City"
                                        fullWidth
                                        sx={{flex: 1}}
                                        slotProps={{inputLabel: {shrink: true}}}
                                        value={addressToShip.city}
                                        onChange={evt => updateAddress(evt.target.value, 'city')}
                                    />
                                    <FormControl fullWidth sx={{flex: 1}}>
                                        <InputLabel id="state-input-label">State</InputLabel>
                                        <Select
                                            label="State"
                                            labelId="state-input-label"
                                            value={addressToShip.state}
                                            onChange={evt => updateAddress(evt.target.value, 'state')}
                                        >
                                            {stateNames.map(state => (
                                                <MenuItem key={state} value={state}>
                                                    {state}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                            </Grid>
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={() => {
                                    setEditingAddress(false)
                                }}
                                sx={{mt: 1}}
                            >
                                Save Address
                            </Button>
                        </Grid>
                    ) : (
                        <Grid container flexDirection="column">
                            <Typography fontSize={16} fontWeight={600} lineHeight="175%">
                                Your new card will be sent to
                            </Typography>
                            <Typography fontSize={16} fontWeight={400} lineHeight="175%">
                                {addressToShip.addressLine1}
                            </Typography>
                            {addressToShip.addressLine2 && (
                                <Typography fontSize={16} fontWeight={400} lineHeight="175%">
                                    {addressToShip.addressLine2}
                                </Typography>
                            )}
                            <Typography fontSize={16} fontWeight={400} lineHeight="175%">
                                {addressToShip.city}, {addressToShip.state} {addressToShip.zip}
                            </Typography>
                            <MUILink
                                fontSize={16}
                                fontWeight={400}
                                textTransform="initial"
                                color="info"
                                onClick={() => setEditingAddress(true)}
                            >
                                change address
                            </MUILink>
                        </Grid>
                    ))}
            </Grid>
            <DialogActions sx={{flexDirection: 'column'}}>
                <Button
                    variant="contained"
                    fullWidth
                    color="primary"
                    onClick={handleOpenPopover}
                    disabled={callingReplace || (card.type === 'physical' && editingAddress)}
                >
                    Replace Card
                </Button>
                <Button variant="text" fullWidth color="error" onClick={onClose}>
                    Cancel
                </Button>
                <Popover
                    open={confirmPopover}
                    anchorEl={popoverAnchor}
                    onClose={closePopover}
                    anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                    }}
                    transformOrigin={{
                        vertical: 'center',
                        horizontal: 'center',
                    }}
                    slotProps={{
                        paper: {
                            sx: {
                                maxWidth: 340,
                            },
                        },
                    }}
                >
                    <Paper>
                        <DialogTitle>
                            <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                                Replace Card?
                            </Typography>
                        </DialogTitle>
                        <DialogContent>
                            <Typography fontSize={16} lineHeight="150%">
                                Replacing your card will immediately prevent any new or recurring charges associated
                                with your current card from being processed.
                            </Typography>
                        </DialogContent>
                        <DialogActions sx={{justifyContent: 'space-between', gap: 1}}>
                            <Button
                                variant="text"
                                color="primary"
                                onClick={() => {
                                    onClose()
                                    closePopover()
                                }}
                            >
                                Keep my card
                            </Button>
                            <Button
                                loading={callingReplace}
                                variant="outlined"
                                color="error"
                                onClick={handleReplaceCard}
                            >
                                Replace my card
                            </Button>
                        </DialogActions>
                    </Paper>
                </Popover>
            </DialogActions>
            {hasError && (
                <Snackbar
                    open={!!hasError}
                    autoHideDuration={5000}
                    onClose={() => setHasError(null)}
                    anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                >
                    <Alert
                        onClose={() => setHasError(null)}
                        severity="error"
                        variant="filled"
                        sx={{width: 350, backgroundColor: 'var(--mui-palette-error-main)'}}
                    >
                        <AlertTitle>Error</AlertTitle>
                        <Typography>{hasError}</Typography>
                    </Alert>
                </Snackbar>
            )}
        </>
    )
}

function getCardAsset(cardType: Type) {
    if (cardType === 'physical') {
        return '/assets/Tallied-Card-Physical.png'
    } else {
        return '/assets/Tallied-Card-Virtual.png'
    }
}
