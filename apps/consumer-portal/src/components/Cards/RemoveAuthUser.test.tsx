import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen, waitFor} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import RemoveAuthUser from './RemoveAuthUser'
import {removeAuthorizedUser} from '@/actions/authorized-users'
import type {Result} from '@/actions/types'

// Configure a timeout for waitFor
const WAIT_TIMEOUT = 1000

// Mock the removeAuthorizedUser action
vi.mock('@/actions/authorized-users', () => ({
    removeAuthorizedUser: vi.fn(),
}))

// Mock React Query client for invalidation calls
vi.mock('@tanstack/react-query', () => ({
    useQueryClient: vi.fn().mockReturnValue({
        invalidateQueries: vi.fn().mockResolvedValue(undefined),
    }),
}))

describe('RemoveAuthUser', () => {
    const mockAuthUserId = 'auth_123'
    const onFinish = vi.fn()

    // Success response mock
    const successResult: Result<void> = {
        success: true,
        data: undefined,
    }

    // Error response mock
    const errorResult: Result<void> = {
        success: false,
        error: 'Failed to remove authorized user',
    }

    beforeEach(() => {
        vi.clearAllMocks()
        vi.mocked(removeAuthorizedUser).mockResolvedValue(successResult)
    })

    describe('Initial render', () => {
        it('displays the correct title and warning messages', () => {
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            expect(screen.getByText('Remove Authorized User')).toBeInTheDocument()
            expect(screen.getByText(/permanently revoke their access/i)).toBeInTheDocument()
            expect(screen.getByText(/all cards associated with this user will be cancelled/i)).toBeInTheDocument()
            expect(screen.getByText(/this action cannot be undone/i)).toBeInTheDocument()
        })

        it('displays primary and secondary action buttons', () => {
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            expect(screen.getByRole('button', {name: /remove user/i})).toBeInTheDocument()
            expect(screen.getByRole('button', {name: /cancel/i})).toBeInTheDocument()
        })
    })

    describe('User interactions', () => {
        it('shows confirmation popover when clicking Remove User', async () => {
            const user = userEvent.setup()
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            await user.click(screen.getByRole('button', {name: /remove user/i}))

            expect(screen.getByText('Remove User?')).toBeInTheDocument()
            expect(screen.getByText(/this will immediately cancel all cards/i)).toBeInTheDocument()
        })

        it('closes confirmation popover when clicking Cancel', async () => {
            const user = userEvent.setup()
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            // Open popover
            await user.click(screen.getByRole('button', {name: /remove user/i}))
            expect(screen.getByText('Remove User?')).toBeInTheDocument()

            // Click Cancel in popover
            await user.click(screen.getByRole('button', {name: /cancel/i}))
            expect(screen.queryByText('Remove User?')).not.toBeInTheDocument()
        })

        it('calls onFinish when clicking the main Cancel button', async () => {
            const user = userEvent.setup()
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            await user.click(screen.getAllByRole('button', {name: /cancel/i})[0])
            expect(onFinish).toHaveBeenCalled()
        })
    })

    describe('Remove user flow', () => {
        it('calls removeAuthorizedUser with correct ID and handles success', async () => {
            const user = userEvent.setup()
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            // Click the remove button
            await user.click(screen.getByRole('button', {name: /remove user/i}))

            // Click confirm in the popover
            await user.click(screen.getByRole('button', {name: /remove user/i}))

            await waitFor(
                () => {
                    expect(removeAuthorizedUser).toHaveBeenCalledWith(mockAuthUserId)
                    expect(onFinish).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })

        it('shows error message when removal fails', async () => {
            vi.mocked(removeAuthorizedUser).mockResolvedValue(errorResult)

            const user = userEvent.setup()
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            // Click the remove button
            await user.click(screen.getByRole('button', {name: /remove user/i}))

            // Click confirm in the popover
            await user.click(screen.getByRole('button', {name: /remove user/i}))

            await waitFor(
                () => {
                    expect(screen.getByText('Failed to remove authorized user')).toBeInTheDocument()
                    expect(onFinish).not.toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })

        it('handles loading state during removal', async () => {
            // Mock the removeAuthorizedUser function to return a delayed promise
            vi.mocked(removeAuthorizedUser).mockImplementation(
                () => new Promise(resolve => setTimeout(() => resolve(successResult), 100)),
            )

            const user = userEvent.setup()
            render(<RemoveAuthUser authUserId={mockAuthUserId} onFinish={onFinish} />)

            // Click the remove button
            await user.click(screen.getByRole('button', {name: /remove user/i}))

            const confirmButton = screen.getByRole('button', {name: /remove user/i})

            // Click confirm in the popover
            await user.click(confirmButton)

            // Verify loading state
            await waitFor(
                () => {
                    expect(confirmButton).toBeDisabled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            // Wait for completion
            await waitFor(
                () => {
                    expect(removeAuthorizedUser).toHaveBeenCalled()
                    expect(onFinish).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )
        })
    })
})
