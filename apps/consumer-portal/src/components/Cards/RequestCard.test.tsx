import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest'
import {render, screen, waitFor} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import RequestCard from './RequestCard'
import {requestCard} from '@/actions/cards'
import {createAuthorizedUser} from '@/actions/authorized-users'
import type {Card} from '@/services/controllers/cards'
import type {Result} from '@/actions/types'
import type {AuthorizedUser} from '@/services/controllers/authorizedUsers'

// Configure a timeout for waitFor
const WAIT_TIMEOUT = 1000

// Mock the API actions
vi.mock('@/actions/cards', () => ({
    requestCard: vi.fn(),
}))

vi.mock('@/actions/authorized-users', () => ({
    createAuthorizedUser: vi.fn(),
}))

// Mock the date picker component to bypass direct DOM interaction issues
vi.mock('@mui/x-date-pickers', () => ({
    LocalizationProvider: ({children}: {children: React.ReactNode}) => children,
    DatePicker: ({onChange, label}: {onChange?: (value: any) => void; label?: string}) => (
        <input
            data-testid="date-picker-input"
            aria-label={label}
            onChange={e => onChange && onChange(e.target.value)}
        />
    ),
}))

// Mock the hooks with more robust implementations
vi.mock('@/hooks/useUserData', () => ({
    useUserData: vi.fn().mockImplementation(({select} = {}) => {
        const data = {
            primaryAddress: {
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                locality: 'New York',
                administrativeArea: 'NY',
                postalCode: '10001',
                country: 'USA',
            },
        }

        return {
            data: select ? select(data) : data,
            isLoading: false,
            isError: false,
            error: null,
            status: 'success',
            isPending: false,
            isSuccess: true,
            isFetching: false,
            refetch: vi.fn(),
            remove: vi.fn(),
            fetchStatus: 'idle',
        }
    }),
}))

vi.mock('@/hooks/useAuthorizedUserData', () => ({
    useAuthorizedUsersData: vi.fn().mockImplementation(({select} = {}) => {
        const data = {
            authorizedUsers: [
                {
                    authorizedUserId: 'auth_123',
                    firstName: 'Jane',
                    lastName: 'Doe',
                    status: 'active',
                },
                {
                    authorizedUserId: 'auth_456',
                    firstName: 'John',
                    lastName: 'Smith',
                    status: 'active',
                },
            ],
        }

        return {
            data: select ? select(data) : data,
            isLoading: false,
            isError: false,
            error: null,
            status: 'success',
            isPending: false,
            isSuccess: true,
            isFetching: false,
            refetch: vi.fn(),
            remove: vi.fn(),
            fetchStatus: 'idle',
        }
    }),
}))

// // Mock React Query client for invalidation calls
// vi.mock('@tanstack/react-query', () => ({
//     useQueryClient: vi.fn().mockReturnValue({
//         invalidateQueries: vi.fn().mockResolvedValue(undefined),
//     }),
// }))

// Success response mock
const cardSuccessResult: Result<Card> = {
    success: true,
    data: {
        cardId: 'card_123',
        creditAccountId: 'ca_123',
        type: 'physical',
        lastFour: '1234',
        expiration: {
            month: 12,
            year: 2025,
        },
        state: 'pendingActivation',
        createdOn: new Date('2024-01-01'),
        updatedOn: new Date('2024-01-01'),
        token: '1234',
    },
}

// Error response mock
const cardErrorResult: Result<Card> = {
    success: false,
    error: 'Failed to request card',
}

// Authorized user success response mock
const authorizedUserSuccessResult: Result<AuthorizedUser> = {
    success: true,
    data: {
        authorizedUserId: 'new_auth_123',
        firstName: 'New',
        lastName: 'User',
        status: 'active',
        creditAccountId: 'ca_123',
        dateOfBirth: new Date('1999-01-01'),
        maskedNationalId: '***-**-6789',
        createdOn: new Date('2024-01-01'),
        updatedOn: new Date('2024-01-01'),
    },
}

// Authorized user error response mock
const authorizedUserErrorResult: Result<AuthorizedUser> = {
    success: false,
    error: 'Failed to create authorized user',
}

describe('RequestCard', () => {
    beforeEach(() => {
        vi.clearAllMocks()
        vi.mocked(requestCard).mockResolvedValue(cardSuccessResult)
        vi.mocked(createAuthorizedUser).mockResolvedValue(authorizedUserSuccessResult)
    })

    afterEach(() => {
        vi.clearAllTimers()
    })

    describe('Initial render and card recipient selection', () => {
        it('renders the card recipient selection screen initially', () => {
            render(<RequestCard onFinish={() => {}} />)

            expect(screen.getByText(/who is the card for/i)).toBeInTheDocument()
            expect(screen.getByText(/it's for me/i)).toBeInTheDocument()
            expect(screen.getByText(/it's for jane doe/i)).toBeInTheDocument()
            expect(screen.getByText(/it's for john smith/i)).toBeInTheDocument()
            expect(screen.getByText(/it's for someone else/i)).toBeInTheDocument()
        })

        it('selects "for me" option and shows card form', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for me/i))

            await waitFor(
                () => {
                    expect(screen.getByText(/this card is for me/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/virtual card/i)).toBeInTheDocument()
            expect(screen.getByText(/physical card/i)).toBeInTheDocument()
            expect(screen.getByText(/would you like to give your card a nickname/i)).toBeInTheDocument()
        })

        it('selects existing authorized user option and shows card form with spend controls', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for jane doe/i))

            await waitFor(
                () => {
                    expect(screen.getByText(/this card is for jane doe/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/spend limit \(optional\)/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/amount/i)).toBeInTheDocument()
            expect(screen.getByText(/month/i)).toBeInTheDocument()
        })

        it('selects "someone else" option and shows authorized user form', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for someone else/i))

            await waitFor(
                () => {
                    expect(screen.getByText(/this card is for someone else/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/cardholder information/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/first name/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/last name/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/date of birth/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/social security number/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument()
        })
    })

    describe('Card type selection', () => {
        it('shows address form when physical card is selected', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByText(/physical card/i))

            await waitFor(
                () => {
                    expect(screen.getByText(/delivery address/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByLabelText(/street address/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/zip code/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/city/i)).toBeInTheDocument()
            expect(screen.getByLabelText(/state/i)).toBeInTheDocument()
        })

        it('does not show address form when virtual card is selected', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByText(/virtual card/i))

            await waitFor(
                () => {
                    expect(screen.queryByText(/delivery address/i)).not.toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.queryByLabelText(/street address/i)).not.toBeInTheDocument()
        })
    })

    describe('Form validation', () => {
        it('validates required fields for new authorized user', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for someone else/i))
            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(screen.getByText(/please fill in all required fields/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).not.toHaveBeenCalled()
        })

        it('validates email format for new authorized user', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for someone else/i))

            // Fill required fields but with invalid email
            await user.type(screen.getByLabelText(/first name/i), 'Test')
            await user.type(screen.getByLabelText(/last name/i), 'User')
            await user.type(screen.getByLabelText(/social security number/i), '123456789')
            await user.type(screen.getByLabelText(/email address/i), 'invalid-email')

            // Simulate date selection (direct input instead of date picker interaction)
            const dobInput = screen.getByLabelText(/date of birth/i)
            await user.type(dobInput, '01/01/1990')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(
                        screen.getByText(/please enter a valid email address/i, {selector: '.MuiAlert-message'}),
                    ).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).not.toHaveBeenCalled()
        })

        it('validates phone number format for new authorized user if provided', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for someone else/i))

            // Fill all required fields
            await user.type(screen.getByLabelText(/first name/i), 'Test')
            await user.type(screen.getByLabelText(/last name/i), 'User')
            await user.type(screen.getByLabelText(/social security number/i), '123456789')
            await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')

            // Simulate date selection (direct input instead of date picker interaction)
            const dobInput = screen.getByLabelText(/date of birth/i)
            await user.type(dobInput, '01/01/1990')

            // Invalid phone number
            await user.type(screen.getByLabelText(/phone number/i), '123')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).not.toHaveBeenCalled()
        })

        it('validates address fields for physical card', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByText(/physical card/i))

            // Clear address fields (they are pre-filled from mock data)
            const streetAddress = screen.getByLabelText(/street address/i)
            await user.clear(streetAddress)

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(screen.getByText(/address is required for physical cards/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).not.toHaveBeenCalled()
        })
    })

    describe('Card request submission', () => {
        it('successfully requests a virtual card for primary user', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()
            render(<RequestCard onFinish={onFinish} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByText(/virtual card/i))

            // Add a nickname
            await user.type(screen.getByLabelText(/nickname/i), 'My Virtual Card')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(requestCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).toHaveBeenCalledWith({
                type: 'virtual',
            })

            // Check success screen is shown
            await waitFor(
                () => {
                    expect(screen.getByText(/new card added/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/your new virtual card/i)).toBeInTheDocument()
            expect(screen.getByText(/nickname of 'my virtual card'/i, {exact: false})).toBeInTheDocument()
            expect(screen.getByText(/you may begin using this card right away/i)).toBeInTheDocument()
            expect(screen.getByRole('button', {name: /back to my cards/i})).toBeInTheDocument()
        })

        it('successfully requests a physical card for primary user', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByText(/physical card/i))

            // Fill in address details
            await user.clear(screen.getByLabelText(/street address/i))
            await user.type(screen.getByLabelText(/street address/i), '123 Main St')
            await user.clear(screen.getByLabelText(/city/i))
            await user.type(screen.getByLabelText(/city/i), 'Boston')

            // Handle state select dropdown
            const stateSelect = screen.getByLabelText(/state/i)
            await user.click(stateSelect)
            await user.click(screen.getByText('MA'))

            await user.clear(screen.getByLabelText(/zip code/i))
            await user.type(screen.getByLabelText(/zip code/i), '02118')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(requestCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'physical',
                    shipping: expect.objectContaining({
                        addressLine1: '123 Main St',
                        locality: 'Boston',
                        administrativeArea: 'MA',
                        postalCode: '02118',
                    }),
                }),
            )

            // Check success screen is shown
            await waitFor(
                () => {
                    expect(screen.getByText(/new card added/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/your new physical card has been added/i)).toBeInTheDocument()
            expect(screen.getByText(/7-10 business days/i)).toBeInTheDocument()
        })

        it('successfully requests a card for existing authorized user', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for jane doe/i))

            // Set a spend limit
            await user.type(screen.getByLabelText(/amount/i), '1000.00')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(requestCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'virtual',
                    authorizedUserId: 'auth_123',
                    spendControls: [
                        {
                            amount: 100000,
                            cadence: 'monthly',
                        },
                    ],
                }),
            )

            // Check success screen is shown
            await waitFor(
                () => {
                    expect(screen.getByText(/new card added/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/jane's new virtual card has been added/i)).toBeInTheDocument()
        })

        it('successfully creates a new authorized user and requests a card', async () => {
            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for someone else/i))

            // Fill new authorized user details
            await user.type(screen.getByLabelText(/first name/i), 'New')
            await user.type(screen.getByLabelText(/last name/i), 'Person')
            await user.type(screen.getByLabelText(/social security number/i), '123456789')
            await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')

            // Simulate date selection (direct input instead of date picker interaction)
            const dobInput = screen.getByLabelText(/date of birth/i)
            await user.type(dobInput, '01/01/1990')

            // Set a phone number
            await user.type(screen.getByLabelText(/phone number/i), '(*************')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            // Check authorized user was created with correct data
            await waitFor(
                () => {
                    expect(createAuthorizedUser).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(createAuthorizedUser).toHaveBeenCalledWith({
                firstName: 'New',
                lastName: 'Person',
                dob: '01/01/1990',
                ssn: '123456789',
                email: '<EMAIL>',
                phoneNumber: '+15555555555',
            })

            // Check card was requested for the new authorized user
            await waitFor(
                () => {
                    expect(requestCard).toHaveBeenCalled()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(requestCard).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'virtual',
                    authorizedUserId: 'new_auth_123',
                }),
            )

            // Check success screen is shown
            await waitFor(
                () => {
                    expect(screen.getByText(/new card added/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.getByText(/new's new virtual card has been added/i)).toBeInTheDocument()
        })
    })

    describe('Error handling', () => {
        it('displays error when card request fails', async () => {
            vi.mocked(requestCard).mockResolvedValue(cardErrorResult)

            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(screen.getByText(/failed to request card/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(screen.queryByText(/new card added/i)).not.toBeInTheDocument()
        })

        it('displays error when authorized user creation fails', async () => {
            vi.mocked(createAuthorizedUser).mockResolvedValue(authorizedUserErrorResult)

            const user = userEvent.setup()
            render(<RequestCard onFinish={() => {}} />)

            await user.click(screen.getByText(/it's for someone else/i))

            // Fill new authorized user details
            await user.type(screen.getByLabelText(/first name/i), 'New')
            await user.type(screen.getByLabelText(/last name/i), 'Person')
            await user.type(screen.getByLabelText(/social security number/i), '123456789')
            await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')

            // Simulate date selection (direct input instead of date picker interaction)
            const dobInput = screen.getByLabelText(/date of birth/i)
            await user.type(dobInput, '01/01/1990')

            await user.click(screen.getByRole('button', {name: /request new card/i}))

            await waitFor(
                () => {
                    expect(screen.getByText(/failed to create authorized user/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            expect(createAuthorizedUser).toHaveBeenCalled()
            expect(requestCard).not.toHaveBeenCalled()
        })
    })

    describe('UI interactions', () => {
        it('calls onFinish when close button is clicked', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()
            render(<RequestCard onFinish={onFinish} />)

            await user.click(screen.getByRole('button', {name: /close request card form/i}))

            expect(onFinish).toHaveBeenCalled()
        })

        it('calls onFinish when BACK TO MY CARDS button is clicked after success', async () => {
            const user = userEvent.setup()
            const onFinish = vi.fn()
            render(<RequestCard onFinish={onFinish} />)

            await user.click(screen.getByText(/it's for me/i))
            await user.click(screen.getByRole('button', {name: /request new card/i}))

            // Wait for success screen to appear
            await waitFor(
                () => {
                    expect(screen.getByText(/new card added/i)).toBeInTheDocument()
                },
                {timeout: WAIT_TIMEOUT},
            )

            await user.click(screen.getByRole('button', {name: /back to my cards/i}))

            expect(onFinish).toHaveBeenCalled()
        })
    })
})
