'use client'

import {
    <PERSON>alog<PERSON>ontent,
    <PERSON>alogTitle,
    Grid,
    <PERSON><PERSON><PERSON>,
    Button,
    DialogActions,
    Popover,
    Paper,
    Box,
    Snackbar,
    Alert,
    AlertTitle,
    Icon,
} from '@mui/material'
import {useState, type SyntheticEvent} from 'react'
import {useQueryClient} from '@tanstack/react-query'
import {removeAuthorizedUser} from '@/actions/authorized-users'

interface RemoveAuthUserProps {
    authUserId: string
    onFinish: () => void
}

export default function RemoveAuthUser({authUserId, onFinish}: RemoveAuthUserProps) {
    const queryClient = useQueryClient()
    const [hasError, setHasError] = useState<null | string>(null)
    const [callingRemove, setCallingRemove] = useState(false)
    const [popoverAnchor, setPopoverAnchor] = useState<HTMLButtonElement | null>(null)
    const confirmPopover = Boolean(popoverAnchor)

    function handleOpenPopover(evt: SyntheticEvent<HTMLButtonElement>) {
        setPopoverAnchor(evt.currentTarget)
    }

    function closePopover() {
        setPopoverAnchor(null)
    }

    function onClose() {
        onFinish()
    }

    async function handleRemoveUser() {
        setCallingRemove(true)
        try {
            const result = await removeAuthorizedUser(authUserId)

            if (!result.success) {
                setHasError(result.error)
                return
            }

            queryClient.invalidateQueries({queryKey: ['cards']})
            queryClient.invalidateQueries({queryKey: ['authorizedUsers']})
            onClose()
        } catch (error) {
            setHasError(
                error instanceof Error ? error.message : 'There was an issue removing the user. Please try again',
            )
        } finally {
            setCallingRemove(false)
        }
    }

    return (
        <>
            <Grid container gap={2} alignContent="flex-start" flex={1}>
                <Grid container gap={2} flexDirection="row" alignItems="center">
                    <Icon
                        baseClassName="fas"
                        className="fa-solid fa-user-minus"
                        sx={{
                            fontSize: 32,
                            color: 'var(--mui-palette-error-main)',
                        }}
                    />
                    <Typography variant="h6">Remove Authorized User</Typography>
                </Grid>
                <Typography fontSize={16} color="textSecondary">
                    Removing this authorized user will permanently revoke their access to your account.{' '}
                    <Box component="span" sx={{color: 'var(--mui-palette-warning-dark)'}}>
                        All cards associated with this user will be cancelled immediately and any recurring charges or
                        subscriptions will be declined.
                    </Box>
                </Typography>
                <Typography fontSize={16} color="textSecondary">
                    This action cannot be undone. You will need to add them as a new authorized user if you want to
                    restore their access in the future.
                </Typography>
            </Grid>
            <DialogActions sx={{flexDirection: 'column'}}>
                <Button variant="contained" fullWidth color="error" onClick={handleOpenPopover} loading={callingRemove}>
                    Remove User
                </Button>
                <Button variant="text" fullWidth color="primary" onClick={onClose}>
                    Cancel
                </Button>
                <Popover
                    open={confirmPopover}
                    anchorEl={popoverAnchor}
                    onClose={closePopover}
                    anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                    }}
                    transformOrigin={{
                        vertical: 'center',
                        horizontal: 'center',
                    }}
                    slotProps={{
                        paper: {
                            sx: {
                                maxWidth: 340,
                            },
                        },
                    }}
                >
                    <Paper>
                        <DialogTitle>
                            <Typography fontSize={20} fontWeight={600} lineHeight="160%">
                                Remove User?
                            </Typography>
                        </DialogTitle>
                        <DialogContent>
                            <Typography fontSize={16} lineHeight="150%">
                                This will immediately cancel all cards associated with this user and prevent any new or
                                recurring charges from being processed. This action cannot be undone.
                            </Typography>
                        </DialogContent>
                        <DialogActions sx={{justifyContent: 'space-between', gap: 1}}>
                            <Button
                                variant="text"
                                color="primary"
                                onClick={() => {
                                    onClose()
                                    closePopover()
                                }}
                            >
                                Cancel
                            </Button>
                            <Button loading={callingRemove} variant="outlined" color="error" onClick={handleRemoveUser}>
                                Remove User
                            </Button>
                        </DialogActions>
                    </Paper>
                </Popover>
            </DialogActions>
            {hasError && (
                <Snackbar
                    open={!!hasError}
                    autoHideDuration={5000}
                    onClose={() => setHasError(null)}
                    anchorOrigin={{vertical: 'top', horizontal: 'center'}}
                >
                    <Alert
                        onClose={() => setHasError(null)}
                        severity="error"
                        variant="filled"
                        sx={{width: 350, backgroundColor: 'var(--mui-palette-error-main)'}}
                    >
                        <AlertTitle>Error</AlertTitle>
                        <Typography>{hasError}</Typography>
                    </Alert>
                </Snackbar>
            )}
        </>
    )
}
