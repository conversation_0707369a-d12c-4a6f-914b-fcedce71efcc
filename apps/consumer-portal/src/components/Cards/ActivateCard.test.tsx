import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen, waitFor, within} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import ActivateCard from './ActivateCard'
import {activateCard} from '@/actions/cards'
import type {Card} from '@/services/controllers/cards'
import type {Result} from '@/actions/types'

// Mock the card activation action
vi.mock('@/actions/cards', () => ({
    activateCard: vi.fn(),
}))

describe('ActivateCard', () => {
    const mockCard: Card = {
        cardId: 'card_123',
        creditAccountId: 'ca_123',
        type: 'physical',
        lastFour: '1234',
        expiration: {
            month: 12,
            year: 2025,
        },
        state: 'pendingActivation',
        createdOn: new Date('2024-01-01'),
        updatedOn: new Date('2024-01-01'),
        token: '1234',
    }

    const successResult: Result<Card> = {
        success: true,
        data: {
            ...mockCard,
            state: 'active',
        },
    }

    const errorResult: Result<Card> = {
        success: false,
        error: 'Invalid last four digits',
    }

    beforeEach(() => {
        vi.clearAllMocks()
        vi.mocked(activateCard).mockResolvedValue(successResult)
    })

    it('displays activation dialog with all input sections', () => {
        render(<ActivateCard cardId={mockCard.cardId} onClose={() => {}} />)

        expect(screen.getByTestId('card-number-section')).toBeInTheDocument()
        expect(screen.getByTestId('tax-id-section')).toBeInTheDocument()
        expect(screen.getByTestId('cvv-section')).toBeInTheDocument()

        // Verify correct number of inputs in each section
        expect(within(screen.getByTestId('card-number-section')).getAllByRole('textbox')).toHaveLength(4)
        expect(within(screen.getByTestId('tax-id-section')).getAllByRole('textbox')).toHaveLength(4)
        expect(screen.getByTestId('cvv-input')).toBeInTheDocument()
    })

    it('enables submit button after initial load', async () => {
        render(<ActivateCard cardId={mockCard.cardId} onClose={() => {}} />)

        const submitButton = screen.getByRole('button', {name: /confirm/i})

        // Initially disabled
        expect(submitButton).toBeDisabled()

        // Should become enabled after async initialization
        await waitFor(() => {
            expect(submitButton).not.toBeDisabled()
        })
    })

    it('submits activation with correct card digits', async () => {
        const user = userEvent.setup()
        const onClose = vi.fn()
        render(<ActivateCard cardId={mockCard.cardId} onClose={onClose} />)

        // Wait for button to be enabled
        await waitFor(() => {
            expect(screen.getByRole('button', {name: /confirm/i})).not.toBeDisabled()
        })

        // Enter card digits
        for (let i = 0; i < 4; i++) {
            await user.type(screen.getByTestId(`card-digit-${i}`), mockCard.lastFour[i])
        }

        // Enter tax ID digits
        for (let i = 0; i < 4; i++) {
            await user.type(screen.getByTestId(`tax-digit-${i}`), '5678')
        }

        // Enter CVV
        await user.type(screen.getByTestId('cvv-input'), '123')

        // Submit form
        await user.click(screen.getByRole('button', {name: /confirm/i}))

        expect(activateCard).toHaveBeenCalledWith({
            cardId: mockCard.cardId,
            lastFour: mockCard.lastFour,
        })

        // Should show success message and close
        expect(await screen.findByText(/Your card has been activated!/i)).toBeInTheDocument()
        expect(onClose).toHaveBeenCalled()
    })

    it('shows error message when activation fails', async () => {
        vi.mocked(activateCard).mockResolvedValue(errorResult)

        const user = userEvent.setup()
        render(<ActivateCard cardId={mockCard.cardId} onClose={() => {}} />)

        // Wait for button to be enabled
        await waitFor(() => {
            expect(screen.getByRole('button', {name: /confirm/i})).not.toBeDisabled()
        })

        // Enter wrong digits
        for (let i = 0; i < 4; i++) {
            await user.type(screen.getByTestId(`card-digit-${i}`), '0000')
        }

        await user.click(screen.getByRole('button', {name: /confirm/i}))

        // Should show error in dialog (not in snackbar)
        expect(await screen.findByText(/activation failed/i)).toBeInTheDocument()
        expect(screen.queryByText(/Your card has been activated!/i)).not.toBeInTheDocument()
    })

    it('handles pasting digits', async () => {
        const user = userEvent.setup()
        render(<ActivateCard cardId={mockCard.cardId} onClose={() => {}} />)

        const firstCardInput = screen.getByTestId('card-digit-0')

        // Mock clipboard data
        const clipboardData = {
            getData: () => '1234',
        }

        // Trigger paste event
        firstCardInput.focus()
        await user.paste(clipboardData.getData())

        // Verify all inputs are filled
        for (let i = 0; i < 4; i++) {
            expect(screen.getByTestId(`card-digit-${i}`)).toHaveValue(mockCard.lastFour[i])
        }
    })

    it('initializes BasisTheory session on mount', async () => {
        render(<ActivateCard cardId={mockCard.cardId} onClose={() => {}} />)

        // Wait for BT session initialization
        await waitFor(() => {
            expect(screen.getByRole('button', {name: /confirm/i})).not.toBeDisabled()
        })
    })

    it('submits activation with BasisTheory elements', async () => {
        const onClose = vi.fn()
        render(<ActivateCard cardId={mockCard.cardId} onClose={onClose} />)

        // Wait for BT initialization
        await waitFor(() => {
            expect(screen.getByRole('button', {name: /confirm/i})).not.toBeDisabled()
        })

        // ... rest of test
    })
})
