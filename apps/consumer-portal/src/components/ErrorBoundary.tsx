'use client'

import React, {Component, type ReactNode, type ErrorInfo} from 'react'

interface Props {
    fallback: ReactNode | ((error: Error) => ReactNode)
    children: ReactNode
}

interface State {
    error: Error | null
}

export class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props)
        this.state = {error: null}
    }

    static getDerivedStateFromError(error: Error): State {
        return {error}
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // You can log the error to an error reporting service here
        console.error('Error caught by boundary:', error)
        console.error('Error info:', errorInfo)
    }

    render() {
        const {error} = this.state
        const {fallback, children} = this.props

        if (error) {
            return typeof fallback === 'function' ? fallback(error) : fallback
        }

        return children
    }
}

// Example usage:
/*
<ErrorBoundary
  fallback={
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => window.location.reload()}>Refresh Page</button>
    </div>
  }
>
  <YourComponent />
</ErrorBoundary>
*/
