import {describe, it, expect, vi, beforeEach} from 'vitest'
import {render, screen, waitFor} from '@/test-utils'
import userEvent from '@testing-library/user-event'
import Transaction from './Transaction'
import type {
    GetTransactionEvents200ResponseEventsInner,
    GetTransactionEvents200ResponseEventsInnerStateEnum,
} from '@/services/controllers/transactionEvents'
import type {GetDisputes200ResponseDisputesInner, DisputeStatusAll} from '@/services/controllers/disputes'
import {useDispute} from '@/hooks/useDisputeData'
import {useCardData} from '@/hooks/useCardData'
import {useAuthorizedUserData} from '@/hooks/useAuthorizedUserData'
import {useUserData} from '@/hooks/useUserData'
import {useEnrichedTransaction} from '@/hooks/useEnrichedTransaction'

// Mock the hooks used by the component
vi.mock('@/hooks/useDisputeData', () => ({
    useDispute: vi.fn(),
}))

vi.mock('@/hooks/useCardData', () => ({
    useCardData: vi.fn(),
}))

vi.mock('@/hooks/useAuthorizedUserData', () => ({
    useAuthorizedUserData: vi.fn(),
}))

vi.mock('@/hooks/useUserData', () => ({
    useUserData: vi.fn(),
}))

vi.mock('@/hooks/useEnrichedTransaction', () => ({
    useEnrichedTransaction: vi.fn(),
}))

// Helper to expand the transaction row and wait for details
async function expandTransactionRow() {
    const user = userEvent.setup()
    const row = screen.getByLabelText('Transaction Row Button')
    await user.click(row)
    await waitFor(() => expect(screen.getByLabelText('Transaction Row Details')).toBeInTheDocument())
}

describe('Transaction', () => {
    const mockTransaction: GetTransactionEvents200ResponseEventsInner = {
        amount: {
            amount: 4000,
            currency: 'USD',
        },
        authorizedUserId: '8c8ee055-24f0-4cd1-b896-18e53fca9613',
        cardId: '123e4567-e89b-12d3-a456-************',
        creditAccountId: '6bc5adb3-d185-4431-9f86-99815c7daca1',
        eventId: 'evt_123',
        merchantInfo: {
            descriptor: 'Retail Clothing Store',
            locality: 'Washington',
            administrativeArea: 'DC',
            country: 'USA',
            mcc: '1234',
            mid: '***************',
        },
        postedOn: '2019-08-25T14:15:22Z' as unknown as Date,
        state: 'pending' as GetTransactionEvents200ResponseEventsInnerStateEnum,
        transactedOn: '2019-08-24T14:15:22Z' as unknown as Date,
        transactionId: 'tx_123',
        type: 'creditAccount/create',
    }

    // Use a partial mock for the dispute to avoid type issues
    const mockDispute = {
        disputeId: 'disp_123',
        status: 'submitted',
        details: {
            dispute: {
                date: '2023-01-15T10:30:00Z' as unknown as Date,
                amount: {
                    amount: 4000,
                    currency: 'USD',
                },
            },
            reason: 'fraud',
            // We'll skip setting the transaction property since we're not sure of its exact structure
        },
    } as GetDisputes200ResponseDisputesInner

    beforeEach(() => {
        // Default mock implementation - return a partial mock that satisfies the component's requirements
        vi.mocked(useDispute).mockReturnValue({
            data: null,
            isLoading: false,
        } as any) // Using 'any' to bypass TypeScript's strict checking for tests

        // Mock additional hooks
        vi.mocked(useCardData).mockReturnValue({
            data: {
                lastFour: '1234',
                authorizedUserId: '8c8ee055-24f0-4cd1-b896-18e53fca9613',
            },
            isLoading: false,
            isError: false,
        } as any)

        vi.mocked(useAuthorizedUserData).mockReturnValue({
            data: {
                firstName: 'John',
                lastName: 'Doe',
            },
            isLoading: false,
            isError: false,
        } as any)

        vi.mocked(useUserData).mockReturnValue({
            data: {
                firstName: 'Jane',
                lastName: 'Smith',
            },
            isLoading: false,
            isError: false,
        } as any)

        vi.mocked(useEnrichedTransaction).mockReturnValue({
            data: null,
            isLoading: false,
            isError: false,
        } as any)
    })

    it('renders basic transaction information', () => {
        render(<Transaction {...mockTransaction} />)

        expect(screen.getByText('Retail Clothing Store')).toBeInTheDocument()
        expect(screen.getByText(/\$40.00/)).toBeInTheDocument()
        expect(screen.getAllByText('August 24, 2019').length).toBeGreaterThan(0)
    })

    it('renders the outstanding balance on posted transactions', () => {
        const postedTransaction: GetTransactionEvents200ResponseEventsInner = {
            ...mockTransaction,
            state: 'posted' as GetTransactionEvents200ResponseEventsInnerStateEnum,
            outstandingBalance: {amount: 40000, currency: 'USD'},
        }

        render(<Transaction {...postedTransaction} />)

        expect(screen.getByText('$400.00')).toBeInTheDocument()
    })

    describe('Expansion', () => {
        it('expands to show transaction details when clicked', async () => {
            render(<Transaction {...mockTransaction} />)
            await expandTransactionRow()
            expect(screen.getByText(/Washington/)).toBeInTheDocument()
        })

        it('collapses transaction details on second click', async () => {
            render(<Transaction {...mockTransaction} />)
            await expandTransactionRow()
            const transactionRow = screen.getByLabelText('Transaction Row Button')
            const details = screen.getByLabelText('Transaction Row Details')
            await userEvent.click(transactionRow)
            await waitFor(() => {
                expect(details).not.toBeVisible()
            })
        })
    })

    describe('Enriched Transaction', () => {
        it('calls useEnrichedTransaction with transaction and open=true when details are expanded', async () => {
            render(<Transaction {...mockTransaction} />)
            await expandTransactionRow()
            expect(useEnrichedTransaction).toHaveBeenCalledWith(
                expect.objectContaining(mockTransaction), // value-based match
                expect.any(Boolean),
            )
        })

        it('renders enriched transaction fields after expansion', async () => {
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: {cardLastFour: '5678', firstName: 'Alice', lastName: 'Smith'},
                isLoading: false,
                isError: false,
            } as any)
            render(<Transaction {...mockTransaction} />)
            await expandTransactionRow()
            expect(screen.getByText('5678')).toBeInTheDocument()
            expect(screen.getByText(/Alice/i)).toBeInTheDocument()
            expect(screen.getByText(/Smith/i)).toBeInTheDocument()
        })

        it('shows loading indicator when enriched transaction is loading', async () => {
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: null,
                isLoading: true,
                isError: false,
            } as any)
            render(<Transaction {...mockTransaction} />)
            const user = userEvent.setup()
            const row = screen.getByLabelText('Transaction Row Button')
            await user.click(row)
            await waitFor(() => {
                expect(screen.getByRole('progressbar')).toBeInTheDocument()
            })
        })

        it('handles error state from useEnrichedTransaction gracefully', async () => {
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: null,
                isLoading: false,
                isError: true,
            } as any)
            render(<Transaction {...mockTransaction} />)
            const user = userEvent.setup()
            const row = screen.getByLabelText('Transaction Row Button')
            await user.click(row)
            expect(screen.getByText(/Could not load additional transaction details/i)).toBeInTheDocument()
        })

        it('renders correct user info for different cardIds', async () => {
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: {cardLastFour: '9999', firstName: 'Bob', lastName: 'Johnson'},
                isLoading: false,
                isError: false,
            } as any)
            const transactionWithDifferentCard = {
                ...mockTransaction,
                cardId: 'card-9999',
            }
            render(<Transaction {...transactionWithDifferentCard} />)
            await expandTransactionRow()
            expect(screen.getByText('9999')).toBeInTheDocument()
            expect(screen.getByText(/Bob/i)).toBeInTheDocument()
            expect(screen.getByText(/Johnson/i)).toBeInTheDocument()
        })
    })

    describe('Dispute Logic', () => {
        it('renders dispute information when a dispute exists', async () => {
            vi.mocked(useDispute).mockReturnValue({
                data: mockDispute,
                isLoading: false,
            } as any)
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: {cardLastFour: '5678', firstName: 'Alice', lastName: 'Smith'},
                isLoading: false,
                isError: false,
            } as any)
            const transactionWithDispute = {
                ...mockTransaction,
                disputeId: 'disp_123',
            }
            render(<Transaction {...transactionWithDispute} />)
            await expandTransactionRow()
            expect(screen.getByText('Disputed')).toBeInTheDocument()
            expect(screen.getByText('Reason of dispute')).toBeInTheDocument()
            expect(screen.getByText('Dispute Status')).toBeInTheDocument()
            expect(screen.getByText('New')).toBeInTheDocument()
        })

        it('shows correct border color for disputed transactions', () => {
            vi.mocked(useDispute).mockReturnValue({
                data: mockDispute,
                isLoading: false,
            } as any)
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: {cardLastFour: '5678', firstName: 'Alice', lastName: 'Smith'},
                isLoading: false,
                isError: false,
            } as any)
            const transactionWithDispute = {
                ...mockTransaction,
                disputeId: 'disp_123',
            }
            render(<Transaction {...transactionWithDispute} />)
            const container = screen.getByLabelText('Transaction under dispute')
            expect(container).toBeInTheDocument()
        })

        it('does not show FileDispute for transactions with active disputes', async () => {
            vi.mocked(useDispute).mockReturnValue({
                data: mockDispute,
                isLoading: false,
            } as any)
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: {cardLastFour: '5678', firstName: 'Alice', lastName: 'Smith'},
                isLoading: false,
                isError: false,
            } as any)
            const transactionWithDispute = {
                ...mockTransaction,
                state: 'posted' as GetTransactionEvents200ResponseEventsInnerStateEnum,
                disputeId: 'disp_123',
            }
            render(<Transaction {...transactionWithDispute} />)
            await expandTransactionRow()
            expect(screen.queryByText(/Report a Problem/i)).not.toBeInTheDocument()
        })

        it('shows FileDispute for transactions with closed or failed disputes', async () => {
            vi.mocked(useDispute).mockReturnValue({
                data: {
                    ...mockDispute,
                    status: 'closed' as DisputeStatusAll,
                },
                isLoading: false,
            } as any)
            vi.mocked(useEnrichedTransaction).mockReturnValue({
                data: {cardLastFour: '5678', firstName: 'Alice', lastName: 'Smith'},
                isLoading: false,
                isError: false,
            } as any)
            const transactionWithDispute = {
                ...mockTransaction,
                state: 'posted' as GetTransactionEvents200ResponseEventsInnerStateEnum,
                disputeId: 'disp_123',
            }
            render(<Transaction {...transactionWithDispute} />)
            await expandTransactionRow()
            expect(screen.queryByText(/Report a Problem/i)).toBeInTheDocument()
        })

        it('displays different dispute status chips based on status', async () => {
            const statuses: DisputeStatusAll[] = ['closed', 'failed', 'lost', 'prepared', 'submitted', 'won', 'created']
            for (const status of statuses) {
                vi.mocked(useDispute).mockReturnValue({
                    data: {
                        ...mockDispute,
                        status,
                    },
                    isLoading: false,
                } as any)
                vi.mocked(useEnrichedTransaction).mockReturnValue({
                    data: {cardLastFour: '5678', firstName: 'Alice', lastName: 'Smith'},
                    isLoading: false,
                    isError: false,
                } as any)
                const transactionWithDispute = {
                    ...mockTransaction,
                    disputeId: 'disp_123',
                }
                const {unmount} = render(<Transaction {...transactionWithDispute} />)
                await expandTransactionRow()
                let chipText = ''
                if (['prepared', 'submitted', 'created'].includes(status)) {
                    chipText = 'New'
                } else if (status === 'closed') {
                    chipText = 'Closed'
                } else if (status === 'failed') {
                    chipText = 'Error'
                } else if (status === 'lost') {
                    chipText = 'Lost'
                } else if (status === 'won') {
                    chipText = 'Won'
                }
                expect(screen.getByText(chipText)).toBeInTheDocument()
                unmount()
            }
        })
    })
})
