'use client'

import {Box, styled, Icon} from '@mui/material'

interface StepperProps {
    steps: number
    currentStep: number
}

const StepCircle = styled(Box, {
    shouldForwardProp: prop => !['isActive', 'isCompleted', 'isFirst', 'isLast'].includes(prop as string),
})<{
    isActive?: boolean
    isCompleted?: boolean
    isFirst?: boolean
    isLast?: boolean
}>(({theme, isActive, isCompleted, isFirst, isLast}) => ({
    width: 40,
    height: 40,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: isActive
        ? theme.palette.primary.main
        : isCompleted
          ? theme.palette.grey[100]
          : theme.palette.grey[200],
    color: isActive ? theme.palette.primary.contrastText : theme.palette.text.primary,
    fontWeight: 600,
    position: 'relative',
    zIndex: 1,
    marginLeft: isFirst ? 0 : theme.spacing(1),
    marginRight: isLast ? 0 : theme.spacing(1),
}))

const StepConnector = styled(Box, {
    shouldForwardProp: prop => prop !== 'isActive',
})<{
    isActive?: boolean
}>(({theme, isActive}) => ({
    flex: 1,
    height: 2,
    backgroundColor: isActive ? theme.palette.primary.main : theme.palette.grey[200],
    transition: theme.transitions.create('background-color'),
}))

export default function Stepper({steps, currentStep}: StepperProps) {
    return (
        <Box
            sx={{
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                position: 'relative',
            }}
        >
            {Array.from({length: steps}).map((_, index) => (
                <Box key={index} sx={{display: 'flex', alignItems: 'center', flex: index === steps - 1 ? 0 : 1}}>
                    <StepCircle
                        isActive={currentStep === index + 1}
                        isCompleted={currentStep > index + 1}
                        isFirst={index === 0}
                        isLast={index === steps - 1}
                    >
                            {currentStep > index + 1 ? (
                                <Icon baseClassName="fas" className="fa-solid fa-check" fontSize="inherit" />
                            ) : (
                            index + 1
                        )}
                    </StepCircle>
                    {index < steps - 1 && <StepConnector isActive={currentStep === index + 1} />}
                </Box>
            ))}
        </Box>
    )
}
