'use client'

import {QueryClient, QueryClientProvider} from '@tanstack/react-query'
import {ReactQueryDevtools} from '@tanstack/react-query-devtools'
import type {GetCreditCards200Response} from '@/services/controllers/cards'
import type {GetAuthorizedUsers200Response} from '@/services/controllers/authorizedUsers'
import type {GetPaymentMethods200Response, GetPayments200Response} from '@/services/controllers/payments'
import {useState, type ReactNode} from 'react'
import type {UserDetails} from '@/data'

interface DataCacheProps {
    children: ReactNode
    user?: UserDetails
    cards?: GetCreditCards200Response
    authorizedUsers?: GetAuthorizedUsers200Response
    paymentMethods?: GetPaymentMethods200Response
    payments?: GetPayments200Response
}

export function DataCache({children, user, cards, authorizedUsers, paymentMethods, payments}: DataCacheProps) {
    const [queryClient] = useState(() => {
        const client = new QueryClient()

        if (user) {
            client.setQueryData(['user'], user)
        }

        if (cards) {
            client.setQueryData(['cards'], cards)
            cards.cards?.forEach(card => {
                client.setQueryData(['cards', card.cardId], card)
            })
        }

        if (authorizedUsers) {
            client.setQueryData(['authorizedUsers'], authorizedUsers)
            authorizedUsers.authorizedUsers?.forEach(authorizedUser => {
                client.setQueryData(['authorizedUsers', authorizedUser.authorizedUserId], authorizedUser)
            })
        }

        if (paymentMethods) {
            client.setQueryData(['paymentMethods'], paymentMethods)
            paymentMethods.paymentMethods?.forEach(paymentMethod => {
                client.setQueryData(['paymentMethods', paymentMethod.paymentMethodId], paymentMethod)
            })
        }

        if (payments) {
            client.setQueryData(['payments'], payments)
            payments.payments?.forEach(payment => {
                client.setQueryData(['payments', payment.paymentId], payment)
            })
        }
        return client
    })

    return (
        <QueryClientProvider client={queryClient}>
            {children}
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    )
}
