'use client'

import {createContext, useEffect, useState, useCallback} from 'react'
import type {ReactNode} from 'react'
import {signOut} from 'next-auth/react'

import {<PERSON>ton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions} from '@mui/material'
import {datadogRum} from '@datadog/browser-rum'

const SessionContext = createContext({})

const FULL_SESSION_TIMER = 29 * 60 * 1000
const NOTIFICATION_SESSION_TIMER = 1 * 60 * 1000

let TIMER_ID: NodeJS.Timeout
let MODAL_TIMER_ID: NodeJS.Timeout

export const SessionTimeoutProvider = ({children}: Readonly<{children: ReactNode}>) => {
    const [openTimerModal, setOpenTimerModal] = useState(false)

    const startTimer = useCallback(() => {
        clearTimeout(MODAL_TIMER_ID)

        // Start the long timer. This is the full session timer. Once expired it will show the expiration modal.
        MODAL_TIMER_ID = setTimeout(() => {
            if (!openTimerModal) {
                setOpenTimerModal(true)
            }
        }, FULL_SESSION_TIMER)
    }, [openTimerModal])

    const handleLogout = () => {
        try {
            datadogRum.addAction('Session Expired')
            datadogRum.stopSession()
            datadogRum.clearUser()
        } catch (error) {
            console.error('Error clearing Datadog RUM session:', error)
        }
        signOut({
            redirect: true,
            callbackUrl: '/login?c=SessionExpired',
        })
    }

    const continueSession = () => {
        setOpenTimerModal(false)
        clearTimeout(TIMER_ID)
        startTimer()
    }

    useEffect(() => {
        startTimer()

        return () => {
            clearTimeout(TIMER_ID)
            clearTimeout(MODAL_TIMER_ID)
        }
    }, [startTimer, openTimerModal])

    useEffect(() => {
        if (openTimerModal) {
            // Start second timer once the main timer has executed
            TIMER_ID = setTimeout(() => {
                handleLogout()
            }, NOTIFICATION_SESSION_TIMER)
        }

        return () => {
            clearTimeout(TIMER_ID)
            clearTimeout(MODAL_TIMER_ID)
        }
    }, [openTimerModal])

    return (
        <SessionContext.Provider value={{}}>
            {children}
            <SessionTimerModal open={openTimerModal} continueSession={continueSession} logout={handleLogout} />
        </SessionContext.Provider>
    )
}

interface SessionTimerModalProps {
    open: boolean
    continueSession: () => void
    logout: () => void
}

function SessionTimerModal({open, continueSession, logout}: SessionTimerModalProps) {
    return (
        <Dialog open={open}>
            <DialogTitle>Are you still there?</DialogTitle>
            <DialogContent>
                <DialogContentText>
                    Your session is about to expire would you like to remain logged in?
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={logout}>Logout</Button>
                <Button onClick={continueSession}>Continue Session</Button>
            </DialogActions>
        </Dialog>
    )
}
