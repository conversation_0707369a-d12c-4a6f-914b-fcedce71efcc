import {describe, it, expect} from 'vitest'
import {render, screen, renderHook, act} from '@testing-library/react'
import UXActionsProvider, {useUXActions} from './UXActions'

describe('UXActionsProvider', () => {
    it('provides drawer states to children', () => {
        const TestComponent = () => {
            const {leftDrawerOpen, rightDrawerOpen} = useUXActions()
            return (
                <div>
                    <div data-testid="left-drawer-state">{leftDrawerOpen.toString()}</div>
                    <div data-testid="right-drawer-state">{rightDrawerOpen.toString()}</div>
                </div>
            )
        }

        render(
            <UXActionsProvider>
                <TestComponent />
            </UXActionsProvider>,
        )

        expect(screen.getByTestId('left-drawer-state')).toHaveTextContent('false')
        expect(screen.getByTestId('right-drawer-state')).toHaveTextContent('false')
    })

    it('allows updating drawer states', () => {
        const {result} = renderHook(() => useUXActions(), {
            wrapper: ({children}) => <UXActionsProvider>{children}</UXActionsProvider>,
        })

        // Initially both drawers should be closed
        expect(result.current.leftDrawerOpen).toBe(false)
        expect(result.current.rightDrawerOpen).toBe(false)

        // Open left drawer
        act(() => {
            result.current.setLeftDrawerState(true)
        })
        expect(result.current.leftDrawerOpen).toBe(true)

        // Open right drawer
        act(() => {
            result.current.setRightDrawerState(true)
        })
        expect(result.current.rightDrawerOpen).toBe(true)

        // Close both drawers
        act(() => {
            result.current.setLeftDrawerState(false)
            result.current.setRightDrawerState(false)
        })
        expect(result.current.leftDrawerOpen).toBe(false)
        expect(result.current.rightDrawerOpen).toBe(false)
    })
})
