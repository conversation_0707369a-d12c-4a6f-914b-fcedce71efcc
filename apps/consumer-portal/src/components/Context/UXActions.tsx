'use client'

import {createContext, useContext, useState, useMemo, type Dispatch, type SetStateAction} from 'react'

interface UXActionsContextType {
    leftDrawerOpen: boolean
    rightDrawerOpen: boolean
    setLeftDrawerState: Dispatch<SetStateAction<boolean>>
    setRightDrawerState: Dispatch<SetStateAction<boolean>>
}

// Create context with a default value matching the interface
const UXActionsContext = createContext<UXActionsContextType>({
    leftDrawerOpen: false,
    rightDrawerOpen: false,
    setLeftDrawerState: () => undefined,
    setRightDrawerState: () => undefined,
})

export default function UXActionsProvider({children}: {children: React.ReactNode}) {
    const [leftDrawerState, setLeftDrawerState] = useState(false)
    const [rightDrawerState, setRightDrawerState] = useState(false)

    const value = useMemo(
        () => ({
            leftDrawerOpen: leftDrawerState,
            rightDrawerOpen: rightDrawerState,
            setLeftDrawerState,
            setRightDrawerState,
        }),
        [leftDrawerState, rightDrawerState],
    )

    return <UXActionsContext.Provider value={value}>{children}</UXActionsContext.Provider>
}

// Custom hook to use the UX actions context
export function useUXActions() {
    const context = useContext(UXActionsContext)

    if (!context) {
        throw new Error('useUXActions must be used within a UXActionsProvider')
    }

    return context
}
