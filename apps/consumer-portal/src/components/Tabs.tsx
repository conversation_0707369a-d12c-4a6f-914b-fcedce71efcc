'use client'

import {Box, Grid, Tabs as MUITabs, Tab, Paper} from '@mui/material'
import {SyntheticEvent, useState} from 'react'

interface TabPanelProps {
    children?: React.ReactNode
    panelName: string
    value: string
}
function TabPanel(props: TabPanelProps) {
    return (
        <Paper
            role="tabpanel"
            hidden={props.value !== props.panelName}
            id={`tabpanel-${props.panelName}`}
            aria-labelledby={`tab-${props.panelName}`}
            style={{
                display: props.value !== props.panelName ? 'none' : 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                gap: 8,
                alignSelf: 'stretch',
            }}
        >
            {props.value === props.panelName && props.children}
        </Paper>
    )
}

function a11yProps(panel: string) {
    return {
        id: `tab-${panel}`,
        'aria-controls': `tabpanel-${panel}`,
    }
}

interface TabsProps {
    tabs: Array<{node: React.ReactNode; id: string; label: string}>
}

export const Tabs = ({tabs}: TabsProps) => {
    const [selectedTab, setSelectedTab] = useState(tabs[0].id)

    function handleChangeTab(event: SyntheticEvent, newValue: string) {
        setSelectedTab(newValue)
    }

    return (
        <Grid container flexDirection="column" alignItems="center" alignSelf="stretch">
            <MUITabs
                onChange={handleChangeTab}
                value={selectedTab}
                centered
                aria-label="Action Tabs"
                sx={{
                    justifyContent: 'space-between',
                    alignItems: 'flex-end',
                    alignSelf: 'stretch',
                    '& .MuiTabs-indicator': {
                        backgroundColor: 'transparent',
                    },
                    borderBottom: theme => `2px solid ${theme.palette.text.primary}`,
                }}
            >
                {tabs.map(tab => {
                    return (
                        <Tab
                            key={tab.id}
                            label={tab.label}
                            value={tab.id}
                            id={`tab-${tab.id}`}
                            sx={{
                                flex: 1,
                                '&.Mui-selected': {
                                    background: 'var(--mui-palette-secondary-dark)',
                                    color: theme => theme.palette.common.white,
                                },
                                textTransform: 'none',
                                transition: theme =>
                                    theme.transitions.create(['background-color'], {
                                        duration: theme.transitions.duration.standard,
                                    }),
                                borderTopLeftRadius: theme => theme.shape.borderRadius * 2,
                                borderTopRightRadius: theme => theme.shape.borderRadius * 2,
                            }}
                        />
                    )
                })}
            </MUITabs>
            {tabs.map(tab => {
                return (
                    <TabPanel key={tab.id} value={selectedTab} panelName={tab.id} {...a11yProps(tab.id)}>
                        {tab.node}
                    </TabPanel>
                )
            })}
        </Grid>
    )
}
