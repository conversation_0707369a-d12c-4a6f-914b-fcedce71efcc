'use client'

import {Grid, Typography, Skeleton} from '@mui/material'
import {useUserData} from '@/hooks/useUserData'

export default function WelcomeBanner({children}: {children?: Readonly<React.ReactNode>}) {
    const {data: user, isLoading} = useUserData()

    if (isLoading) {
        return <LoadingWelcomeBanner />
    }

    if (!user) {
        return null
    }

    return (
        <>
            <Grid
                container
                justifyContent="space-between"
                alignItems="center"
                sx={{pt: '12px', pr: 0, pb: 1, pl: 1}}
                role="banner"
            >
                <Grid container alignItems="center" flex={1}>
                    <Typography fontSize={20} fontWeight={600} lineHeight="24px" textTransform="uppercase" flex={1}>
                        Hi {user.firstName}!
                    </Typography>

                    {children}
                </Grid>
            </Grid>
        </>
    )
}

export function LoadingWelcomeBanner() {
    return (
        <Grid container justifyContent="space-between" alignItems="center" sx={{pt: 1.5, pb: 1}}>
            <Typography fontSize={20} sx={{width: '100%'}}>
                <Skeleton />
            </Typography>
        </Grid>
    )
}
