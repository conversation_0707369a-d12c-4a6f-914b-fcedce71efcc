import {Avatar, ListItemButton, ListItemAvatar, ListItemText, type TypographyTypeMap, Icon} from '@mui/material'
import type {ReactElement, ReactNode} from 'react'

export interface TableListItemProps {
    leftPrimaryText: ReactElement<TypographyTypeMap> | string
    leftSecondaryText?: ReactElement<TypographyTypeMap> | string
    rightPrimaryText: ReactElement<TypographyTypeMap> | string
    rightSecondaryText?: ReactElement<TypographyTypeMap> | string

    onClick?: () => void
}

export const TableListItem = ({
    leftPrimaryText,
    leftSecondaryText,
    rightPrimaryText,
    rightSecondaryText,
    onClick,
}: TableListItemProps) => {
    return (
        <ListItemButton divider disableGutters onClick={onClick} sx={{px: 2}}>
            <ListItemAvatar>
                <Avatar>
                    <Icon baseClassName="fas" className="fa-solid fa-file-pdf" fontSize="small" />
                </Avatar>
            </ListItemAvatar>
            <ListItemText
                primary={leftPrimaryText}
                secondary={leftSecondaryText}
                primaryTypographyProps={{fontWeight: 600}}
                secondaryTypographyProps={{color: 'var(--mui-palette-text-secondary)'}}
            />
            <ListItemText
                primary={rightPrimaryText}
                secondary={rightSecondaryText}
                sx={{justifyItems: 'end'}}
                primaryTypographyProps={{fontWeight: 600}}
            />
            <Icon baseClassName="fas" className="fa-solid fa-chevron-right" fontSize="small" sx={{ml: 1}} />
        </ListItemButton>
    )
}
