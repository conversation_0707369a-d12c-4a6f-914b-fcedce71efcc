import {describe, it, expect} from 'vitest'
import {render, screen} from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import {Tabs} from './Tabs'

describe('Tabs', () => {
    const mockTabs = [
        {
            node: <div>First Tab Content</div>,
            id: 'first_tab',
            label: 'First Tab',
        },
        {
            node: <div>Second Tab Content</div>,
            id: 'second_tab',
            label: 'Second Tab',
        },
    ]

    it('renders all tab labels', () => {
        render(<Tabs tabs={mockTabs} />)

        expect(screen.getByText('First Tab')).toBeInTheDocument()
        expect(screen.getByText('Second Tab')).toBeInTheDocument()
    })

    it('shows first tab content by default', () => {
        render(<Tabs tabs={mockTabs} />)

        expect(screen.getByText('First Tab Content')).toBeInTheDocument()
        expect(screen.queryByText('Second Tab Content')).not.toBeInTheDocument()
    })

    it('switches content when clicking different tab', async () => {
        const user = userEvent.setup()
        render(<Tabs tabs={mockTabs} />)

        // Click second tab
        const secondTab = screen.getByRole('tab', {name: /second tab/i})
        await user.click(secondTab)

        // First tab content should be hidden
        expect(screen.queryByText('First Tab Content')).not.toBeInTheDocument()
        // Second tab content should be visible
        expect(screen.getByText('Second Tab Content')).toBeInTheDocument()
    })

    it('maintains proper ARIA attributes', () => {
        render(<Tabs tabs={mockTabs} />)

        // Check panel attributes
        const panels = screen.getAllByRole('tabpanel', {hidden: true})

        expect(panels[0]).toHaveAttribute('aria-labelledby', 'tab-first_tab')
        expect(panels[1]).toHaveAttribute('aria-labelledby', 'tab-second_tab')
    })

    it('shows correct tab as selected', async () => {
        const user = userEvent.setup()
        render(<Tabs tabs={mockTabs} />)

        // First tab should be selected by default
        const firstTab = screen.getByRole('tab', {name: /first tab/i})
        expect(firstTab).toHaveAttribute('aria-selected', 'true')

        // Click second tab
        const secondTab = screen.getByRole('tab', {name: /second tab/i})
        await user.click(secondTab)

        // Check selection state
        expect(firstTab).toHaveAttribute('aria-selected', 'false')
        expect(secondTab).toHaveAttribute('aria-selected', 'true')

        expect(screen.queryByText('First Tab Content')).not.toBeInTheDocument()
        expect(screen.queryByText('Second Tab Content')).toBeInTheDocument()
    })

    it('renders complex content in tabs', () => {
        const complexTabs = [
            {
                node: (
                    <div>
                        <h2>Complex Content</h2>
                        <p>With multiple elements</p>
                        <button>Action Button</button>
                    </div>
                ),
                id: 'complex_tab',
                label: 'Complex Tab',
            },
            {
                node: <div>Simple Tab</div>,
                id: 'simple_tab',
                label: 'Simple Tab',
            },
        ]

        render(<Tabs tabs={complexTabs} />)

        // Complex content should be visible by default
        expect(screen.getByRole('heading', {name: 'Complex Content'})).toBeInTheDocument()
        expect(screen.getByText('With multiple elements')).toBeInTheDocument()
        expect(screen.getByRole('button', {name: 'Action Button'})).toBeInTheDocument()
    })
})
