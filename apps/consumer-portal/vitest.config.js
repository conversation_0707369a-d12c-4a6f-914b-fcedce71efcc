import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
    plugins: [react()],
    test: {
        environment: 'jsdom',
        setupFiles: ['./src/test-utils.tsx'],
        globals: true,
        exclude: ['**/e2e/**', '**/node_modules/**'],
        alias: {
            '@': resolve(__dirname, './src'),
        },
        typecheck: {
            enabled: false,
        },
    },
    resolve: {
        conditions: ['import', 'node', 'browser'],
    },
})
