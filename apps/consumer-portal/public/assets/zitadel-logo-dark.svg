<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 295 81" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,0,-107)">
        <g id="zitadel-logo-dark" transform="matrix(1,0,0,1,-20.9181,18.2562)">
            <rect x="20.918" y="89.57" width="294.943" height="79.632" style="fill:none;"/>
            <g transform="matrix(2.73916,0,0,1.55095,-35271.3,23.6234)">
                <g transform="matrix(0.160219,-0.0758207,0.0429306,0.282967,12622.8,-105.843)">
                    <path d="M1493.5,1056.38L1493.5,1037L1496.5,1037L1496.5,1061.62L1426.02,1020.38L1496.5,979.392L1496.5,1004L1493.5,1004L1493.5,984.608L1431.98,1020.39L1493.5,1056.38Z" style="fill:rgb(16,16,16);"/>
                </g>
                <g>
                    <g transform="matrix(-0.0429306,-0.282967,0.160219,-0.0758207,12884.5,137.392)">
                        <path d="M212.517,110L200.392,110L190,92L179.608,110L167.483,110L190,71L212.517,110Z" style="fill:url(#_Linear1);"/>
                    </g>
                    <g transform="matrix(0.160219,0.0758207,-0.0429306,0.282967,12878.9,10.8747)">
                        <path d="M212.517,110L200.392,110L190,92L179.608,110L167.483,110L190,71L212.517,110Z" style="fill:url(#_Linear2);"/>
                    </g>
                    <g transform="matrix(-0.117289,0.207146,-0.117289,-0.207146,12943.8,65.7)">
                        <path d="M212.517,110L200.392,110L190,92L179.608,110L167.483,110L190,71L212.517,110Z" style="fill:url(#_Linear3);"/>
                    </g>
                    <g transform="matrix(-0.160219,-0.0758207,0.0429306,-0.282967,12917.4,132.195)">
                        <path d="M139.622,117L149,142L130.244,142L139.622,117Z" style="fill:url(#_Linear4);"/>
                    </g>
                    <g transform="matrix(-0.117289,0.207146,0.117289,0.207146,12897.8,5.87512)">
                        <path d="M139.622,117L149,142L130.244,142L139.622,117Z" style="fill:url(#_Linear5);"/>
                    </g>
                    <g transform="matrix(-0.0429306,-0.282967,-0.160219,0.0758207,12936.8,97.6441)">
                        <path d="M139.622,117L149,142L130.244,142L139.622,117Z" style="fill:url(#_Linear6);"/>
                    </g>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0429306,0.282967,12622.6,-105.767)">
                    <circle cx="1496" cy="1004" r="7" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0429306,0.282967,12624.1,-96.4295)">
                    <circle cx="1496" cy="1004" r="7" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0429306,0.282967,12625,-90.2042)">
                    <circle cx="1496" cy="1004" r="7" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0429306,0.282967,12612.8,-96.1272)">
                    <circle cx="1496" cy="1004" r="7" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0429306,0.282967,12621.7,-111.993)">
                    <circle cx="1496" cy="1004" r="7" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0375643,0.247596,12637.2,-10.9628)">
                    <path d="M1499.26,757.787C1499.26,757.787 1497.37,756.489 1497,755.2C1496.71,754.182 1496.57,750.662 1496.54,750C1496.41,747.303 1499.21,745.644 1499.21,745.644L1490.01,745.835C1490.01,745.835 1493.15,745.713 1493.46,750C1493.51,750.661 1493.23,753.476 1493,755.2C1492.91,756.447 1491.2,757.668 1491.2,757.668L1499.26,757.787Z" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0375643,0.247596,12639.5,4.60032)">
                    <path d="M1495,760L1495,744" style="fill:none;"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0375643,0.247596,12639.5,4.60032)">
                    <path d="M1498.27,757.077C1498.27,757.077 1496.71,756.46 1496.65,754.8C1496.65,753.658 1496.64,753.281 1496.65,752.016C1496.62,751.334 1496.59,750.608 1496.65,749.949C1496.78,746.836 1498.5,746.156 1498.5,746.156L1491.46,745.931C1491.46,745.931 1493.37,746.719 1493.65,749.83C1493.71,750.489 1493.69,751.528 1493.65,752.209C1493.64,753.331 1493.64,753.413 1493.65,754.518C1493.68,756.334 1492.58,756.827 1492.58,756.827L1498.27,757.077Z" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0375643,0.247596,12627.7,-0.733956)">
                    <path d="M1496.17,759.473L1555.54,720.014" style="fill:none;"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,0.0375643,0.247596,12627.7,-0.733956)">
                    <path d="M1500.86,762.056C1500.86,762.056 1499.86,760.4 1503.09,757.456C1504.91,755.797 1507.33,754.151 1509.98,752.255C1514.82,748.79 1520.68,744.94 1526.52,741.049C1531.45,737.766 1536.38,734.479 1540.82,731.68C1544.52,729.349 1547.85,727.296 1550.54,725.8C1551.07,725.506 1551.6,725.329 1552.05,725.029C1554.73,723.257 1556.85,724.968 1556.85,724.968L1552.23,716.282C1552.23,716.282 1551.99,719.454 1550,720.997C1549.57,721.333 1549.15,721.741 1548.67,722.12C1546.2,724.053 1542.99,726.344 1539.39,728.867C1535.06,731.898 1530.13,735.166 1525.19,738.438C1519.35,742.314 1513.52,746.234 1508.49,749.329C1505.74,751.023 1503.28,752.577 1501.13,753.598C1497.99,755.086 1495.28,753.617 1495.28,753.617L1500.86,762.056Z" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,-0.0375643,-0.247596,12684.9,376.33)">
                    <path d="M1496.17,759.473L1555.54,720.014" style="fill:none;"/>
                </g>
                <g transform="matrix(0.160219,-0.0758207,-0.0375643,-0.247596,12684.9,376.33)">
                    <path d="M1496.1,754.362C1496.1,754.362 1497.2,755.607 1501.13,753.598C1503.25,752.509 1505.74,751.023 1508.49,749.329C1513.52,746.234 1519.35,742.314 1525.19,738.438C1530.13,735.166 1534.94,731.832 1539.27,728.802C1542.87,726.279 1549.36,722.059 1549.81,721.75C1552.75,719.73 1552.18,718.196 1552.18,718.196L1555.28,724.152C1555.28,724.152 1553.77,722.905 1551.37,724.681C1550.93,725.006 1544.52,729.349 1540.82,731.68C1536.38,734.479 1531.45,737.766 1526.52,741.049C1520.68,744.94 1514.82,748.79 1509.98,752.255C1507.33,754.151 1504.89,755.771 1503.09,757.456C1499.47,760.841 1501.26,763.283 1501.26,763.283L1496.1,754.362Z" style="fill:rgb(16,16,16);"/>
                </g>
                <g transform="matrix(0.156811,0,0,0.230771,12477,-400.567)">
                    <g transform="matrix(94.7456,0,0,94.7456,2811.73,2063)">
                        <path d="M0.449,-0.7L0.177,-0.7C0.185,-0.682 0.197,-0.654 0.2,-0.648C0.205,-0.639 0.216,-0.628 0.239,-0.628L0.32,-0.628C0.332,-0.628 0.336,-0.62 0.334,-0.611L0.128,-0L0.389,-0C0.412,-0 0.422,-0.01 0.427,-0.02L0.45,-0.071L0.255,-0.071C0.245,-0.071 0.239,-0.078 0.242,-0.09L0.449,-0.7Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(94.7456,0,0,94.7456,2882.79,2063)">
                        <path d="M0.214,-0.7L0.214,-0.015C0.215,-0.01 0.218,-0 0.235,-0L0.286,-0L0.286,-0.672C0.286,-0.684 0.278,-0.7 0.257,-0.7L0.214,-0.7Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(94.7456,0,0,94.7456,2944.37,2063)">
                        <path d="M0.441,-0.7L0.155,-0.7C0.143,-0.7 0.133,-0.69 0.133,-0.678L0.133,-0.629L0.234,-0.629L0.234,-0.015C0.234,-0.01 0.237,-0 0.254,-0L0.305,-0L0.305,-0.612C0.306,-0.621 0.313,-0.629 0.323,-0.629L0.379,-0.629C0.402,-0.629 0.413,-0.639 0.417,-0.648L0.441,-0.7Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(94.7456,0,0,94.7456,3010.69,2063)">
                        <path d="M0.422,-0L0.343,-0L0.28,-0.482L0.217,-0L0.138,-0L0.244,-0.7L0.283,-0.7C0.313,-0.7 0.318,-0.681 0.321,-0.662L0.422,-0Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(94.7456,0,0,94.7456,3077.96,2063)">
                        <path d="M0.186,-0.7L0.186,-0L0.325,-0C0.374,-0 0.413,-0.039 0.414,-0.088L0.414,-0.612C0.413,-0.661 0.374,-0.7 0.325,-0.7L0.186,-0.7ZM0.343,-0.108C0.343,-0.081 0.325,-0.071 0.305,-0.071L0.258,-0.071L0.258,-0.628L0.305,-0.628C0.325,-0.628 0.343,-0.618 0.343,-0.592L0.343,-0.108Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(94.7456,0,0,94.7456,3149.02,2063)">
                        <path d="M0.291,-0.071L0.291,-0.314C0.291,-0.323 0.299,-0.331 0.308,-0.331L0.338,-0.331C0.361,-0.331 0.371,-0.341 0.376,-0.35C0.379,-0.356 0.391,-0.385 0.399,-0.403L0.291,-0.403L0.291,-0.611C0.291,-0.621 0.298,-0.628 0.308,-0.628L0.366,-0.628C0.389,-0.628 0.4,-0.639 0.404,-0.648L0.428,-0.7L0.241,-0.7C0.229,-0.7 0.22,-0.691 0.219,-0.68L0.219,-0L0.379,-0C0.402,-0 0.413,-0.01 0.418,-0.019C0.421,-0.025 0.433,-0.053 0.441,-0.071L0.291,-0.071Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                    <g transform="matrix(94.7456,0,0,94.7456,3220.08,2063)">
                        <path d="M0.283,-0.071L0.283,-0.678C0.283,-0.69 0.273,-0.699 0.261,-0.7L0.211,-0.7L0.211,-0L0.383,-0C0.406,-0 0.417,-0.01 0.422,-0.019C0.425,-0.025 0.437,-0.053 0.445,-0.071L0.283,-0.071Z" style="fill:rgb(16,16,16);fill-rule:nonzero;"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-41.5984,155.247,-155.247,-41.5984,201.516,76.8392)"><stop offset="0" style="stop-color:rgb(255,143,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(254,0,255);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(155.247,-41.5984,41.5984,155.247,110.08,195.509)"><stop offset="0" style="stop-color:rgb(255,143,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(254,0,255);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-113.649,-113.649,113.649,-113.649,258.31,215.618)"><stop offset="0" style="stop-color:rgb(255,143,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(254,0,255);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear4" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-155.247,41.5984,-41.5984,-155.247,220.914,144.546)"><stop offset="0" style="stop-color:rgb(255,143,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(254,0,255);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear5" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-113.649,113.649,113.649,113.649,206.837,124.661)"><stop offset="0" style="stop-color:rgb(255,143,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(254,0,255);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear6" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-41.5984,-155.247,-155.247,41.5984,152.054,262.8)"><stop offset="0" style="stop-color:rgb(255,143,0);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(254,0,255);stop-opacity:1"/></linearGradient>
    </defs>
</svg>
