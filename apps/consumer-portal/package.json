{"name": "@tallied-technologies/consumer-portal", "type": "module", "version": "0.1.0", "private": true, "scripts": {"dev": "local-domain-info && next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "dependencies": {"@basis-theory/basis-theory-react": "^1.32.3", "@datadog/browser-rum": "^6.8.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/lab": "^6.0.0-beta.9", "@mui/material": "^7.0.2", "@mui/material-nextjs": "^7.0.2", "@mui/x-date-pickers": "^7.24.1", "@mxenabled/web-widget-sdk": "^0.0.13", "@tallied-technologies/assets": "*", "@tallied-technologies/common": "*", "@tallied-technologies/component-library": "*", "@tallied-technologies/services": "*", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.71.1", "dayjs": "^1.11.13", "dd-trace": "^5.53.0", "next": "^15.2.4", "next-auth": "^5.0.0-beta.25", "react": "^18.3.1", "react-dom": "^18", "server-only": "^0.0.1", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.49.1", "@rollup/rollup-darwin-arm64": "^4.43.0", "@tallied-technologies/eslint-config": "*", "@tallied-technologies/scripts": "*", "@tallied-technologies/types": "*", "@tallied-technologies/typescript-config": "*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.0.6", "eslint": "^8", "eslint-config-next": "^15.0.3", "jsdom": "^26.0.0", "pdf-parse": "^1.1.1", "prettier": "^3.4.2", "typescript": "^5", "vite": "^6.2.5", "vitest": "^3.0.6"}}