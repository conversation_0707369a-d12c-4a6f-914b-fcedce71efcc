'use client'
import {Poppins} from 'next/font/google'
import {createTheme} from '@mui/material/styles'
import {programTheme} from '@tallied-technologies/ui-themes'

const poppins = Poppins({
    weight: ['300', '400', '500', '600', '700'],
    subsets: ['latin'],
    display: 'swap',
    preload: true,
})

const productTheme = programTheme('tallied')

const combinedTheme = createTheme({
    cssVariables: true,
    ...productTheme,
    typography: {
        fontFamily: poppins.style.fontFamily,
    },
})

export {combinedTheme as theme}
