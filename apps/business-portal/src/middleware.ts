import {NextResponse} from 'next/server'
import type {NextRequest} from 'next/server'

export function middleware(req: NextRequest) {
    const requestHeaders = new Headers(req.headers)

    // Read and rip the headers to determine the brand or organization
    requestHeaders.set('x-foo', 'bar')

    return NextResponse.next({
        request: {
            headers: requestHeaders,
        },
    })
}
