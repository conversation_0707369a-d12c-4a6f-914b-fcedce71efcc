'use client'

import {styled} from '@mui/material/styles'

const ContentOuterWrapperStyled = styled('div')(({theme}) => ({
    display: 'flex',
    flex: '1',
    justifyContent: 'center',
    minHeight: 600,
    backgroundColor: theme.palette.background.default,
}))

export default function ContentOuterWrapper({children}: {children: React.ReactNode}) {
    return <ContentOuterWrapperStyled>{children}</ContentOuterWrapperStyled>
}
