'use client'

import {Box} from '@mui/material'
import {styled} from '@mui/material/styles'

const ContentWrapperStyled = styled(Box)(({theme}) => ({
    display: 'flex',
    flexWrap: 'nowrap',
    margin: theme.spacing(6, 2),
    gap: theme.spacing(2),
    width: '100%',
    maxWidth: 1440,
    [theme.breakpoints.down('lg')]: {
        flexWrap: 'wrap',
        margin: theme.spacing(2, 2),
    },
}))

export default function ContentWrapper({children}: {children: React.ReactNode}) {
    return <ContentWrapperStyled>{children}</ContentWrapperStyled>
}
