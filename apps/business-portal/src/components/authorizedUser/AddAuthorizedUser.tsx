'use client'

import {Add, Close} from '@mui/icons-material'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    DialogContent,
    DialogT<PERSON>le,
    <PERSON>vider,
    IconButton,
    Slide,
    Stepper,
    Step,
    StepButton,
    Box,
    Typography,
    TextField,
} from '@mui/material'
import type {Theme} from '@mui/material'
import useMediaQuery from '@mui/material/useMediaQuery'
import {TransitionProps} from '@mui/material/transitions'
import {forwardRef, useState} from 'react'
import type {ChangeEvent, ReactElement, Ref} from 'react'

// Really clunky api on MUI's side for this one. I would expect the TransitionComponent to just take in a ReactElement and run
// But there's an explicit need for children on the type signature.
const Transition = forwardRef(function Transition(
    props: TransitionProps & {children: ReactElement},
    ref: Ref<unknown>,
) {
    return <Slide direction="up" ref={ref} {...props} />
})

export default function AddAuthorizedUser() {
    const [modalOpen, setModalOpen] = useState(false)
    const [activeStep, setActiveStep] = useState(0)
    const [phoneNumber, setPhoneNumber] = useState('')

    // Mobile should be a full screen experience
    const fullScreen = useMediaQuery((theme: Theme) => theme.breakpoints.down('lg'))

    const handlePhoneNumberChange = (event: ChangeEvent<HTMLInputElement>) => {
        let input = event.target.value
        input = input.replace(/\D/g, '') // Remove all non-numeric characters
        input = input.substring(0, 10) // Keep the first 10 digits

        // Format as (*************
        let size = input.length
        if (size === 0) {
            input = input
        } else if (size < 4) {
            input = '(' + input
        } else if (size < 7) {
            input = '(' + input.substring(0, 3) + ') ' + input.substring(3)
        } else {
            input = '(' + input.substring(0, 3) + ') ' + input.substring(3, 6) + '-' + input.substring(6)
        }

        setPhoneNumber(input)
    }

    return (
        <>
            <Button variant="outlined" fullWidth startIcon={<Add />} onClick={() => setModalOpen(true)} sx={{mb: 2}}>
                Add new Authorized User
            </Button>
            <Dialog
                open={modalOpen}
                onClose={() => setModalOpen(false)}
                TransitionComponent={Transition}
                aria-labelledby="add-auth-user-title"
                maxWidth="md"
                fullWidth
                fullScreen={fullScreen}
                sx={{top: fullScreen ? 65 : 0}}
            >
                <DialogTitle id="add-auth-user-title">Add New Authorized User</DialogTitle>
                <IconButton sx={{position: 'absolute', right: 8, top: 8}} onClick={() => setModalOpen(false)}>
                    <Close />
                </IconButton>
                <DialogContent>
                    <Box display="flex" flexDirection="column" gap={2}>
                        <Stepper nonLinear activeStep={activeStep}>
                            <Step>
                                <StepButton onClick={() => setActiveStep(0)} />
                            </Step>
                            <Step>
                                <StepButton onClick={() => setActiveStep(1)} />
                            </Step>
                            <Step>
                                <StepButton onClick={() => setActiveStep(2)} />
                            </Step>
                        </Stepper>
                        <Divider />
                        <Box>
                            {activeStep === 0 ? (
                                <>
                                    <Typography>Personal Information</Typography>
                                    <TextField
                                        size="small"
                                        hiddenLabel
                                        variant="outlined"
                                        type="text"
                                        placeholder="First Name"
                                    />
                                    <TextField
                                        size="small"
                                        hiddenLabel
                                        variant="outlined"
                                        type="text"
                                        placeholder="Last Name"
                                    />
                                    <TextField
                                        size="small"
                                        hiddenLabel
                                        variant="outlined"
                                        type="email"
                                        placeholder="Email"
                                    />
                                    <TextField
                                        size="small"
                                        hiddenLabel
                                        variant="outlined"
                                        type="tel"
                                        placeholder="Phone Number"
                                        value={phoneNumber}
                                        onChange={handlePhoneNumberChange}
                                    />
                                </>
                            ) : activeStep === 1 ? (
                                <></>
                            ) : activeStep === 2 ? (
                                <></>
                            ) : null}
                        </Box>
                    </Box>
                </DialogContent>
            </Dialog>
        </>
    )
}
