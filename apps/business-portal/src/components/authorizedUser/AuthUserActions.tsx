import {ChangeEvent, useReducer, useState} from 'react'
import type {Dispatch, MouseEvent} from 'react'
import {AuthUserFilterState, AuthUserFilterAction} from './AuthUserTypes'
import {Sort, EventAvailable, FilterAlt, Search} from '@mui/icons-material'
import {Box, IconButton, Menu, MenuItem, ListItemIcon, ListItemText, TextField} from '@mui/material'

function authUserFilterReducer(state: AuthUserFilterState, action: AuthUserFilterAction): AuthUserFilterState {
    if (action.type === 'toggle_user_enabled') {
        return {...state, userEnabled: !state.userEnabled}
    } else if (action.type === 'toggle_user_filter') {
        return {...state, userFilterOn: action.value}
    } else if (action.type === 'user_search') {
        return {...state, userSearch: action.value}
    }
    return state
}

// While the reducer is invoked in the StateController I put the source here, to be closer to where it's needed most
export function useAuthUserReducer() {
    return useReducer(authUserFilterReducer, {
        userEnabled: true,
        userFilterOn: 'lastName',
        userSearch: '',
    })
}

interface AuthUserActionsProps {
    authUserState: AuthUserFilterState
    dispatch: Dispatch<AuthUserFilterAction>
}

export default function AuthUserActions({authUserState, dispatch}: AuthUserActionsProps) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)
    const filterMenuOpen = Boolean(anchorEl)

    function handleOpenFilterMenu(event: MouseEvent<HTMLButtonElement>) {
        setAnchorEl(event.currentTarget)
    }

    function handleCloseFilterMenu() {
        setAnchorEl(null)
    }

    function handleMenuClick(
        event: MouseEvent<HTMLElement>,
        type: AuthUserFilterAction['type'],
        value?: 'lastName' | 'createdOn',
    ) {
        if (type === 'toggle_user_enabled') {
            dispatch({type})
        } else if (value) {
            dispatch({type, value})
        }
        handleCloseFilterMenu()
    }

    function handleAuthUserSearchChange(event: ChangeEvent<HTMLInputElement>) {
        dispatch({
            type: 'user_search',
            value: event.target.value,
        })
    }

    return (
        <Box sx={{display: 'flex', alignItems: 'center', pt: 1}}>
            <IconButton
                onClick={handleOpenFilterMenu}
                id="authorized-user-filter-button"
                aria-label="authorized-user-filter-button"
                sx={{
                    borderColor: 'rgba(0,0,0,0.23)',
                    borderWidth: 1,
                    borderStyle: 'solid',
                    borderRadius: theme => `${theme.shape.borderRadius}px`,
                    height: 40,
                    top: 2,
                    marginRight: 1,
                }}
            >
                <Sort />
            </IconButton>
            <Menu
                id="authorized-user-filter-menu"
                anchorEl={anchorEl}
                open={filterMenuOpen}
                onClose={handleCloseFilterMenu}
                MenuListProps={{
                    'aria-labelledby': 'authorized-user-filter-button',
                }}
                anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
            >
                <MenuItem
                    selected={authUserState.userFilterOn === 'lastName'}
                    onClick={evt => handleMenuClick(evt, 'toggle_user_filter', 'lastName')}
                >
                    <ListItemIcon>
                        <Sort />
                    </ListItemIcon>
                    <ListItemText>Last Name</ListItemText>
                </MenuItem>
                <MenuItem
                    selected={authUserState.userFilterOn === 'createdOn'}
                    onClick={evt => handleMenuClick(evt, 'toggle_user_filter', 'createdOn')}
                >
                    <ListItemIcon>
                        <EventAvailable />
                    </ListItemIcon>
                    <ListItemText>Recently Added</ListItemText>
                </MenuItem>
                <MenuItem
                    selected={!authUserState.userEnabled}
                    onClick={evt => handleMenuClick(evt, 'toggle_user_enabled')}
                >
                    <ListItemIcon>
                        <FilterAlt />
                    </ListItemIcon>
                    <ListItemText>Exclude Deactivated</ListItemText>
                </MenuItem>
            </Menu>
            <TextField
                variant="outlined"
                label="Search Authorized Users"
                margin="dense"
                size="small"
                fullWidth
                InputProps={{
                    endAdornment: <Search />,
                }}
                value={authUserState.userSearch}
                onChange={handleAuthUserSearchChange}
            />
        </Box>
    )
}
