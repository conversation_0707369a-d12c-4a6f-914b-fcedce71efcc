import {Grid} from '@mui/material'
import AuthUserStateController from './authorizedUser/AuthUserStateController'
import {RightTabs} from './RightTabs'
import StatementsStateController from './statements/StatementsStateController'

async function getAuthorizedUsers(): Promise<boolean> {
    return new Promise(resolve => setTimeout(() => resolve(true), 1300))
}

async function getStatements(): Promise<boolean> {
    return new Promise(resolve => setTimeout(() => resolve(true), 2000))
}

export default async function RightContent() {
    // This changes to a real big-boi api someday
    const authUsers = await getAuthorizedUsers()
    const statements = await getStatements()

    return (
        <Grid container gap={6} alignContent="flex-start">
            <Grid flex="1 1 100%" sx={{width: '100%'}}>
                <RightTabs
                    tabs={[
                        {
                            node: <AuthUserStateController authUsers={{authorizedUsers: []}} />,
                            id: 'auth_users',
                            label: 'Authorized Users',
                        },
                        {
                            node: <StatementsStateController statements={{statements: []}} />,
                            id: 'statements',
                            label: 'Statements',
                        },
                    ]}
                />
            </Grid>
        </Grid>
    )
}
