'use client'

import {Box, Tabs, Tab} from '@mui/material'
import {SyntheticEvent, useState} from 'react'

interface TabPanelProps {
    children?: React.ReactNode
    panelName: string
    value: string
}
function TabPanel(props: TabPanelProps) {
    return (
        <div
            role="tabpanel"
            hidden={props.value !== props.panelName}
            id={`tabpanel-${props.panelName}`}
            aria-labelledby={`tab-${props.panelName}`}
        >
            {props.value === props.panelName && <Box>{props.children}</Box>}
        </div>
    )
}

function a11yProps(panel: string) {
    return {
        id: `tab-${panel}`,
        'aria-controls': `tabpanel-${panel}`,
    }
}

interface RightTabsProps {
    tabs: Array<{node: React.ReactNode; id: string; label: string}>
}

export const RightTabs = ({tabs}: RightTabsProps) => {
    const [selectedTab, setSelectedTab] = useState(tabs[0].id)

    function handleChangeTab(event: SyntheticEvent, newValue: string) {
        setSelectedTab(newValue)
    }

    return (
        <Box>
            <Tabs onChange={handleChangeTab} value={selectedTab} aria-label="Action Tabs">
                {tabs.map(tab => {
                    return <Tab key={tab.id} label={tab.label} value={tab.id} />
                })}
            </Tabs>
            {tabs.map(tab => {
                return (
                    <TabPanel key={tab.id} value={selectedTab} panelName={tab.id} {...a11yProps(tab.id)}>
                        {tab.node}
                    </TabPanel>
                )
            })}
        </Box>
    )
}
