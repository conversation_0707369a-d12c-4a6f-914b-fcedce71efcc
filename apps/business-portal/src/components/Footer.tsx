'use client'

import {Facebook, Instagram, Twitter, YouTube} from '@mui/icons-material'
import {Box, Grid} from '@mui/material'
import {TalliedLogo} from '@tallied-technologies/assets'
import Image from 'next/image'

export default function Footer() {
    return (
        <Box
            display="flex"
            justifyContent="center"
            sx={{color: 'white', background: theme => theme.palette.primary.main, py: 3, px: 2}}
        >
            <Box
                display="flex"
                justifyContent="space-between"
                alignItems="flex-start"
                sx={{maxWidth: 1440, width: '100%'}}
            >
                <Grid container wrap="nowrap">
                    <Grid
                        container
                        display="flex"
                        alignContent="flex-start"
                        flex="1 1 auto"
                        flexDirection="column"
                    >
                        <Grid>Footer</Grid>
                        <Grid>Link</Grid>
                        <Grid>Link</Grid>
                    </Grid>
                    <Grid
                        container
                        display="flex"
                        alignContent="flex-end"
                        flex="1 1 auto"
                        flexDirection="column"
                        gap={2}
                    >
                        <Grid>
                            <Image src={TalliedLogo} alt="Tallied Logo" />
                        </Grid>
                        <Grid gap={2} display="flex">
                            <YouTube fontSize="small" />
                            <Instagram fontSize="small" />
                            <Facebook fontSize="small" />
                            <Twitter fontSize="small" />
                        </Grid>
                    </Grid>
                </Grid>
            </Box>
        </Box>
    )
}
