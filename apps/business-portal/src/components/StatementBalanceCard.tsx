'use client'

import {PaymentCard} from '@tallied-technologies/component-library'

interface StatementBalanceProps {
    balance: number
    minimumPayment: number
}

export default function StatementBalance({balance, minimumPayment}: StatementBalanceProps) {
    function handleMakePayment() {
        return null
    }
    return <PaymentCard balance={balance} minimumPayment={minimumPayment} onClickMakePayment={handleMakePayment} />
}
