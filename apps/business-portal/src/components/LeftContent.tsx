import {Grid} from '@mui/material'
import {CardBalance} from '@tallied-technologies/component-library'
import {card} from '@tallied-technologies/assets'
import Image from 'next/image'
import StatementBalance from './StatementBalanceCard'

async function getBalance(): Promise<{
    balance: number
    availableCredit: number
    statementBalance: number
    minimumPayment: number
}> {
    return new Promise(resolve =>
        setTimeout(
            () =>
                resolve({
                    balance: 5000,
                    availableCredit: 200000,
                    statementBalance: 10000,
                    minimumPayment: 3500,
                }),
            1000,
        ),
    )
}

export default async function LeftContent() {
    const CardArt = <Image src={card} height={80} alt="Card Art" />

    // This changes to a real big-boi api someday
    const balances = await getBalance()

    return (
        <Grid container gap={6} alignContent="flex-start">
            <Grid flex="1 1 100%">
                <CardBalance balance={balances.balance} availableCredit={balances.availableCredit} cardArt={CardArt} />
            </Grid>
            <Grid flex="1 1 100%">
                <StatementBalance
                    balance={balances.statementBalance || 0}
                    minimumPayment={balances.minimumPayment || 0}
                />
            </Grid>
        </Grid>
    )
}
