'use client'
import {Description, KeyboardArrowRight} from '@mui/icons-material'
import {Box, Divider, Typography, Grid, Avatar, IconButton, Link} from '@mui/material'
import {formatCurrency} from '@tallied-technologies/common'

interface StatementRowProps {
    displayTitle: string
    link: {href: string; title: string}
    statementPeriod: string
    statementBalance: {amount: number; currency: string}
}

export default function StatementRow({displayTitle, link, statementPeriod, statementBalance}: StatementRowProps) {
    return (
        <Link
            href={link.href}
            target="_blank"
            underline="none"
            sx={{
                display: 'block',
                color: 'initial',
                '&:hover': {backgroundColor: theme => theme.palette.grey[100]},
            }}
        >
            <Box sx={{py: 1, px: 0}}>
                <Grid
                    container
                    flexDirection="row"
                    alignItems="center"
                    sx={{
                        gap: 2,
                        flexWrap: 'nowrap',
                    }}
                >
                    <Grid>
                        <Avatar sx={{backgroundColor: theme => theme.palette.primary.light, mr: 1}}>
                            <Description sx={{color: theme => theme.palette.text.primary}} />
                        </Avatar>
                    </Grid>
                    <Grid container flexDirection="column" sx={{maxWidth: '50%'}}>
                        <Grid sx={{width: '100%'}}>
                            <Typography
                                fontWeight={600}
                                fontSize={16}
                                lineHeight="175%"
                                noWrap
                                sx={{
                                    color: theme => theme.palette.text.primary,
                                }}
                            >
                                {statementPeriod}
                            </Typography>
                        </Grid>
                        <Grid sx={{width: '100%'}}>
                            <Typography
                                fontSize={16}
                                lineHeight="175%"
                                noWrap
                                sx={{
                                    color: theme => theme.palette.text.secondary,
                                }}
                            >
                                {displayTitle}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container flexDirection="column" textAlign="end" sx={{maxWidth: '40%'}}>
                        <Grid sx={{width: '100%'}}>
                            <Typography
                                fontSize={16}
                                fontWeight={600}
                                sx={{
                                    color: theme => theme.palette.text.primary,
                                }}
                                lineHeight="175%"
                                noWrap
                            >
                                {formatCurrency(statementBalance.amount / 100, statementBalance.currency)}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid>
                        <IconButton edge="end" size="small">
                            <KeyboardArrowRight />
                        </IconButton>
                    </Grid>
                </Grid>
            </Box>
            <Divider />
        </Link>
    )
}
