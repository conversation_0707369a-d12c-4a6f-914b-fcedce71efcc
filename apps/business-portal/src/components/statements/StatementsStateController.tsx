'use client'

import {Box, MenuItem, Select} from '@mui/material'
import type {SelectChangeEvent} from '@mui/material'
import StatementRow from './StatementRow'
import {useState} from 'react'

interface StatementsStateControllerProps {
    statements: Record<string,Array<Record<string, any>>>
}

function filterStatementsByYear(
    statements: Array<Record<string, any>>,
    selectedYear: 'all_statements' | string,
) {
    if (selectedYear === 'all_statements') {
        return statements
    } else {
        const filteredStatements = statements.filter(statement => {
            const statementYear = new Date(statement?.statementEndDate ?? '').getFullYear()

            return statementYear === Number(selectedYear)
        })
        return filteredStatements
    }
}

export default function StatementsStateController({statements}: StatementsStateControllerProps) {
    const [selectedStatementsRange, setSelectedStatementsRange] = useState('all_statements')

    function handleStatementRangeChange(event: SelectChangeEvent) {
        setSelectedStatementsRange(event.target.value)
    }

    const statementYearMap = statements.statements?.reduce((existingYears: Array<number>, currentStatement) => {
        const year = new Date(currentStatement.statementEndDate).getFullYear()
        if (!existingYears.includes(year)) {
            existingYears.push(year)
            return existingYears
        }
        return existingYears
    }, [])

    return (
        <Box sx={{p: 2, boxShadow: theme => theme.shadows[1], backgroundColor: theme => theme.palette.common.white}}>
            <Box sx={{display: 'flex', alignItems: 'center', pt: 1}}>
                <Select
                    fullWidth
                    size="small"
                    variant="filled"
                    value={selectedStatementsRange}
                    onChange={handleStatementRangeChange}
                    sx={{
                        backgroundColor: theme => theme.palette.primary.light,
                        '&:hover': {backgroundColor: theme => theme.palette.grey[100]},
                        '&.Mui-focused': {backgroundColor: theme => theme.palette.grey[100]},
                    }}
                    SelectDisplayProps={{
                        style: {
                            fontWeight: 600,
                            fontSize: 16,
                            paddingTop: 8,
                            paddingBottom: 8,
                        },
                    }}
                >
                    <MenuItem value="all_statements">All Statements</MenuItem>
                    {statementYearMap.map(statementYear => (
                        <MenuItem key={statementYear} value={statementYear}>
                            {statementYear}
                        </MenuItem>
                    ))}
                </Select>
            </Box>
            <Box sx={{maxHeight: 800, overflowY: 'auto', overflowX: 'hidden'}} role="statments-list">
                {filterStatementsByYear(statements.statements, selectedStatementsRange).map(statement => {
                    return (
                        <StatementRow
                            key={statement.title}
                            displayTitle={statement.displayTitle}
                            link={statement.link}
                            statementBalance={statement.statementBalance}
                            statementPeriod={statement.statementPeriod}
                        />
                    )
                })}
            </Box>
        </Box>
    )
}
