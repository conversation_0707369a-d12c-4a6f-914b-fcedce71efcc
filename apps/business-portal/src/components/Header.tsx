'use client'
import {useRef, useState} from 'react'
import type {MouseEvent} from 'react'
import {Close, Logout, MenuRounded, Notifications, Settings} from '@mui/icons-material'
import {
    AppBar,
    Button,
    IconButton,
    Toolbar,
    Box,
    ButtonProps,
    List,
    ListItem,
    ListItemButton,
    ListItemText,
    Badge,
    Menu,
    MenuItem,
    ListItemIcon,
    Popover,
    Divider,
} from '@mui/material'
import {styled} from '@mui/material/styles'
import {TalliedLogo} from '@tallied-technologies/assets'
import Image from 'next/image'
import Link from 'next/link'
import {usePathname} from 'next/navigation'
import {navigationList} from '@/dataComponents/navigation'

interface NavigationButtonProps extends ButtonProps {
    navActive: boolean
}

// Using a stylecomponent here because I want a bit more control over the styles, and I want to extend
// a custom prop that will have an effect on said styles. The alternative way to do this component would
// be a HOC with sx props. I think this just reads better.
const NavigationButton = styled(But<PERSON>, {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    slot: 'Root',
    shouldForwardProp: prop => prop !== 'navActive',
})<NavigationButtonProps>(({theme, ...props}) => ({
    color: props.navActive ? theme.palette.primary.main : 'inherit',
    backgroundColor: props.navActive ? theme.palette.common.white : 'inherit',
    padding: theme.spacing(1, 2),
    textTransform: 'initial',
    '&:active': {
        backgroundColor: theme.palette.common.white,
        color: theme.palette.primary.main,
    },
    '&:focus': {
        backgroundColor: theme.palette.common.white,
        color: theme.palette.primary.main,
    },
    '&:hover': {
        backgroundColor: theme.palette.common.white,
        color: theme.palette.primary.main,
    },
}))

export default function Header() {
    const path = usePathname()
    const [navigationAnchorEl, setNavigationAnchorEl] = useState<HTMLElement | null>(null)
    const navigationPopperElement = useRef<HTMLElement | null>(null)
    const [settingsAnchorElement, setSettingsAnchorElement] = useState<HTMLElement | null>(null)
    const settingsMenuOpen = Boolean(settingsAnchorElement)
    const navigationMenuOpen = Boolean(navigationAnchorEl)

    const popoverId = navigationMenuOpen ? 'navigation-popper' : undefined

    function handleNavigationClick() {
        setNavigationAnchorEl(navigationPopperElement.current as HTMLElement)
    }

    function handleNavigationClose() {
        setNavigationAnchorEl(null)
    }

    function setSettingsMenuAnchorElement(event: MouseEvent<HTMLElement>) {
        setSettingsAnchorElement(event.currentTarget)
    }

    function handleCloseSettingsMenu() {
        setSettingsAnchorElement(null)
    }

    return (
        <>
            <AppBar position="static" ref={navigationPopperElement}>
                <Toolbar>
                    <IconButton
                        color="inherit"
                        edge="start"
                        sx={{mr: 2, display: {md: 'none'}}}
                        onClick={handleNavigationClick}
                        aria-describedby={popoverId}
                        aria-label="Navigation Menu"
                    >
                        {navigationMenuOpen ? <Close /> : <MenuRounded />}
                    </IconButton>
                    <Image src={TalliedLogo} alt="Tallied Logo - Portal" />
                    <Box
                        sx={{
                            display: {sm: 'none', md: 'flex'},
                            ml: 6,
                            gap: 6,
                            flex: 1,
                            '& a': {color: 'white', textDecoration: 'none'},
                        }}
                    >
                        {navigationList.map(navigationItem => (
                            <Link key={navigationItem.path} href={navigationItem.path}>
                                <NavigationButton
                                    startIcon={navigationItem.icon ? <navigationItem.icon /> : null}
                                    navActive={path === navigationItem.path}
                                >
                                    {navigationItem.name}
                                </NavigationButton>
                            </Link>
                        ))}
                    </Box>
                    <Box sx={{display: 'flex', justifyContent: 'flex-end', gap: 1, flex: 1}}>
                        <IconButton sx={{color: 'white'}} aria-label="Notifications">
                            <Badge
                                badgeContent={2}
                                max={9}
                                anchorOrigin={{vertical: 'bottom', horizontal: 'right'}}
                                color="secondary"
                                sx={{'& .MuiBadge-badge': {border: theme => `1px solid ${theme.palette.primary.main}`}}}
                            >
                                <Notifications />
                            </Badge>
                        </IconButton>
                        <IconButton
                            sx={{color: 'white'}}
                            onClick={setSettingsMenuAnchorElement}
                            aria-controls={settingsMenuOpen ? 'settings-menu' : undefined}
                            aria-haspopup="true"
                            aria-expanded={settingsMenuOpen ? 'true' : undefined}
                            aria-label="Settings"
                        >
                            <Settings name="Settings" />
                        </IconButton>
                    </Box>
                </Toolbar>
            </AppBar>
            <Popover
                id={popoverId}
                open={navigationMenuOpen}
                anchorEl={navigationAnchorEl}
                onClose={handleNavigationClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                sx={{display: {md: 'none'}}}
                slotProps={{
                    paper: {
                        sx: {
                            width: '100%',
                            maxWidth: '100%',
                            backgroundColor: theme => theme.palette.primary.main,
                            borderTopLeftRadius: 0,
                            borderTopRightRadius: 0,
                            color: theme => theme.palette.common.white,
                        },
                    },
                }}
                marginThreshold={null}
            >
                <Box onClick={handleNavigationClose}>
                    <List sx={{px: 4}}>
                        {navigationList.map(navigationItem => (
                            <ListItem key={navigationItem.path} disablePadding disableGutters>
                                <ListItemButton component={Link} href={navigationItem.path}>
                                    <ListItemIcon>
                                        {navigationItem.icon ? <navigationItem.icon htmlColor="white" /> : null}
                                    </ListItemIcon>
                                    <ListItemText
                                        sx={{flex: '0 0 auto'}}
                                        primaryTypographyProps={{fontSize: 24, fontWeight: 600}}
                                    >
                                        {navigationItem.name}
                                    </ListItemText>
                                </ListItemButton>
                            </ListItem>
                        ))}
                        <ListItem>
                            <Divider sx={{borderColor: 'white', width: '100%'}} />
                        </ListItem>
                        <ListItem
                            sx={{mb: 1}}
                            secondaryAction={
                                <Button
                                    variant="text"
                                    endIcon={<Logout />}
                                    sx={{
                                        color: theme => theme.palette.common.white,
                                        fontSize: 24,
                                        fontWeight: 600,
                                        textTransform: 'initial',
                                    }}
                                >
                                    Logout
                                </Button>
                            }
                        >
                            <ListItemButton></ListItemButton>
                        </ListItem>
                    </List>
                </Box>
            </Popover>
            <Menu
                anchorEl={settingsAnchorElement}
                id="settings-menu"
                open={settingsMenuOpen}
                onClose={handleCloseSettingsMenu}
                transformOrigin={{horizontal: 'right', vertical: 'top'}}
                anchorOrigin={{horizontal: 'right', vertical: 'bottom'}}
            >
                <MenuItem onClick={handleCloseSettingsMenu}>
                    <ListItemIcon>
                        <Logout fontSize="small" />
                    </ListItemIcon>
                    Logout
                </MenuItem>
            </Menu>
        </>
    )
}
