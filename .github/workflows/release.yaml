name: Release

concurrency:
  group: user-frontend
  cancel-in-progress: false

on:
  workflow_dispatch:
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read
  pull-requests: read
  checks: write

env:
  _EXPERIMENTAL_DAGGER_RUNNER_HOST: "docker-container://dagger-engine"

jobs:
  run_release_workflow:
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    name: Run Release Workflow
    runs-on: runs-on,run-id=${{ github.run_id }},job-shorthand=cr,runner=arm64-16cpu-dagger,spot=false
    steps:
      - name: Generate a token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.TALLIED_RELEASE_BOT_APP_ID }}
          private-key: ${{ secrets.TALLIED_RELEASE_BOT_PRIVATE_KEY }}

      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          fetch-depth: "0"

      - name: Calculate Version
        id: semver
        uses: paulhatch/semantic-version@v5.4.0
        with:
          major_pattern: "breaking:"
          minor_pattern: "feat:"

      - name: Configure AWS Profiles
        uses: mcblair/configure-aws-profiles@v0.0.6
        with:
          default-region: us-west-2
          profiles: |
            # ALL ENV
            ## For any env using the global IPAM for VPC CIDR allocations
            global-ipam:
              role-arn: arn:aws:iam::590183864865:role/user-frontend-global-ipam
              
            # DEV
            dev-secrets:
              role-arn: arn:aws:iam::043084217297:role/tallied-workflow-roles-user-frontend
            dev-configuration:
              role-arn: arn:aws:iam::739127650947:role/tallied-workflow-roles-user-frontend          
            ## For Pulumi State bucket in dev
            dev-infra:
              role-arn: arn:aws:iam::654654191239:role/tallied-workflow-roles-platform-infra-consumer
            ## For Dev Stacks
            team:
              role-arn: arn:aws:iam::822186872427:role/tallied-workflow-roles-user-frontend
            
            # PREPROD
            preprod-secrets:
              role-arn: arn:aws:iam::972695919095:role/tallied-workflow-roles-user-frontend
            preprod-configuration:
              role-arn: arn:aws:iam::861322067195:role/tallied-workflow-roles-user-frontend
            ## For Pulumi State bucket in preprod
            preprod-infra:
              role-arn: arn:aws:iam::851725656448:role/tallied-workflow-roles-platform-infra-consumer
            ## For Preprod Stacks
            preprod-user-frontend:
              role-arn: arn:aws:iam::593793023154:role/tallied-workflow-roles-user-frontend
              
            # PROD
            # For Pulumi State bucket in prod
            prod-infra:
              role-arn: arn:aws:iam::992382563172:role/tallied-workflow-roles-platform-infra-consumer
            ## For Publishing artifacts
            artifacts:
              role-arn: arn:aws:iam::323007991338:role/tallied-workflow-roles-artifacts-publisher
            
      - name: Login to Amazon ECR
        run: aws ecr get-login-password --region us-west-2 --profile artifacts | docker login --username AWS --password-stdin 323007991338.dkr.ecr.us-west-2.amazonaws.com

      - name: Go Cache
        id: gocache
        uses: runs-on/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
          
      - name: Update Timestamps
        run: go run ./tools/git-mtimestamp
        working-directory: iac        

      - name: Dagger Call `release-workflow`
        uses: dagger/dagger-for-github@v7
        with:
          # We'll need to keep this in sync with the version of Dagger in `dagger.json`.
          version: "0.18.7"
          verb: call
          args: >-
            --semantic-version ${{ steps.semver.outputs.version_tag }}
            --github-token GITHUB_TOKEN
            release-workflow 
            --aws-dir=~/.aws 
            --github-token RELEASE_GITHUB_TOKEN 
            --next-semver ${{ steps.semver.outputs.version_tag }}
            --target-sha ${{ github.sha }}
            --current-branch ${{ github.ref_name }}
          cloud-token: ${{ secrets.FRONTEND_DAGGER_CLOUD_TOKEN }}
          engine-stop: false # Caches are persisted to dagger cloud on engine shutdown, but we systemd now handles engine shutdown in the AMI.
        env:
          RELEASE_GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
          GITHUB_TOKEN: ${{ secrets.GOPRIVATE_ORG_TOKEN }}
