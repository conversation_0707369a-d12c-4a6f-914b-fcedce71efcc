name: Generate Backend OpenAPI Spec

run-name: Generate Backend OpenAPI Spec for `${{ github.event.inputs.version }}`

concurrency:
  group: gen_api_spec
  cancel-in-progress: false

on:
  workflow_dispatch:
    inputs:
      version:
        description: "The version of the backend to generate the OpenAPI spec for. e.g. v0.17.79"
        required: true

permissions:
  checks: write
  id-token: write
  contents: write

jobs:
  generate_openapi_spec:
    runs-on: runs-on,run-id=${{ github.run_id }},job-shorthand=goas,runner=arm64-4cpu-linux
    name: Generate Backend OpenAPI Spec
    env:
      # Can't upgrade to 7.9.0 to take the fix for https://github.com/OpenAPITools/openapi-generator/issues/18746,
      # due to https://github.com/OpenAPITools/openapi-generator/issues/19858.
      # Hopefully it'll get fixed in 7.10.0/whatever the next version is.
      OPENAPI_GENERATOR_VERSION: 7.5.0
      REDOCLY_CLI_VERSION: 1.25.0
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: ./package-lock.json
        
      - name: Install Redocly CLI
        run: npm install -g @redocly/cli@${{ env.REDOCLY_CLI_VERSION }}
          
      - name: Install openapi-generator
        run: | 
          mkdir -p ~/bin/openapitools && \
          curl https://raw.githubusercontent.com/OpenAPITools/openapi-generator/master/bin/utils/openapi-generator-cli.sh > ~/bin/openapitools/openapi-generator-cli && \
          chmod u+x ~/bin/openapitools/openapi-generator-cli && \
          sudo mv ~/bin/openapitools/openapi-generator-cli /usr/local/bin/openapi-generator
      - name: Ensure openapi-generator on PATH
        run: openapi-generator help
        
      - name: Generate a token
        if: github.event_name == 'workflow_dispatch'
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.TALLIED_RELEASE_BOT_APP_ID }}
          private-key: ${{ secrets.TALLIED_RELEASE_BOT_PRIVATE_KEY }}
          repositories: "user-frontend,program-backend"

      - uses: dsaltares/fetch-gh-release-asset@master
        with:
          repo: Tallied-Technologies/program-backend
          version: tags/${{ github.event.inputs.version }}
          file: oas.tar.gz
          token: ${{ steps.generate_token.outputs.token }}
          
      - uses: dsaltares/fetch-gh-release-asset@master
        with:
          repo: Tallied-Technologies/program-backend
          version: tags/${{ github.event.inputs.version }}
          file: tallctl
          token: ${{ steps.generate_token.outputs.token }}

      - name: Install tallctl
        run: chmod +x ./tallctl && sudo mv ./tallctl /usr/local/bin/tallctl
        shell: bash

      - name: Ensure tallctl on PATH
        run: tallctl --help
        shell: bash
          
      - name: Extract Spec
        run: tar -vxzf oas.tar.gz
        
      - name: Cleanup Existing Services
        run: rm -rf ./packages/services/src/api/*
        
      - name: Generate typescript-fetch SDK from OAS
        run: tallctl program oas sdk generate --generator typescript-fetch

      - name: Move generated SDK to services
        run: mv ./gen/typescript-fetch/* ./packages/services/src/api/
        
      - name: Cleanup Generated SDK
        run: rm -rf ./gen

      - name: Post release version
        run: npm version ${{ github.event.inputs.version }} --allow-same-version
        working-directory: ./packages/services

      - name: Cleanup Spec
        run: rm -rf oas.tar.gz ./spec

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ steps.generate_token.outputs.token }}
          branch: gen-api-spec/${{ github.event.inputs.version }}
          delete-branch: true
          title: Generate OpenAPI Spec for Backend ${{ github.event.inputs.version }}
          reviewers: JeffBaumgardt
          draft: false